# 依赖包目录
node_modules/
miniprogram_npm/

# npm相关文件
package-lock.json
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 微信小程序私有配置文件
project.private.config.json

# 编辑器和IDE配置文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp

# 构建输出目录
dist/
build/

# 环境配置文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.rar
*.7z
*.tar.gz

# 微信开发者工具生成的文件
.wechat_devtools/

# 其他不需要版本控制的文件
*.cache
.cache/
.nyc_output/
coverage/