# 微信授权登录实现说明

## 功能概述

已为小程序添加微信授权登录功能，用户可以通过微信授权快速登录，无需输入手机号和验证码。

## 前端实现

### 1. 登录页面修改

- **文件位置**: `pages/number_login/number_login.wxml`
- **新增内容**: 
  - 微信登录成功后自动申请获取用户手机号
  - 添加手机号授权弹窗界面
  - 手机号用于匹配优惠券功能
  - 微信头像和昵称用于个人中心显示
  - 用户可选择跳过手机号授权
  - 完善的错误处理和用户提示
  - 添加了微信授权登录按钮
  - 添加了"或"分割线，区分手机号登录和微信登录
  - 微信登录按钮采用微信绿色主题色

### 2. 样式优化

- **文件位置**: `pages/number_login/number_login.wxss`
- **新增样式**:
  - 分割线样式
  - 微信登录按钮内容布局
  - 微信图标样式

### 3. 登录逻辑实现

- **文件位置**: `pages/number_login/number_login.js`
- **新增方法**:
  - `wxLogin()`: 微信授权登录主方法
  - `sendWxLoginToServer()`: 发送微信登录信息到服务器

### 4. API配置

- **文件位置**: `config/setting.js`
- **新增接口**: `wx_login: rootUrl + '/wx_login'`

### 5. 权限配置

- **文件位置**: `app.json`
- **新增权限**: 添加了 `scope.userInfo` 权限配置

## 微信授权登录流程

1. 用户点击"微信授权登录"按钮
2. 调用 `wx.login()` 获取临时登录凭证 code
3. 调用 `wx.getUserProfile()` 获取用户基本信息（需用户授权）
4. 将 code 和用户信息发送到后端接口 `/api/wx_login`
5. 后端验证 code 并返回用户信息和 token
6. 前端保存用户信息到本地存储和全局状态
7. 跳转到个人中心页面

## 后端接口要求

### 接口地址
```
POST /api/wx_login
```

### 请求参数
```json
{
  "code": "微信登录凭证",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "用户头像URL",
    "gender": 1,
    "country": "国家",
    "province": "省份",
    "city": "城市",
    "language": "语言"
  }
}
```

### 响应格式
```json
{
  "code": 200,
  "msg": "登录成功",
  "token": "用户token",
  "id": "用户ID",
  "name": "用户昵称",
  "score": 0,
  "avatar": "用户头像URL",
  "mobile": "绑定手机号（可选）"
}
```

## 后端实现建议

1. **验证微信 code**:
   - 使用微信提供的接口验证 code 的有效性
   - 获取用户的 openid 和 session_key

2. **用户信息处理**:
   - 根据 openid 查询用户是否已存在
   - 如果是新用户，创建用户记录
   - 如果是老用户，更新用户信息

3. **返回数据**:
   - 生成并返回 JWT token
   - 返回用户基本信息

## 注意事项

1. **用户授权**: 微信授权登录需要用户主动点击授权，不能强制获取用户信息
2. **隐私保护**: 严格按照微信小程序隐私规范处理用户数据
3. **错误处理**: 已添加完善的错误处理机制，包括网络错误、授权失败等情况
4. **兼容性**: 保留了原有的手机号登录方式，两种登录方式可以并存

## 测试建议

1. 测试微信授权流程是否正常
2. 测试用户拒绝授权的情况
3. 测试网络异常的处理
4. 测试登录成功后的页面跳转
5. 测试用户信息的正确保存和显示