// 环境配置
const envConfig = {
  // 开发环境
  develop: {
    baseUrl: 'http://localhost:8000',
    imageBaseUrl: 'https://files.fb-software.cn'
  },
  // 测试环境
  trial: {
    baseUrl: 'https://test-api.your-domain.com',
    imageBaseUrl: 'https://test-files.your-domain.com'
  },
  // 生产环境
  release: {
    baseUrl: 'https://api.your-domain.com',
    imageBaseUrl: 'https://files.your-domain.com'
  }
};

// 获取当前环境
function getCurrentEnv() {
  // 默认开发环境
  let env = 'develop';
  
  try {
    // 通过微信小程序全局变量_wxConfig获取环境信息
    if (typeof _wxConfig !== 'undefined' && _wxConfig.envVersion) {
      env = _wxConfig.envVersion;
    }
  } catch (e) {
    console.log('获取环境信息失败，使用默认开发环境:', e);
  }
  
  console.log('当前环境:', env);
  return env;
}

// 获取当前环境配置
const currentEnv = getCurrentEnv();
const config = envConfig[currentEnv] || envConfig.develop;

const baseUrl = config.baseUrl;
const rootUrl = baseUrl + '/api';
const imageBaseUrl = config.imageBaseUrl;

// 图片URL处理工具函数
function getImageUrl(imagePath) {
  if (!imagePath) return '';

  // 如果已经是完整URL，直接返回
  if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
    return imagePath;
  }

  // 如果是相对路径，拼接域名
  if (imagePath.startsWith('/')) {
    return imageBaseUrl + imagePath;
  }

  // 其他情况，添加前缀斜杠后拼接
  return imageBaseUrl + '/' + imagePath;
}

module.exports={
  // 基础配置
  baseUrl: baseUrl,
  rootUrl: rootUrl,
  imageBaseUrl: imageBaseUrl,
  
  // 环境信息
  currentEnv: currentEnv,
  envConfig: envConfig,
  getCurrentEnv: getCurrentEnv,
  
  // API接口
  product:rootUrl+'/products',
  banner:rootUrl+'/banners',
  index_product:rootUrl+'/index_products',
  product_zones:rootUrl+'/product-zones',
  shoppingcar:rootUrl+'/shoppingcar',
  send_sms:rootUrl+'/send_sms',
  verify_code:rootUrl+'/verify_code',
  wx_login:rootUrl+'/wx_login',
  update_phone:rootUrl+'/update_phone',
  
  // 优惠券相关接口
  coupons_available: rootUrl+'/coupons/available',
  coupons_my: rootUrl+'/coupons/my',
  coupons_claim: rootUrl+'/coupons/claim',
  
  // 工具函数
  getImageUrl: getImageUrl
}