/* pages/coupon/coupon.wxss */
.product-desc {
  color: #888;
  font-size: 24rpx;
  margin-bottom: 4rpx;
}
.custom-submit-bar {
  margin-bottom: -70rpx; /* 或 padding-bottom: 30rpx; */
}
.cart-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}
.cart-info {
  flex: 1;
  margin-left: 16rpx;
  position: relative;
}
.footer-bar {
  display: flex;
  align-items: center;
  padding: 16rpx;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
}
.total-price {
  flex: 1;
  text-align: right;
  color: #e64340;
  font-weight: bold;
  margin-right: 24rpx;
}
.top-bar {
  width: 100%;
  height: 100rpx; /* 可根据实际调整高度 */
  background: #ffb6c1; /* 粉色，可根据实际调整 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed; /* 悬浮在最上方 */
  top: 0;
  left: 0;
  z-index: 100;
}
.top-bar-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
}
.page-content {
  margin-top: 100rpx; /* 与 .top-bar 高度一致 */
}
.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  z-index: 10;
  background: #ffb6c1;
  color: #fff;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  font-size: 28rpx;
  border: none;
  padding: 0;
}