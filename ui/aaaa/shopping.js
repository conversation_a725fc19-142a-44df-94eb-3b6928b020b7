// pages/shopping/shopping.js
const config = require('../config/setting.js');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    cartList: [],
    checkedIds: [],
    allChecked: false,
    totalPrice: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getCartList();
  },

  // 获取购物车数据
  getCartList() {
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';
    if (!mobile) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }
    wx.request({
      url: config.baseUrl + '/shoppingcar/list',
      method: 'GET',
      data: { mobile },
      success: (res) => {
        if (res.data.success) {
          // 默认全部不选中
          const cartList = res.data.data.map(item => ({
            ...item,
            checked: false
          }));
          this.setData({ cartList, checkedIds: [], allChecked: false });
          this.calcTotal();
        } else {
          wx.showToast({ title: res.data.message || '获取失败', icon: 'none' });
        }
      },
      fail: () => {
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 减少数量
  reduceQuantity(e) {
    const index = e.currentTarget.dataset.index;
    const cartList = this.data.cartList;
    const item = cartList[index];
    if (!item) return;
    if (item.quantity <= 1) return;
    const newQuantity = item.quantity - 1;
    this.updateQuantity(item, newQuantity);
  },

  // 增加数量
  addQuantity(e) {
    const index = e.currentTarget.dataset.index;
    const cartList = this.data.cartList;
    const item = cartList[index];
    if (!item) return;
    const newQuantity = item.quantity + 1;
    this.updateQuantity(item, newQuantity);
  },

  // 更新数据库并刷新
  updateQuantity(item, newQuantity) {
    wx.request({
      url: config.baseUrl + '/shoppingcar/updateQuantity',
      method: 'POST',
      data: {
        mobile: item.mobile,
        id: item.id,
        quantity: newQuantity
      },
      success: (res) => {
        if (res.data.success) {
          this.getCartList(); // 数据库更新成功后，重新获取并刷新页面
        } else {
          wx.showToast({ title: res.data.message || '更新失败', icon: 'none' });
        }
      },
      fail: () => {
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },

  // 商品选中变化
  onCheckboxChange(e) {
    const checkedIds = e.detail; // 选中的id数组
    const cartList = this.data.cartList.map(item => ({
      ...item,
      checked: checkedIds.includes(String(item.id)) || checkedIds.includes(item.id)
    }));
    const allChecked = cartList.length > 0 && cartList.every(item => item.checked);
    this.setData({ checkedIds, cartList, allChecked });
    this.calcTotal();
  },

  // 全选/取消全选
  onCheckAll(e) {
    const allChecked = !this.data.allChecked;
    const checkedIds = allChecked ? this.data.cartList.map(item => String(item.id)) : [];
    const cartList = this.data.cartList.map(item => ({
      ...item,
      checked: allChecked
    }));
    this.setData({ allChecked, checkedIds, cartList });
    this.calcTotal();
  },

  // 计算总价
  calcTotal() {
    let total = 0;
    this.data.cartList.forEach(item => {
      if (item.checked) {
        total += item.price * item.quantity;
      }
    });
    this.setData({ totalPrice: total.toFixed(2) });
  },

  onDeleteTap(e) {
    const productId = e.currentTarget.dataset.id;
    const that = this;
    wx.showModal({
      title: '提示',
      content: '确定要删除该商品吗？',
      success(res) {
        if (res.confirm) {
          that.deleteProduct(productId);
        }
      }
    });
  },

  deleteProduct(productId) {
    const that = this;
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';

    wx.request({
      url: config.baseUrl + '/shoppingcar/delete',
      method: 'POST',
      data: {
        id: productId,
        mobile: mobile
      },
      success: (res) => {
        if (res.data.success) {
          wx.showToast({ title: '删除成功', icon: 'success' });
          that.getCartList();
        } else {
          wx.showToast({ title: res.data.message || '删除失败', icon: 'none' });
        }
      },
      fail: () => {
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  }
})