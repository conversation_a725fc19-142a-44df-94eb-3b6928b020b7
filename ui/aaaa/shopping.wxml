<!-- 顶部条形栏 -->
<view class="top-bar">
  <text class="top-bar-title">购物车</text>
</view>

<view class="page-content">
  <!-- 商品列表 -->
  <van-checkbox-group value="{{checkedIds}}" bind:change="onCheckboxChange">
    <block wx:for="{{cartList}}" wx:key="id">
      <view class="cart-item">
        <!-- 左侧单选框 -->
        <van-checkbox name="{{item.id}}" checked="{{item.checked}}">
        </van-checkbox>
        <!-- 右侧商品卡片内容 -->
        <view class="cart-info">
          <!-- 你的商品内容，如图片、标题、价格等 -->
          <van-card
            num="{{item.quantity}}"
            tag="标签"
            price="{{item.price}}起"
            title="{{item.title}}"
            thumb="{{item.img}}"
          >
            <view slot="desc">
              <view class="product-desc">{{item.size}}</view>
              <view>{{item.limit}}</view>
            </view>
            <view slot="footer">
              <van-button size="mini" data-index="{{index}}" bind:tap="reduceQuantity" style="margin-right: 16rpx;">━</van-button>
              <van-button size="mini" data-index="{{index}}" bind:tap="addQuantity">✚</van-button>
            </view>
          </van-card>
          <button class="delete-btn" data-id="{{item.id}}" bindtap="onDeleteTap">删除</button>
        </view>
      </view>
    </block>
  </van-checkbox-group>

  <!-- 占位空白，防止底部栏遮挡 -->
  <view style="height: 80rpx;"></view>
</view>

<!-- 底部全选栏 -->
<view class="footer-bar">
  <van-checkbox
    value="{{allChecked}}"
    checked="{{allChecked}}"
    bind:change="onCheckAll"
    shape="round"
  >全选</van-checkbox>
  <text class="total-price">合计: ￥{{totalPrice}}</text>
  <van-button type="danger" size="small">结算</van-button>
</view>
