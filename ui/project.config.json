{"setting": {"es6": true, "postcss": true, "minified": true, "uglifyFileName": false, "enhance": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": false, "minifyWXML": true, "compileWorklet": false, "uploadWithSourceMap": true, "packNpmManually": false, "minifyWXSS": true, "localPlugins": false, "disableUseStrict": false, "condition": true, "swc": false, "disableSWC": true, "ignoreDevUnusedFiles": false, "ignoreUploadUnusedFiles": false}, "compileType": "miniprogram", "simulatorPluginLibVersion": {}, "packOptions": {"ignore": [], "include": []}, "appid": "wxfb5acefd7300185b", "editorSetting": {}, "libVersion": "3.8.9", "condition": {"miniprogram": {"list": [{"name": "开发环境", "pathName": "pages/index/index", "query": "", "launchMode": "default", "scene": null, "envVersion": "develop"}, {"name": "测试环境", "pathName": "pages/index/index", "query": "", "launchMode": "default", "scene": null, "envVersion": "trial"}, {"name": "生产环境", "pathName": "pages/index/index", "query": "", "launchMode": "default", "scene": null, "envVersion": "release"}]}}}