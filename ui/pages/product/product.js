const config = require('../../config/setting');
const { getImageUrl } = config;
const app = getApp();

Page({
  data: {
    categories: [],
    products: [
      {
        id: '',
        category: '',
        img: '',
        title: '',
        size: '',
        price: '',
        limit: ''
      },
      // ...更多商品，category字段对应左侧分类id
    ],
    currentCategory: 0,
    currentProducts: []
  },

  // 加载产品分类
  loadCategories() {
    wx.request({
      url: config.product_zones,
      method: 'GET',
      success: (res) => {
        if (res.data && Array.isArray(res.data)) {
          // 对分类进行排序：全部分类始终在最前面，其他按 sort_order 排序
          const sortedCategories = this.sortCategories(res.data);

          this.setData({
            categories: sortedCategories
          });
        }
      },
      fail: (err) => {
        console.error('加载分类失败:', err);
        wx.showToast({
          title: '加载分类失败',
          icon: 'none'
        });
      }
    });
  },

  // 分类排序方法
  sortCategories(categories) {
    // 分离"全部"分类和其他分类
    const allCategory = categories.find(cat => cat.zone_key === 'all' || cat.id === 0);
    const otherCategories = categories.filter(cat => cat.zone_key !== 'all' && cat.id !== 0);

    // 对其他分类按 sort_order 排序
    otherCategories.sort((a, b) => {
      const sortA = a.sort_order || 0;
      const sortB = b.sort_order || 0;
      return sortA - sortB;
    });

    // 如果存在"全部"分类，将其放在最前面
    if (allCategory) {
      return [allCategory, ...otherCategories];
    } else {
      return otherCategories;
    }
  },

  refresh(){
    wx.showLoading({
      mask:true
    })
    wx.request({
      url: config.rootUrl + '/products-with-categories',
      method:'GET',
      success:(res)=>{
          if (res.data && res.data.success) {
            // 处理产品数据，将price_sale字段赋值给price，并处理图片URL
            const processedProducts = res.data.data.map(product => ({
              ...product,
              img: getImageUrl(product.img), // 处理商品图片URL
              price: product.price_sale ? (Number(product.price_sale) / 100).toFixed(2) : '0.00' // 将分转换为元
            }));

            this.setData({
              products: processedProducts
            });
          } else {
            // 如果新接口失败，回退到原接口
            wx.request({
              url: config.product,
              method:'GET',
              success:(res)=>{
                // 同样处理原接口的数据
                const processedProducts = res.data.map(product => ({
                  ...product,
                  img: getImageUrl(product.img), // 处理商品图片URL
                  price: product.price_sale ? (Number(product.price_sale) / 100).toFixed(2) : '0.00'
                }));

                this.setData({
                  products: processedProducts
                });
                this.filterProducts(this.data.currentCategory);
              }
            });
            return;
          }
          this.filterProducts(this.data.currentCategory);
      },
      complete:()=>{
        wx.hideLoading()
      },
    })
  },
  onLoad() {
    this.loadCategories();
    this.refresh();
  },
  onShow() {
    const zone = app.globalData.navigateToProductZone;
    if (zone) {
      // 根据zone_key查找对应的分类
      const category = this.data.categories.find(cat => cat.zone_key === zone);
      if (category) {
        const categoryIndex = this.data.categories.findIndex(cat => cat.zone_key === zone);
        this.setData({
          currentCategory: categoryIndex
        });
        this.filterProducts(categoryIndex);
      }

      // Reset the global data so it doesn't trigger again
      app.globalData.navigateToProductZone = null;
    }
  },
  onCategoryTap(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentCategory: index
    });
    this.filterProducts(index);
  },
  filterProducts(categoryIndex) {
    let filtered;
    const selectedCategory = this.data.categories[categoryIndex];

    if (!selectedCategory || selectedCategory.id === 0 || selectedCategory.zone_key === 'all') {
      // 显示全部产品
      filtered = this.data.products;
    } else {
      // 根据category_ids字段筛选产品（从index_product表关联得到的分类ID列表）
      filtered = this.data.products.filter(item => {
        if (!item.category_ids) return false;
        const categoryIds = item.category_ids.split(',').map(id => Number(id));
        return categoryIds.includes(Number(selectedCategory.id));
      });
    }
    this.setData({
      currentProducts: filtered
    });
  },
  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/productDetail/productDetail?id=${id}`
    });
  }
});