<!-- 顶部条形栏 -->
<view class="top-bar">
  <text class="top-bar-title">产品中心</text>
</view>

<view class="container">
  <view class="sidebar">
    <!-- 左侧类别栏 -->
    <scroll-view class="category-bar" scroll-y="true">
      <block wx:for="{{categories}}" wx:key="id">
        <view
          class="category-item {{currentCategory === index ? 'active' : ''}}"
          bindtap="onCategoryTap"
          data-index="{{index}}"
        >
          {{item.name}}
        </view>
      </block>
    </scroll-view>
  </view>
  <view class="main-content">
    <!-- 右侧商品列表 -->
    <scroll-view class="product-list" scroll-y="true">
      <block wx:for="{{currentProducts}}" wx:key="id">
        <view class="product-card" bindtap="goToDetail" data-id="{{item.id}}">
          <image class="product-img" src="{{item.img}}" mode="aspectFill"/>
          <view class="product-info">
            <view class="product-title">{{item.title}}</view>
            <view class="product-size">{{item.size}}</view>
            <view class="product-meta">
              <text class="product-price">￥{{item.price}} 起</text>
              <text class="product-limit">{{item.limit_size}}张起订</text>
            </view>
          </view>
          <!-- 管理员删除按钮 -->
          <view class="delete-btn-container" wx:if="{{isAdmin}}" bindtap="onDeleteProduct" data-id="{{item.id}}" data-title="{{item.title}}" catchtap="true">
            <text class="delete-btn-text">删除</text>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
</view>