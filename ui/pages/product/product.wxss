.container {
  display: flex;
  flex-direction: row;
}

.sidebar {
  position: fixed;
  top: 100rpx; /* 如果有顶部栏，需加上顶部栏高度 */
  left: 0;
  width: 160rpx; /* 你实际的宽度 */
  height: calc(100vh - 100rpx); /* 100rpx为顶部栏高度，需同步调整 */
  background: #ffe4ec;
  z-index: 10;
  overflow-y: auto;
  border-radius: 0 0 30rpx 0;
}

.main-content {
  margin-left: 160rpx; /* 与sidebar宽度一致 */
  margin-top: 100rpx;  /* 与顶部栏高度一致 */
  flex: 1;
  padding: 20rpx 0 20rpx 20rpx;
  height: 100vh;
}

.product-page {
  display: flex;
  height: 100vh;
  background: #fff;
}
.category-bar {
  width: 160rpx;
  background: #ffe0e0;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
  border-radius: 0 24rpx 24rpx 0;
}
.category-item {
  padding: 18rpx 0;
  text-align: center;
  color: #d94f4f;
  font-size: 26rpx;
  border-left: 8rpx solid transparent;
  background: transparent;
  transition: background 0.2s;
}
.category-item.active {
  background: #fff;
  color: #ff4081;
  font-weight: bold;
  border-left: 8rpx solid #ff4081;
  border-radius: 0 24rpx 24rpx 0;
}
.product-list {
  flex: 1;
  padding: 20rpx 0 20rpx 20rpx;
  height: 100vh;
}
.product-card {
  display: flex;
  margin-bottom: 32rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  padding: 16rpx;
  align-items: center;
}
.product-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  background: #f8f8f8;
}
.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.product-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.product-size {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 8rpx;
}
.product-meta {
  display: flex;
  align-items: center;
  gap: 16rpx;
}
.product-price {
  color: #ff4081;
  font-size: 26rpx;
  font-weight: bold;
}
.product-limit {
  color: #bbb;
  font-size: 22rpx;
}

.top-bar {
  width: 100%;
  height: 100rpx; /* 可根据实际调整高度 */
  background: #ffb6c1; /* 粉色，可根据实际调整 */
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed; /* 如果你想让它悬浮在最上方 */
  top: 0;
  left: 0;
  z-index: 100;
}
.top-bar-title {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  letter-spacing: 4rpx;
}

.page-content {
  margin-top: 100rpx;
}