// pages/customized/customized.js
function groupArray(arr, groupSize = 2) {
  const result = [];
  for (let i = 0; i < arr.length; i += groupSize) {
    let group = arr.slice(i, i + groupSize);
    while (group.length < groupSize) {
      group.push(null);
    }
    result.push(group);
  }
  return result;
}

const config = require('../../config/setting');
const { getImageUrl } = config;

Page({

  /**
   * 页面的初始数据
   */
  data: {
    popupProduct: {},
    imgList: [], // 所有图片路径（可能包含处理后的图片）
    originalImgList: [], // 用户原始上传的图片路径（永远不变）
    groupedImgList: [], // 每两张为一组
    groupCounts: [], // 每组数量
    borderList: [], // 每组边框
    backList: [], // 每组卡背
    previewImages: [], // 预览图片数组，存储合成后的预览图
    serverPathList: [], // 服务器路径数组，存储上传后的相对路径
    selectedBorder: null, // 从底部按钮选择的边框
    selectedCardBack: null, // 从底部按钮选择的卡背
    editedImage: '', // 编辑后的图片路径
    totalCount: 0,
    showUploadModal: false,
    showBorderModal: false, // 边框选择弹窗
    currentCategory: 'all', // 当前选中的分类
    borderCategories: [], // 边框分类列表
    allBorderImages: [], // 所有边框图片
    currentBorderImages: [], // 当前分类的边框图片
    currentBorderGroup: null, // 当前操作的图片组
    currentBorderImgIdx: null, // 当前操作的图片索引
    uploadGroups: [
      {
        key: 'frontBack',
        img: '/images/upload_placeholder/all.png',
        title: '批量上传（正反面）',
        desc: '按上传顺序上传为正反面'
      },
      {
        key: 'front',
        img: '/images/upload_placeholder/正面.png',
        title: '批量上传（正面）',
        desc: '按上传顺序上传为正面'
      },
      {
        key: 'back',
        img: '/images/upload_placeholder/反面.jpg',
        title: '批量上传（反面）',
        desc: '按上传顺序上传为反面'
      }
    ],
    showBackModal: false,
    currentBackCategory: 'all',
    backCategories: [],
    allBackImages: [],
    currentBackImages: [],
    currentBackGroup: null,
    currentBackImgIdx: null,
    processName: '',
    processPrice: '',
    limit: null,
    productPrice: 0,
    totalPrice: 0,

    // 预览Canvas相关
    previewCanvas: null,
    previewCtx: null,
  },




  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const processName = options.processName ? decodeURIComponent(options.processName) : '';
    const processPrice = options.processPrice ? decodeURIComponent(options.processPrice) : '';

    const selectedOptions = options.selectedOptions ? decodeURIComponent(options.selectedOptions) : '';
    const category = options.category ? decodeURIComponent(options.category) : 'DIY';

    // 为显示用途创建格式化的价格（除以100）
    const displayPrice = processPrice ? (Number(processPrice) / 100).toFixed(2) : '';

    this.setData({
      processName,
      processPrice, // 原始价格（分），用于计算
      displayPrice, // 显示价格（元），用于界面显示
      category, // 产品类别
      isDIY: category === 'DIY', // 是否为DIY产品
      isREADY: category === 'READY' // 是否为READY产品
    });

    // 新增：获取商品详情
    const productId = options.productId;
    if (productId) {
      this.fetchProductDetail(productId);
    }

    // 如果是READY产品，初始化一个默认的图片组
    if (category === 'READY') {
      this.initReadyProduct();
    }

    this.loadBorderData();

    // 初始化预览Canvas
    this.initPreviewCanvas();
  },

  // 初始化预览Canvas
  async initPreviewCanvas() {
    try {
      const query = wx.createSelectorQuery().in(this);
      const canvas = await new Promise((resolve, reject) => {
        query.select('#previewCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0]) {
              resolve(res[0].node);
            } else {
              reject(new Error('获取预览画布失败'));
            }
          });
      });

      const ctx = canvas.getContext('2d');

      // 设置画布尺寸 - 增大到300x400以匹配显示尺寸
      const dpr = wx.getSystemInfoSync().pixelRatio;
      canvas.width = 300 * dpr;
      canvas.height = 400 * dpr;
      ctx.scale(dpr, dpr);

      this.setData({
        previewCanvas: canvas,
        previewCtx: ctx
      });

      console.log('预览Canvas初始化成功');
    } catch (error) {
      console.error('预览Canvas初始化失败:', error);
    }
  },

  // 初始化READY产品
  initReadyProduct() {
    // 为READY产品创建一个默认的图片组，用于数量选择
    const defaultImgList = ['', '']; // 两个空位置
    const groupedImgList = groupArray(defaultImgList, 2);
    const groupCounts = [1]; // 默认数量为1

    this.setData({
      imgList: defaultImgList,
      originalImgList: defaultImgList,
      groupedImgList: groupedImgList,
      groupCounts: groupCounts,
      totalCount: 1
    });

    this.calculateTotalPrice();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 打开上传弹窗
  openUploadModal() {
    this.setData({ showUploadModal: true });
  },

  // 关闭上传弹窗
  closeUploadModal() {
    this.setData({ showUploadModal: false });
  },

  // 单张上传
  chooseImage() {
    wx.chooseImage({
      count: 9,
      success: (res) => {
        this._addImages(res.tempFilePaths);
      }
    });
  },

  // 批量上传
  onBatchUpload(e) {
    const key = e && e.currentTarget && e.currentTarget.dataset.key;
    wx.chooseImage({
      count: 9,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: (res) => {
        this.setData({ showUploadModal: false });
        const newImgs = res.tempFilePaths;
        if (key === 'front') {
          // 只放到每组左边（偶数位）
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          let serverPathList = this.data.serverPathList || [];
          let groupCount = Math.max(Math.ceil((imgList.length + newImgs.length) / 2), Math.ceil(imgList.length / 2));
          for (let i = 0; i < newImgs.length; i++) {
            let idx = i * 2;
            if (imgList.length < idx + 1) {
              while (imgList.length < idx) {
                imgList.push('');
                originalImgList.push('');
                serverPathList.push(null);
              }
              imgList.push(newImgs[i]);
              originalImgList.push(newImgs[i]);
              serverPathList.push(null);
            } else {
              imgList[idx] = newImgs[i];
              originalImgList[idx] = newImgs[i];
              serverPathList[idx] = null;
            }
          }
          const groupedImgList = groupArray(imgList, 2);
          let groupCounts = this.data.groupCounts;

          // 每组数量固定初始化为1
          while (groupCounts.length < groupedImgList.length) {
            groupCounts.push(1);
          }
          this.setData({
            imgList,
            originalImgList,
            groupedImgList,
            groupCounts,
            serverPathList
          });
        } else if (key === 'back') {
          // 只放到每组右边（奇数位）
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          let serverPathList = this.data.serverPathList || [];
          let groupCount = Math.max(Math.ceil((imgList.length + 1) / 2), Math.ceil(imgList.length / 2));
          for (let i = 0; i < newImgs.length; i++) {
            let idx = i * 2 + 1;
            if (imgList.length < idx + 1) {
              while (imgList.length < idx) {
                imgList.push('');
                originalImgList.push('');
                serverPathList.push(null);
              }
              imgList.push(newImgs[i]);
              originalImgList.push(newImgs[i]);
              serverPathList.push(null);
            } else {
              imgList[idx] = newImgs[i];
              originalImgList[idx] = newImgs[i];
              serverPathList[idx] = null;
            }
          }
          const groupedImgList = groupArray(imgList, 2);
          let groupCounts = this.data.groupCounts;

          // 每组数量固定初始化为1
          while (groupCounts.length < groupedImgList.length) {
            groupCounts.push(1);
          }
          this.setData({
            imgList,
            originalImgList,
            groupedImgList,
            groupCounts,
            serverPathList
          });
        } else {
          // 其他批量上传逻辑（如正反面）
          this._addImages(newImgs);
        }
      }
    });
  },

  // 添加图片并分组
  _addImages(newImgs) {
    const imgList = this.data.imgList.concat(newImgs);
    const originalImgList = this.data.originalImgList.concat(newImgs); // 同时保存到原始图片列表
    const groupedImgList = groupArray(imgList, 2);
    let groupCounts = this.data.groupCounts;
    let serverPathList = this.data.serverPathList || [];

    // 确保serverPathList与imgList长度一致
    while (serverPathList.length < imgList.length) {
      serverPathList.push(null); // 新上传的图片暂时没有服务器路径
    }

    // 为新增的组设置数量为1
    while (groupCounts.length < groupedImgList.length) {
      groupCounts.push(1);
    }

    this.setData({
      imgList,
      originalImgList, // 保存原始图片列表
      groupedImgList,
      groupCounts,
      serverPathList // 更新服务器路径列表
    });
    this.updateTotalCount();
  },

  // 数量加减
  changeCount(e) {
    const { groupIdx, delta } = e.currentTarget.dataset;
    let groupCounts = this.data.groupCounts;
    let newCount = Number(groupCounts[groupIdx]) + Number(delta);

    // 最小数量固定为1
    if (newCount < 1) newCount = 1;
    groupCounts[groupIdx] = newCount;
    this.setData({ groupCounts });
    this.updateTotalCount();
  },

  // 编辑按钮事件 - 跳转到图片编辑器
  onEdit(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);
    const realIndex = groupIdx * 2 + imgIdx;

    const { originalImgList, borderList, backList } = this.data;

    // 获取正面和反面图片
    const frontIndex = groupIdx * 2;
    const backIndex = groupIdx * 2 + 1;
    const frontImage = originalImgList[frontIndex] || '';
    const backImage = originalImgList[backIndex] || '';

    // 检查是否有图片可以编辑
    if (!frontImage && !backImage) {
      wx.showToast({
        title: '请先上传图片',
        icon: 'none'
      });
      return;
    }

    // 根据图片位置判断编辑类型
    const border = borderList[groupIdx];
    const cardBack = backList[groupIdx];

    // 获取产品尺寸信息
    const productSize = this.data.product ? this.data.product.size : '';

    if (imgIdx === 0) {
      // 左边图片 - 边框模式
      let url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent(frontImage)}&backImage=${encodeURIComponent(backImage)}&editType=border&groupIndex=${groupIdx}&imgIndex=${imgIdx}&currentSide=front`;

      // 传递产品尺寸信息
      if (productSize) {
        url += `&productSize=${encodeURIComponent(productSize)}`;
      }

      // 传递边框信息
      if (border) {
        url += `&borderImage=${encodeURIComponent(border.image_url)}`;
      }

      // 同时传递卡背信息，以便切换到反面时使用
      if (cardBack) {
        url += `&cardBackImage=${encodeURIComponent(cardBack.image_url)}`;
      }

      wx.navigateTo({
        url: url
      });
    } else {
      // 右边图片 - 卡背模式
      let url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent(frontImage)}&backImage=${encodeURIComponent(backImage)}&editType=cardback&groupIndex=${groupIdx}&imgIndex=${imgIdx}&currentSide=back`;

      // 传递产品尺寸信息
      if (productSize) {
        url += `&productSize=${encodeURIComponent(productSize)}`;
      }

      // 传递卡背信息
      if (cardBack) {
        url += `&cardBackImage=${encodeURIComponent(cardBack.image_url)}`;
      }

      // 同时传递边框信息，以便切换到正面时使用
      if (border) {
        url += `&borderImage=${encodeURIComponent(border.image_url)}`;
      }

      wx.navigateTo({
        url: url
      });
    }
  },

  onUploadBtn(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: (res) => {
        const filePath = res.tempFilePaths[0];
        let imgList = this.data.imgList.slice();
        let originalImgList = this.data.originalImgList.slice();
        let serverPathList = this.data.serverPathList || [];

        // 确保serverPathList长度足够
        while (serverPathList.length < imgList.length) {
          serverPathList.push(null);
        }

        // 计算在imgList中的真实索引
        const realIdx = groupIdx * 2 + imgIdx;
        imgList[realIdx] = filePath;
        originalImgList[realIdx] = filePath; // 同时更新原始图片列表
        serverPathList[realIdx] = null; // 新上传的图片清空服务器路径

        const groupedImgList = groupArray(imgList, 2);
        this.setData({
          imgList,
          originalImgList,
          groupedImgList,
          serverPathList
        });
      }
    });
  },

  onCopyBtn(e) {
    wx.showToast({ title: '复制', icon: 'none' });
  },

  onBorderBtn(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);

    // 检查是否已经有边框数据，如果没有则加载
    if (this.data.allBorderImages.length === 0) {
      console.log('边框数据为空，重新加载');
      this.loadBorderData();
    }

    this.setData({
      showBorderModal: true,
      currentBorderGroup: groupIdx,
      currentBorderImgIdx: imgIdx,
      currentCategory: 'all',
      currentBorderImages: this.data.allBorderImages // 使用已加载的数据
    });
  },

  onBackBtn(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);

    this.setData({
      showBackModal: true,
      currentBackGroup: groupIdx,
      currentBackImgIdx: imgIdx
    });
    this.loadBackData();
  },

  onCountInput(e) {
    const groupIdx = e.currentTarget.dataset.groupIdx;
    let value = e.detail.value;
    let groupCounts = this.data.groupCounts;
    groupCounts[groupIdx] = value;
    this.setData({ groupCounts });
    this.updateTotalCount();
  },

  onCountBlur(e) {
    const groupIdx = e.currentTarget.dataset.groupIdx;
    let value = parseInt(e.detail.value, 10);

    // 最小数量固定为1
    if (isNaN(value) || value < 1) value = 1;
    let groupCounts = this.data.groupCounts;
    groupCounts[groupIdx] = value;
    this.setData({ groupCounts });
    this.updateTotalCount();
  },

  onDeleteGroup(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const groupSize = 2;
    const start = groupIdx * groupSize;
    const end = start + groupSize;
    let imgList = this.data.imgList.slice();
    imgList.splice(start, groupSize);
    const groupedImgList = groupArray(imgList, groupSize);
    let groupCounts = this.data.groupCounts.slice();
    groupCounts.splice(groupIdx, 1);

    // 为新组设置数量为1
    while (groupCounts.length < groupedImgList.length) {
      groupCounts.push(1);
    }
    this.setData({
      imgList,
      groupedImgList,
      groupCounts
    });
    this.updateTotalCount();
  },

  // 去重函数 - 根据id去重
  removeDuplicateBorders(borderArray) {
    const seen = new Set();
    return borderArray.filter(border => {
      if (seen.has(border.id)) {
        console.log('发现重复边框，已过滤:', border.name, border.id);
        return false;
      }
      seen.add(border.id);
      return true;
    });
  },

  // 加载边框数据
  loadBorderData() {
    // 如果已经有边框数据，则不重复加载
    if (this.data.allBorderImages.length > 0) {
      console.log('边框数据已存在，跳过加载');
      return;
    }

    console.log('loadBorderData: 开始加载边框数据');
    // 获取边框分类
    wx.request({
      url: config.rootUrl + '/border-categories',
      method: 'GET',
      success: (res) => {
        console.log('边框分类API响应:', res);
        if (res.data.code === 200) {
          // 分类category_id转字符串
          const categories = res.data.data.map(cat => ({
            ...cat,
            id: String(cat.category_id)
          }));
          console.log('处理后的边框分类:', categories);
          this.setData({
            borderCategories: categories
          });
        } else {
          console.error('边框分类API返回错误:', res.data);
          // 使用模拟数据
          this.setData({
            borderCategories: [
              { id: '5', name: '每日更新' },
              { id: '7', name: '夏日清凉' },
              { id: '9', name: '国色天香' }
            ]
          });
        }
      },
      fail: (err) => {
        console.error('获取边框分类失败:', err);
        // 使用模拟数据
        this.setData({
          borderCategories: [
            { id: '5', name: '每日更新' },
            { id: '7', name: '夏日清凉' },
            { id: '9', name: '国色天香' }
          ]
        });
      }
    });

    // 获取所有边框素材
    wx.request({
      url: config.rootUrl + '/borders',
      method: 'GET',
      success: (res) => {
        console.log('边框素材API响应:', res);
        if (res.data.code === 200 || res.data.success === true) {
          // 图片category_id转字符串，并处理图片URL
          const images = res.data.data.map(img => ({
            ...img,
            category_id: String(img.category_id),
            image_url: getImageUrl(img.image_url),
            thumbnail_url: getImageUrl(img.thumbnail_url)
          }));
          console.log('处理后的边框素材:', images);
          // 去重处理
          const uniqueImages = this.removeDuplicateBorders(images);
          console.log('去重后的边框素材:', uniqueImages);
          this.setData({
            allBorderImages: uniqueImages,
            currentBorderImages: uniqueImages
          });
        } else {
          console.error('边框素材API返回错误:', res.data);
          // 使用模拟数据
          this.setData({
            allBorderImages: [
              { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
              { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
              { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
              { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
              { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
            ],
            currentBorderImages: [
              { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
              { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
              { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
              { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
              { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
            ]
          });
        }
      },
      fail: (err) => {
        console.error('获取边框图片失败:', err);
        // 使用模拟数据
        const mockBorderImages = [
          { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
          { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
          { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
          { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
          { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
        ].map(item => ({
          ...item,
          image_url: getImageUrl(item.image_url)
        }));

        // 去重处理
        const uniqueMockImages = this.removeDuplicateBorders(mockBorderImages);
        console.log('去重后的模拟边框素材:', uniqueMockImages);
        this.setData({
          allBorderImages: uniqueMockImages,
          currentBorderImages: uniqueMockImages
        });
      }
    });
  },

  // 关闭边框选择弹窗
  closeBorderModal() {
    this.setData({
      showBorderModal: false,
      currentBorderGroup: null,
      currentBorderImgIdx: null
    });
  },

  // 切换分类
  switchCategory(e) {
    const category = e.currentTarget.dataset.category;
    let currentBorderImages = [];
    if (category === 'all') {
      currentBorderImages = this.data.allBorderImages;
    } else {
      currentBorderImages = this.data.allBorderImages.filter(item =>
        item.category_id === category
      );
    }

    // 确保去重
    const uniqueImages = this.removeDuplicateBorders(currentBorderImages);
    console.log(`切换到分类 ${category}，去重前: ${currentBorderImages.length}，去重后: ${uniqueImages.length}`);

    this.setData({
      currentCategory: category,
      currentBorderImages: uniqueImages
    });
  },

  // 选择边框
  selectBorder(e) {
    const border = e.currentTarget.dataset.border;
    const { currentBorderGroup, currentBorderImgIdx, originalImgList } = this.data;

    if (currentBorderGroup !== null) {
      // 记录边框到对应分组
      let borderList = this.data.borderList.slice();
      borderList[currentBorderGroup] = border;
      this.setData({ borderList });

      // 获取用户原始上传的图片（而不是处理后的图片）
      const imgIndex = currentBorderImgIdx !== null ? currentBorderImgIdx : 0;
      const realIndex = currentBorderGroup * 2 + imgIndex;
      const originalImage = originalImgList[realIndex];

      console.log('选择边框 - 生成预览:', {
        currentBorderGroup,
        imgIndex,
        realIndex,
        originalImage,
        borderUrl: border.image_url
      });

      if (originalImage) {
        // 生成预览图而不是跳转到编辑页面
        this.generateBorderPreview(originalImage, border.image_url, realIndex);

        wx.showToast({
          title: `已选择边框: ${border.name}`,
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '请先上传图片',
          icon: 'none'
        });
      }
    } else {
      // 从底部按钮点击，暂存选择的边框
      this.setData({
        selectedBorder: border
      });

      wx.showToast({
        title: `已选择边框: ${border.name}`,
        icon: 'success'
      });
    }

    this.closeBorderModal();
  },

  loadBackData() {
    // 获取卡背分类
    wx.request({
      url: config.rootUrl + '/card-back-categories',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200) {
          const categories = res.data.data.map(cat => ({
            ...cat,
            id: String(cat.category_id)
          }));
          this.setData({
            backCategories: categories
          });
        }
      },
      fail: (err) => {
        console.error('获取卡背分类失败:', err);
      }
    });

    // 获取所有卡背素材
    wx.request({
      url: config.rootUrl + '/card-backs',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200) {
          const images = res.data.data.map(img => ({
            ...img,
            category_id: String(img.category_id),
            image_url: getImageUrl(img.image_url),
            thumbnail_url: getImageUrl(img.thumbnail_url)
          }));
          this.setData({
            allBackImages: images,
            currentBackImages: images
          });
        }
      },
      fail: (err) => {
        console.error('获取卡背图片失败:', err);
      }
    });
  },

  closeBackModal() {
    this.setData({
      showBackModal: false,
      currentBackGroup: null,
      currentBackImgIdx: null
    });
  },

  switchBackCategory(e) {
    const category = e.currentTarget.dataset.category;
    let currentBackImages = [];
    if (category === 'all') {
      currentBackImages = this.data.allBackImages;
    } else {
      currentBackImages = this.data.allBackImages.filter(item =>
        item.category_id === category
      );
    }
    this.setData({
      currentBackCategory: category,
      currentBackImages: currentBackImages
    });
  },

  // 选择卡背
  selectBack(e) {
    const back = e.currentTarget.dataset.back;
    const { currentBackGroup } = this.data;

    if (currentBackGroup !== null) {
      let backList = this.data.backList.slice();
      backList[currentBackGroup] = back;
      this.setData({ backList });

      // 生成卡背预览图
      const imgIndex = this.data.currentBackImgIdx || 1;
      const realIndex = currentBackGroup * 2 + imgIndex;

      console.log('选择卡背 - 生成预览:', {
        currentBackGroup,
        imgIndex,
        realIndex,
        cardBackUrl: back.image_url
      });

      this.generateCardBackPreview(back.image_url, realIndex);

      wx.showToast({
        title: `已选择卡背: ${back.name}`,
        icon: 'success'
      });
    } else {
      // 从底部按钮点击，暂存选择的卡背
      this.setData({
        selectedCardBack: back
      });

      wx.showToast({
        title: `已选择卡背: ${back.name}`,
        icon: 'success'
      });
    }

    this.closeBackModal();
  },

  onBackTap() {
    wx.navigateBack();
  },

  // 新增方法
  fetchProductDetail(productId) {
    wx.request({
      url: `${config.rootUrl}/product/${productId}`,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          const product = res.data.data;

          // 基础价格优先使用售价，其次标价，转换为元
          const basePrice = (Number(product.price_sale) || Number(product.price_tag) || 0) / 100;

          this.setData({
            product,
            limit: product.limit,
            productPrice: basePrice
          });
          this.updateTotalPrice();
        }
      },
      fail: () => {
        wx.showToast({ title: '获取商品信息失败', icon: 'none' });
      }
    });
  },

  updateTotalCount() {
    const total = this.data.groupCounts.reduce((sum, n) => sum + Number(n), 0);
    this.setData({ totalCount: total }, () => {
      this.updateTotalPrice();
    });
  },

  updateTotalPrice() {
    // 计算总价格 = (产品单价 + 工艺单价) * 总数量
    const productPrice = Number(this.data.productPrice) || 0; // 产品单价（元）
    const processPrice = Number(this.data.processPrice) / 100 || 0; // 工艺单价（转换为元）
    const totalCount = Number(this.data.totalCount) || 0;

    const totalPrice = ((productPrice + processPrice) * totalCount).toFixed(2);
    this.setData({ totalPrice });
  },

  onAddToCart() {
    const that = this;
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';
    if (!mobile) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }
    const { product, processName, processPrice, limit, title, size } = that.data;
    // 组装每组卡片明细
    const cartItems = that.data.groupedImgList.map((group, idx) => {
      const serverPathList = that.data.serverPathList || [];
      const realIndex = idx * 2; // 计算在图片列表中的真实索引

      return {
        quantity: that.data.groupCounts[idx],
        // 优先使用服务器路径，如果没有则使用本地路径
        front_image: serverPathList[realIndex] || group[0] || '',
        back_image: serverPathList[realIndex + 1] || group[1] || '',
        border: that.data.borderList[idx] ? that.data.borderList[idx].name : '',
        card_back: that.data.backList[idx] ? that.data.backList[idx].name : '',
      };
    });
    if (!cartItems.length) {
      wx.showToast({ title: '请先选择图片', icon: 'none' });
      return;
    }

    // 调试日志：检查图片路径
    console.log('🛒 加入购物车 - 图片路径信息:');
    console.log('serverPathList:', that.data.serverPathList);
    console.log('groupedImgList:', that.data.groupedImgList);
    console.log('构建的cartItems:', cartItems);

    // 详细显示每组的图片路径信息
    cartItems.forEach((item, index) => {
      console.log(`第${index + 1}组:`, {
        数量: item.quantity,
        正面图片: item.front_image,
        反面图片: item.back_image,
        边框: item.border,
        卡背: item.card_back,
        是否使用服务器路径: {
          正面: item.front_image.startsWith('/miniapp/'),
          反面: item.back_image.startsWith('/miniapp/')
        }
      });
    });
    // 处理 limit 字段，保证为数字
    let limitValue = limit;
    if (typeof limit === 'string') {
      const match = limit.match(/\d+/);
      limitValue = match ? parseInt(match[0]) : 0;
    }
    // 获取商品主图
    let img = product && product.img ? product.img : '';
    // 发送请求
    wx.request({
      url: config.rootUrl + '/shoppingcar/add',
      method: 'POST',
      header: { 'content-type': 'application/json' },
      data: {
        mobile,
        product_id: product ? product.id : '',
        title: product ? product.title : title,
        size: product ? product.size : size,
        limit: limitValue,
        process_name: processName,
        process_price: processPrice,
        img: img, // 新增主图字段
        items: cartItems
      },
      success(res) {
        if (res.data && res.data.success) {
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 800,
            success() {
              setTimeout(() => {
                wx.switchTab({ url: '/pages/shopping/shopping' });
              }, 800);
            }
          });
        } else {
          wx.showToast({ title: '加入购物车失败', icon: 'none' });
        }
      },
      fail() {
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },

  // 立即定制功能已取消，现在只能通过购物车下单
  onImmediateCustomize() {
    wx.showToast({
      title: '请先加入购物车，然后在购物车中下单',
      icon: 'none',
      duration: 2000
    });
  },

  // 底部边框模板按钮点击事件
  onBottomBorderTemplate() {
    console.log('点击边框模板按钮');

    // 检查是否已经有边框数据，如果没有则加载
    if (this.data.allBorderImages.length === 0) {
      console.log('边框数据为空，重新加载');
      this.loadBorderData();
    }

    // 直接显示弹窗，使用已加载的数据
    this.setData({
      showBorderModal: true,
      currentBorderGroup: null,
      currentBorderImgIdx: null,
      currentCategory: 'all',
      currentBorderImages: this.data.allBorderImages // 使用已加载的数据
    });

    console.log('边框弹窗已显示，当前边框数量:', this.data.allBorderImages.length);
  },

  // 底部卡背模板按钮点击事件
  onBottomCardBackTemplate() {
    console.log('点击卡背模板按钮');

    // 直接使用模拟数据，并处理图片URL
    const backImagesData = [
      // 每日更新分类
      { id: 27, name: '020', image_url: '/miniapp/material/20250802171209_2504b0fe.jpg', category_id: '6' },
      { id: 26, name: '019', image_url: '/miniapp/material/20250802171156_88a4499c.jpeg', category_id: '6' },
      { id: 25, name: '018', image_url: '/miniapp/material/20250802171145_1bce21a1.jpeg', category_id: '6' },
      { id: 24, name: '017', image_url: '/miniapp/material/20250802171134_b8832c84.jpeg', category_id: '6' },
      // 夏日清凉分类
      { id: 23, name: '016', image_url: '/miniapp/material/20250802171122_ecb036eb.jpeg', category_id: '8' },
      { id: 22, name: '015', image_url: '/miniapp/material/20250802171110_e82bbbfc.jpeg', category_id: '8' },
      { id: 21, name: '015', image_url: '/miniapp/material/20250802171059_222b46a9.jpeg', category_id: '8' },
      { id: 16, name: '010', image_url: '/miniapp/material/20250802170945_56356a85.jpeg', category_id: '8' },
      { id: 15, name: '009', image_url: '/miniapp/material/20250802170933_c02357b0.jpeg', category_id: '8' },
      // 国色天香分类
      { id: 18, name: '012', image_url: '/miniapp/material/20250802171007_cfb6503c.jpeg', category_id: '10' },
      { id: 17, name: '011', image_url: '/miniapp/material/20250802170956_114e3855.jpeg', category_id: '10' },
      { id: 20, name: '014', image_url: '/miniapp/material/20250802171044_5b9d6b9d.jpeg', category_id: '10' },
      { id: 19, name: '013', image_url: '/miniapp/material/20250802171026_eca0a233.jpeg', category_id: '10' }
    ];

    // 处理图片URL
    const processedBackImages = backImagesData.map(item => ({
      ...item,
      image_url: getImageUrl(item.image_url)
    }));

    this.setData({
      showBackModal: true,
      currentBackGroup: null,
      currentBackImgIdx: null,
      currentBackCategory: 'all',
      backCategories: [
        { id: '6', name: '每日更新' },
        { id: '8', name: '夏日清凉' },
        { id: '10', name: '国色天香' }
      ],
      allBackImages: processedBackImages,
      currentBackImages: processedBackImages
    });

    console.log('卡背数据已设置');
  },

  // 处理编辑后的图片
  updateEditedImage(editedImagePath, groupIndex, imgIndex, editType, serverPath = null) {
    console.log('更新编辑后的图片:', {
      editedImagePath,
      groupIndex,
      imgIndex,
      editType,
      serverPath
    });
    console.log('当前图片列表:', this.data.imgList);
    console.log('原始图片列表:', this.data.originalImgList);

    let imgList = this.data.imgList.slice();
    let serverPathList = this.data.serverPathList || [];

    // 确保服务器路径列表长度与图片列表一致
    while (serverPathList.length < imgList.length) {
      serverPathList.push(null);
    }

    // 注意：不修改originalImgList，保持用户原始图片不变

    // 计算在imgList中的真实索引
    const realIndex = groupIndex * 2 + imgIndex;

    console.log('计算的真实索引:', realIndex, '图片列表长度:', imgList.length);

    if (realIndex < imgList.length) {
      // 更新对应位置的图片（只更新显示用的imgList）
      const oldImage = imgList[realIndex];
      const oldServerPath = serverPathList[realIndex];
      const originalImage = this.data.originalImgList[realIndex];

      imgList[realIndex] = editedImagePath;
      serverPathList[realIndex] = serverPath;

      console.log('图片更新:', {
        position: `第${groupIndex + 1}组第${imgIndex + 1}张`,
        originalImage, // 原始图片保持不变
        oldImage,
        newImage: editedImagePath,
        oldServerPath,
        newServerPath: serverPath
      });

      // 重新分组
      const groupedImgList = groupArray(imgList, 2);

      // 同时更新预览图
      let previewImages = this.data.previewImages.slice();
      previewImages[realIndex] = editedImagePath;

      // 如果是边框编辑，清除对应组的边框记录（因为图片已经包含边框效果）
      let borderList = this.data.borderList.slice();
      let backList = this.data.backList.slice();

      if (editType === 'border') {
        // 清除边框记录，因为编辑后的图片已经包含边框
        borderList[groupIndex] = null;
        console.log('清除边框记录，组索引:', groupIndex);
      } else if (editType === 'cardback') {
        // 清除卡背记录，因为编辑后的图片已经包含卡背
        backList[groupIndex] = null;
        console.log('清除卡背记录，组索引:', groupIndex);
      }

      this.setData({
        imgList, // 只更新显示列表
        groupedImgList,
        previewImages,
        borderList,
        backList,
        serverPathList // 保存服务器路径列表
      });

      console.log('更新后的分组列表:', groupedImgList);
      console.log('服务器路径列表:', serverPathList);

      wx.showToast({
        title: serverPath ? '图片已保存并上传' : (editType === 'border' ? '边框效果已应用' : '卡背效果已应用'),
        icon: 'success'
      });
    } else {
      console.error('图片索引超出范围:', { realIndex, imgListLength: imgList.length });

      // 如果索引超出范围，扩展数组
      let originalImgList = this.data.originalImgList.slice();
      while (imgList.length <= realIndex) {
        imgList.push('');
        originalImgList.push(''); // 同步扩展原始图片列表
      }
      imgList[realIndex] = editedImagePath;
      // 注意：这里不设置originalImgList[realIndex]，因为这是处理后的图片

      const groupedImgList = groupArray(imgList, 2);
      let groupCounts = this.data.groupCounts.slice();
      let borderList = this.data.borderList.slice();
      let backList = this.data.backList.slice();

      // 为新增的组设置数量为1
      while (groupCounts.length < groupedImgList.length) {
        groupCounts.push(1);
        borderList.push(null); // 为新组添加空边框
        backList.push(null);   // 为新组添加空卡背
      }

      // 同时扩展预览图数组
      let previewImages = this.data.previewImages.slice();
      while (previewImages.length <= realIndex) {
        previewImages.push(null);
      }
      previewImages[realIndex] = editedImagePath;

      // 如果是边框编辑，清除对应组的边框记录
      if (editType === 'border') {
        borderList[groupIndex] = null;
        console.log('清除边框记录，组索引:', groupIndex);
      } else if (editType === 'cardback') {
        backList[groupIndex] = null;
        console.log('清除卡背记录，组索引:', groupIndex);
      }

      this.setData({
        imgList,
        originalImgList,
        groupedImgList,
        groupCounts,
        previewImages,
        borderList,
        backList
      });

      console.log('扩展后的图片列表:', imgList);

      wx.showToast({
        title: editType === 'border' ? '边框效果已应用' : '卡背效果已应用',
        icon: 'success'
      });
    }
  },

  // 测试图片编辑器
  testImageEditor() {
    wx.showActionSheet({
      itemList: ['测试边框效果', '测试卡背效果'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 测试边框：先确保有测试图片，然后应用边框
          const testImage = getImageUrl('/miniapp/material/20250802170919_26682bd8.jpeg');
          const testBorder = getImageUrl('/miniapp/material/20250802170903_b8b3ea65.jpg');

          // 确保有图片数据用于测试
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          if (imgList.length === 0) {
            imgList = [testImage, ''];
            originalImgList = [testImage, '']; // 同时设置原始图片列表
            const groupedImgList = groupArray(imgList, 2);
            this.setData({
              imgList,
              originalImgList,
              groupedImgList,
              groupCounts: [1]
            });
          }

          const url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent(testImage)}&backImage=${encodeURIComponent('')}&borderImage=${encodeURIComponent(testBorder)}&editType=border&groupIndex=0&imgIndex=0&currentSide=front`;

          wx.navigateTo({
            url: url
          });
        } else if (res.tapIndex === 1) {
          // 测试卡背：直接使用卡背图片
          const testCardBack = getImageUrl('/miniapp/material/20250802170903_b8b3ea65.jpg');

          // 确保有图片数据用于测试
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          if (imgList.length < 2) {
            imgList = ['', testCardBack];
            originalImgList = ['', testCardBack]; // 同时设置原始图片列表
            const groupedImgList = groupArray(imgList, 2);
            this.setData({
              imgList,
              originalImgList,
              groupedImgList,
              groupCounts: [1]
            });
          }

          const url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent('')}&backImage=${encodeURIComponent(testCardBack)}&cardBackImage=${encodeURIComponent(testCardBack)}&editType=cardback&groupIndex=0&imgIndex=1&currentSide=back`;

          wx.navigateTo({
            url: url
          });
        }
      }
    });
  },

  // 加载图片为Canvas Image对象
  loadCanvasImage(src) {
    return new Promise((resolve, reject) => {
      if (!this.data.previewCanvas) {
        reject(new Error('预览Canvas未初始化'));
        return;
      }

      const img = this.data.previewCanvas.createImage();
      img.onload = () => resolve(img);
      img.onerror = (err) => reject(err);
      img.src = src;
    });
  },

  // 下载网络图片到本地
  downloadImage(url) {
    return new Promise((resolve, reject) => {
      if (!url) {
        reject(new Error('图片URL为空'));
        return;
      }

      // 如果是本地路径或临时文件，直接返回
      if (url.startsWith('/') || url.startsWith('wxfile://')) {
        resolve(url);
        return;
      }

      // 下载网络图片
      wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath);
          } else {
            reject(new Error(`下载失败，状态码：${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  // 生成边框预览图
  async generateBorderPreview(originalImage, borderImage, realIndex) {
    console.log('开始生成边框预览:', { originalImage, borderImage, realIndex });

    const { previewCanvas, previewCtx } = this.data;

    if (!previewCanvas || !previewCtx) {
      console.error('预览Canvas未初始化');
      return;
    }

    try {
      // 下载图片
      const [localOriginal, localBorder] = await Promise.all([
        this.downloadImage(originalImage),
        this.downloadImage(borderImage)
      ]);

      // 加载为Canvas图片对象
      const [originalImg, borderImg] = await Promise.all([
        this.loadCanvasImage(localOriginal),
        this.loadCanvasImage(localBorder)
      ]);

      // 清空画布 - 使用新的尺寸300x400
      previewCtx.fillStyle = '#ffffff';
      previewCtx.fillRect(0, 0, 300, 400);

      // 计算原图在画布中的显示尺寸
      const imageAspectRatio = originalImg.width / originalImg.height;
      const canvasAspectRatio = 300 / 400; // 3:4 比例

      // 图片占画布的75%，为边框留出空间
      const imageScale = 0.75;
      let drawWidth, drawHeight;

      if (imageAspectRatio > canvasAspectRatio) {
        drawWidth = 300 * imageScale;
        drawHeight = drawWidth / imageAspectRatio;
      } else {
        drawHeight = 400 * imageScale;
        drawWidth = drawHeight * imageAspectRatio;
      }

      const drawX = (300 - drawWidth) / 2;
      const drawY = (400 - drawHeight) / 2;

      // 绘制原图
      previewCtx.drawImage(originalImg, drawX, drawY, drawWidth, drawHeight);

      // 直接叠加已镂空的边框图片
      previewCtx.drawImage(borderImg, 0, 0, 300, 400);

      // 将合成结果转为临时文件
      const tempFilePath = await new Promise((resolve, reject) => {
        wx.canvasToTempFilePath({
          canvas: previewCanvas,
          width: 300,
          height: 400,
          success: (res) => resolve(res.tempFilePath),
          fail: (err) => reject(err)
        });
      });

      // 更新预览图
      let previewImages = this.data.previewImages.slice();
      previewImages[realIndex] = tempFilePath;

      this.setData({
        previewImages: previewImages
      });

      console.log('边框预览生成完成:', tempFilePath);

    } catch (error) {
      console.error('边框预览生成失败:', error);

      // 降级处理：直接使用原图
      let previewImages = this.data.previewImages.slice();
      previewImages[realIndex] = originalImage;

      this.setData({
        previewImages: previewImages
      });
    }
  },

  // 生成卡背预览图
  generateCardBackPreview(cardBackImage, realIndex) {
    console.log('开始生成卡背预览:', { cardBackImage, realIndex });

    // 卡背模式直接使用卡背图片作为预览
    let previewImages = this.data.previewImages.slice();
    previewImages[realIndex] = cardBackImage;

    this.setData({
      previewImages: previewImages
    });

    console.log('卡背预览生成完成');
  },

  // 清除预览图
  clearPreview(realIndex) {
    let previewImages = this.data.previewImages.slice();
    previewImages[realIndex] = null;

    this.setData({
      previewImages: previewImages
    });
  },

  // 测试边框预览功能
  testBorderPreview() {
    const testOriginal = getImageUrl('/miniapp/material/20250802170919_26682bd8.jpeg');
    const testBorder = getImageUrl('/miniapp/material/20250802170903_b8b3ea65.jpg');

    this.generateBorderPreview(testOriginal, testBorder, 0);
  }
})