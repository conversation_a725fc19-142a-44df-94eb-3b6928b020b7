// pages/customized/customized.js
function groupArray(arr, groupSize = 2) {
  const result = [];
  for (let i = 0; i < arr.length; i += groupSize) {
    let group = arr.slice(i, i + groupSize);
    while (group.length < groupSize) {
      group.push(null);
    }
    result.push(group);
  }
  return result;
}

const config = require('../../config/setting');
const { getImageUrl } = config;

Page({

  /**
   * 页面的初始数据
   */
  data: {
    popupProduct: {},
    imgList: [], // 所有图片路径（可能包含处理后的图片）
    originalImgList: [], // 用户原始上传的图片路径（永远不变）
    groupedImgList: [], // 每两张为一组
    groupCounts: [], // 每组数量
    borderList: [], // 每组边框
    backList: [], // 每组卡背
    previewImages: [], // 预览图片数组，存储合成后的预览图
    serverPathList: [], // 服务器路径数组，存储上传后的相对路径
    selectedBorder: null, // 从底部按钮选择的边框
    selectedCardBack: null, // 从底部按钮选择的卡背
    editedImage: '', // 编辑后的图片路径
    totalCount: 0,
    showUploadModal: false,
    showBorderModal: false, // 边框选择弹窗
    currentCategory: 'all', // 当前选中的分类
    borderCategories: [], // 边框分类列表
    allBorderImages: [], // 所有边框图片
    currentBorderImages: [], // 当前分类的边框图片
    currentBorderGroup: null, // 当前操作的图片组
    currentBorderImgIdx: null, // 当前操作的图片索引
    uploadGroups: [
      {
        key: 'frontBack',
        img: '/images/upload_placeholder/all.png',
        title: '批量上传（正反面）',
        desc: '按上传顺序上传为正反面'
      },
      {
        key: 'front',
        img: '/images/upload_placeholder/正面.png',
        title: '批量上传（正面）',
        desc: '按上传顺序上传为正面'
      },
      {
        key: 'back',
        img: '/images/upload_placeholder/反面.jpg',
        title: '批量上传（反面）',
        desc: '按上传顺序上传为反面'
      }
    ],
    showBackModal: false,
    currentBackCategory: 'all',
    backCategories: [],
    allBackImages: [],
    currentBackImages: [],
    currentBackGroup: null,
    currentBackImgIdx: null,
    processName: '',
    processPrice: '',
    limit: null,
    productPrice: 0,
    totalPrice: 0,

    // 预览Canvas相关
    previewCanvas: null,
    previewCtx: null,

    // 图片展示框尺寸（根据产品尺寸计算）
    imgCardWidth: 280, // 默认值
    imgWidth: 260,     // 默认值
    imgHeight: 360,    // 默认值
    previewWidth: 260, // 默认值
    previewHeight: 360 // 默认值
  },

  // 根据产品尺寸计算图片展示框大小
  calculateImageCardSize(productSize) {
    // 默认尺寸（rpx）
    let imgWidth = 260;
    let imgHeight = 360;

    if (productSize) {
      // 清理尺寸字符串，移除可能的单位（如mm）
      let cleanSize = productSize.replace(/mm$/i, '').trim();

      // 解析产品尺寸，支持多种格式：
      // "90x125", "90*125", "50x50", "50*50" 等
      let sizeParts = [];
      if (cleanSize.includes('x')) {
        sizeParts = cleanSize.split('x');
      } else if (cleanSize.includes('*')) {
        sizeParts = cleanSize.split('*');
      }

      if (sizeParts.length === 2) {
        const widthMm = parseFloat(sizeParts[0]);
        const heightMm = parseFloat(sizeParts[1]);

        if (widthMm > 0 && heightMm > 0) {
          // 将mm转换为rpx，使用合理的比例
          // 基准：90mm对应260rpx，计算比例
          const mmToRpxRatio = 260 / 90; // 约2.89 rpx/mm

          imgWidth = Math.round(widthMm * mmToRpxRatio);
          imgHeight = Math.round(heightMm * mmToRpxRatio);

          // 设置合理的最小和最大尺寸
          imgWidth = Math.max(200, Math.min(400, imgWidth));
          imgHeight = Math.max(260, Math.min(500, imgHeight));

          console.log('图片展示框尺寸计算:', {
            originalProductSize: productSize,
            cleanSize,
            widthMm,
            heightMm,
            imgWidth,
            imgHeight,
            mmToRpxRatio
          });
        } else {
          console.warn('产品尺寸解析失败，宽度或高度无效:', { widthMm, heightMm });
        }
      } else {
        console.warn('产品尺寸格式不正确，无法解析:', productSize);
      }
    } else {
      console.log('未提供产品尺寸，使用默认图片展示框尺寸');
    }

    // 计算容器宽度（图片宽度 + 边距）
    const imgCardWidth = imgWidth + 20;

    return {
      imgCardWidth,
      imgWidth,
      imgHeight,
      previewWidth: imgWidth,
      previewHeight: imgHeight
    };
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    const processName = options.processName ? decodeURIComponent(options.processName) : '';
    const processPrice = options.processPrice ? decodeURIComponent(options.processPrice) : '';

    const selectedOptions = options.selectedOptions ? decodeURIComponent(options.selectedOptions) : '';
    const category = options.category ? decodeURIComponent(options.category) : 'DIY';

    // 为显示用途创建格式化的价格（除以100）
    const displayPrice = processPrice ? (Number(processPrice) / 100).toFixed(2) : '';

    this.setData({
      processName,
      processPrice, // 原始价格（分），用于计算
      displayPrice, // 显示价格（元），用于界面显示
      category, // 产品类别
      isDIY: category === 'DIY', // 是否为DIY产品
      isREADY: category === 'READY' // 是否为READY产品
    });

    // 新增：获取商品详情
    const productId = options.productId;
    if (productId) {
      this.fetchProductDetail(productId);
    }

    // 如果是READY产品，初始化一个默认的图片组
    if (category === 'READY') {
      this.initReadyProduct();
    }

    this.loadBorderData();
    this.loadBackData();

    // 如果没有产品ID，使用默认尺寸
    if (!productId) {
      const { imgCardWidth, imgWidth, imgHeight, previewWidth, previewHeight } = this.calculateImageCardSize();
      console.log('使用默认图片展示框尺寸:', { imgCardWidth, imgWidth, imgHeight, previewWidth, previewHeight });
      this.setData({
        imgCardWidth,
        imgWidth,
        imgHeight,
        previewWidth,
        previewHeight
      });
    }

    // 初始化预览Canvas
    this.initPreviewCanvas();
  },

  // 初始化预览Canvas
  async initPreviewCanvas() {
    try {
      const query = wx.createSelectorQuery().in(this);
      const canvas = await new Promise((resolve, reject) => {
        query.select('#previewCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            if (res[0]) {
              resolve(res[0].node);
            } else {
              reject(new Error('获取预览画布失败'));
            }
          });
      });

      const ctx = canvas.getContext('2d');

      // 设置画布尺寸 - 增大到300x400以匹配显示尺寸
      const dpr = wx.getSystemInfoSync().pixelRatio;
      canvas.width = 300 * dpr;
      canvas.height = 400 * dpr;
      ctx.scale(dpr, dpr);

      this.setData({
        previewCanvas: canvas,
        previewCtx: ctx
      });

      console.log('预览Canvas初始化成功');
    } catch (error) {
      console.error('预览Canvas初始化失败:', error);
    }
  },

  // 初始化READY产品
  initReadyProduct() {
    // 为READY产品创建一个默认的图片组，用于数量选择
    const defaultImgList = ['', '']; // 两个空位置
    const groupedImgList = groupArray(defaultImgList, 2);
    const groupCounts = [1]; // 默认数量为1

    this.setData({
      imgList: defaultImgList,
      originalImgList: defaultImgList,
      groupedImgList: groupedImgList,
      groupCounts: groupCounts,
      totalCount: 1
    });

    this.calculateTotalPrice();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 打开上传弹窗
  openUploadModal() {
    this.setData({ showUploadModal: true });
  },

  // 关闭上传弹窗
  closeUploadModal() {
    this.setData({ showUploadModal: false });
  },

  // 单张上传
  chooseImage() {
    wx.chooseImage({
      count: 9,
      success: (res) => {
        this._addImages(res.tempFilePaths);
      }
    });
  },

  // 批量上传
  onBatchUpload(e) {
    const key = e && e.currentTarget && e.currentTarget.dataset.key;
    wx.chooseImage({
      count: 9,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: (res) => {
        this.setData({ showUploadModal: false });
        const newImgs = res.tempFilePaths;
        if (key === 'front') {
          // 只放到每组左边（偶数位）
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          let serverPathList = this.data.serverPathList || [];
          let groupCount = Math.max(Math.ceil((imgList.length + newImgs.length) / 2), Math.ceil(imgList.length / 2));
          for (let i = 0; i < newImgs.length; i++) {
            let idx = i * 2;
            if (imgList.length < idx + 1) {
              while (imgList.length < idx) {
                imgList.push('');
                originalImgList.push('');
                serverPathList.push(null);
              }
              imgList.push(newImgs[i]);
              originalImgList.push(newImgs[i]);
              serverPathList.push(null);
            } else {
              imgList[idx] = newImgs[i];
              originalImgList[idx] = newImgs[i];
              serverPathList[idx] = null;
            }
          }
          const groupedImgList = groupArray(imgList, 2);
          let groupCounts = this.data.groupCounts;

          // 每组数量固定初始化为1
          while (groupCounts.length < groupedImgList.length) {
            groupCounts.push(1);
          }

          // 清除被替换图片的预览图和边框记录
          let previewImages = this.data.previewImages.slice();
          let borderList = this.data.borderList.slice();

          for (let i = 0; i < newImgs.length; i++) {
            let idx = i * 2; // 前面图片的索引
            previewImages[idx] = null; // 清除预览图

            // 清除对应组的边框记录
            let groupIdx = Math.floor(idx / 2);
            if (borderList[groupIdx]) {
              borderList[groupIdx] = null;
              console.log('批量上传前面图片，清除边框记录，组索引:', groupIdx);
            }
          }

          this.setData({
            imgList,
            originalImgList,
            groupedImgList,
            groupCounts,
            serverPathList,
            previewImages,
            borderList
          });
        } else if (key === 'back') {
          // 只放到每组右边（奇数位）
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          let serverPathList = this.data.serverPathList || [];
          let groupCount = Math.max(Math.ceil((imgList.length + 1) / 2), Math.ceil(imgList.length / 2));
          for (let i = 0; i < newImgs.length; i++) {
            let idx = i * 2 + 1;
            if (imgList.length < idx + 1) {
              while (imgList.length < idx) {
                imgList.push('');
                originalImgList.push('');
                serverPathList.push(null);
              }
              imgList.push(newImgs[i]);
              originalImgList.push(newImgs[i]);
              serverPathList.push(null);
            } else {
              imgList[idx] = newImgs[i];
              originalImgList[idx] = newImgs[i];
              serverPathList[idx] = null;
            }
          }
          const groupedImgList = groupArray(imgList, 2);
          let groupCounts = this.data.groupCounts;

          // 每组数量固定初始化为1
          while (groupCounts.length < groupedImgList.length) {
            groupCounts.push(1);
          }

          // 清除被替换图片的预览图和卡背记录
          let previewImages = this.data.previewImages.slice();
          let backList = this.data.backList.slice();

          for (let i = 0; i < newImgs.length; i++) {
            let idx = i * 2 + 1; // 后面图片的索引
            previewImages[idx] = null; // 清除预览图

            // 清除对应组的卡背记录
            let groupIdx = Math.floor(idx / 2);
            if (backList[groupIdx]) {
              backList[groupIdx] = null;
              console.log('批量上传后面图片，清除卡背记录，组索引:', groupIdx);
            }
          }

          this.setData({
            imgList,
            originalImgList,
            groupedImgList,
            groupCounts,
            serverPathList,
            previewImages,
            backList
          });
        } else {
          // 其他批量上传逻辑（如正反面）
          this._addImages(newImgs);
        }
      }
    });
  },

  // 添加图片并分组
  _addImages(newImgs) {
    const imgList = this.data.imgList.concat(newImgs);
    const originalImgList = this.data.originalImgList.concat(newImgs); // 同时保存到原始图片列表
    const groupedImgList = groupArray(imgList, 2);
    let groupCounts = this.data.groupCounts;
    let serverPathList = this.data.serverPathList || [];

    // 确保serverPathList与imgList长度一致
    while (serverPathList.length < imgList.length) {
      serverPathList.push(null); // 新上传的图片暂时没有服务器路径
    }

    // 为新增的组设置数量为1
    while (groupCounts.length < groupedImgList.length) {
      groupCounts.push(1);
    }

    this.setData({
      imgList,
      originalImgList, // 保存原始图片列表
      groupedImgList,
      groupCounts,
      serverPathList // 更新服务器路径列表
    });
    this.updateTotalCount();
  },

  // 数量加减
  changeCount(e) {
    const { groupIdx, delta } = e.currentTarget.dataset;
    let groupCounts = this.data.groupCounts;
    let newCount = Number(groupCounts[groupIdx]) + Number(delta);

    // 最小数量固定为1
    if (newCount < 1) newCount = 1;
    groupCounts[groupIdx] = newCount;
    this.setData({ groupCounts });
    this.updateTotalCount();
  },

  // 编辑按钮事件 - 跳转到图片编辑器
  onEdit(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);
    const realIndex = groupIdx * 2 + imgIdx;

    const { originalImgList, borderList, backList } = this.data;

    // 获取正面和反面图片
    const frontIndex = groupIdx * 2;
    const backIndex = groupIdx * 2 + 1;
    const frontImage = originalImgList[frontIndex] || '';
    const backImage = originalImgList[backIndex] || '';

    // 检查是否有图片可以编辑
    if (!frontImage && !backImage) {
      wx.showToast({
        title: '请先上传图片',
        icon: 'none'
      });
      return;
    }

    // 根据图片位置判断编辑类型
    const border = borderList[groupIdx];
    const cardBack = backList[groupIdx];

    // 获取产品尺寸信息
    const productSize = this.data.product ? this.data.product.size : '';

    if (imgIdx === 0) {
      // 左边图片 - 边框模式
      let url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent(frontImage)}&backImage=${encodeURIComponent(backImage)}&editType=border&groupIndex=${groupIdx}&imgIndex=${imgIdx}&currentSide=front`;

      // 传递产品尺寸信息
      if (productSize) {
        url += `&productSize=${encodeURIComponent(productSize)}`;
      }

      // 传递边框信息
      if (border) {
        url += `&borderImage=${encodeURIComponent(border.image_url)}`;
      }

      // 同时传递卡背信息，以便切换到反面时使用
      if (cardBack) {
        url += `&cardBackImage=${encodeURIComponent(cardBack.image_url)}`;
      }

      wx.navigateTo({
        url: url
      });
    } else {
      // 右边图片 - 卡背模式
      let url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent(frontImage)}&backImage=${encodeURIComponent(backImage)}&editType=cardback&groupIndex=${groupIdx}&imgIndex=${imgIdx}&currentSide=back`;

      // 传递产品尺寸信息
      if (productSize) {
        url += `&productSize=${encodeURIComponent(productSize)}`;
      }

      // 传递卡背信息
      if (cardBack) {
        url += `&cardBackImage=${encodeURIComponent(cardBack.image_url)}`;
      }

      // 同时传递边框信息，以便切换到正面时使用
      if (border) {
        url += `&borderImage=${encodeURIComponent(border.image_url)}`;
      }

      wx.navigateTo({
        url: url
      });
    }
  },

  onUploadBtn(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album'],
      success: (res) => {
        const filePath = res.tempFilePaths[0];
        let imgList = this.data.imgList.slice();
        let originalImgList = this.data.originalImgList.slice();
        let serverPathList = this.data.serverPathList || [];

        // 确保serverPathList长度足够
        while (serverPathList.length < imgList.length) {
          serverPathList.push(null);
        }

        // 计算在imgList中的真实索引
        const realIdx = groupIdx * 2 + imgIdx;
        imgList[realIdx] = filePath;
        originalImgList[realIdx] = filePath; // 同时更新原始图片列表
        serverPathList[realIdx] = null; // 新上传的图片清空服务器路径

        // 清除对应位置的预览图，因为用户重新上传了图片
        let previewImages = this.data.previewImages.slice();
        previewImages[realIdx] = null;

        // 清除对应组的边框和卡背记录，因为用户重新上传了图片
        let borderList = this.data.borderList.slice();
        let backList = this.data.backList.slice();

        // 确保数组长度足够
        while (borderList.length <= groupIdx) {
          borderList.push(null);
        }
        while (backList.length <= groupIdx) {
          backList.push(null);
        }

        if (imgIdx === 0) {
          // 如果是左边图片（边框位置），清除边框记录
          borderList[groupIdx] = null;
          console.log('重新上传图片，清除边框记录，组索引:', groupIdx);
        } else {
          // 如果是右边图片（卡背位置），清除卡背记录
          backList[groupIdx] = null;
          console.log('重新上传图片，清除卡背记录，组索引:', groupIdx);
        }

        const groupedImgList = groupArray(imgList, 2);
        this.setData({
          imgList,
          originalImgList,
          groupedImgList,
          serverPathList,
          previewImages,
          borderList,
          backList
        });

        console.log('图片重新上传完成:', {
          groupIdx,
          imgIdx,
          realIdx,
          filePath,
          clearedPreview: true,
          clearedBorder: imgIdx === 0,
          clearedCardBack: imgIdx === 1
        });

        wx.showToast({
          title: imgIdx === 0 ? '前面图片已替换' : '后面图片已替换',
          icon: 'success'
        });
      }
    });
  },

  onCopyBtn(e) {
    wx.showToast({ title: '复制', icon: 'none' });
  },

  onBorderBtn(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);

    // 检查是否已经有边框数据，如果没有则加载
    if (this.data.allBorderImages.length === 0) {
      console.log('边框数据为空，重新加载');
      this.loadBorderData();
    }

    this.setData({
      showBorderModal: true,
      currentBorderGroup: groupIdx,
      currentBorderImgIdx: imgIdx,
      currentCategory: 'all',
      currentBorderImages: this.data.allBorderImages // 使用已加载的数据
    });
  },

  onBackBtn(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const imgIdx = Number(e.currentTarget.dataset.imgIdx);

    // 检查是否已经有卡背数据，如果没有则加载
    if (this.data.allBackImages.length === 0) {
      console.log('卡背数据为空，重新加载');
      this.loadBackData();
    }

    this.setData({
      showBackModal: true,
      currentBackGroup: groupIdx,
      currentBackImgIdx: imgIdx,
      currentBackCategory: 'all',
      currentBackImages: this.data.allBackImages // 使用已加载的数据
    });
  },

  onCountInput(e) {
    const groupIdx = e.currentTarget.dataset.groupIdx;
    let value = e.detail.value;
    let groupCounts = this.data.groupCounts;
    groupCounts[groupIdx] = value;
    this.setData({ groupCounts });
    this.updateTotalCount();
  },

  onCountBlur(e) {
    const groupIdx = e.currentTarget.dataset.groupIdx;
    let value = parseInt(e.detail.value, 10);

    // 最小数量固定为1
    if (isNaN(value) || value < 1) value = 1;
    let groupCounts = this.data.groupCounts;
    groupCounts[groupIdx] = value;
    this.setData({ groupCounts });
    this.updateTotalCount();
  },

  onDeleteGroup(e) {
    const groupIdx = Number(e.currentTarget.dataset.groupIdx);
    const groupSize = 2;
    const start = groupIdx * groupSize;
    const end = start + groupSize;
    let imgList = this.data.imgList.slice();
    imgList.splice(start, groupSize);
    const groupedImgList = groupArray(imgList, groupSize);
    let groupCounts = this.data.groupCounts.slice();
    groupCounts.splice(groupIdx, 1);

    // 为新组设置数量为1
    while (groupCounts.length < groupedImgList.length) {
      groupCounts.push(1);
    }
    this.setData({
      imgList,
      groupedImgList,
      groupCounts
    });
    this.updateTotalCount();
  },

  // 去重函数 - 根据id去重
  removeDuplicateBorders(borderArray) {
    const seen = new Set();
    return borderArray.filter(border => {
      if (seen.has(border.id)) {
        console.log('发现重复边框，已过滤:', border.name, border.id);
        return false;
      }
      seen.add(border.id);
      return true;
    });
  },

  // 卡背去重函数 - 根据id去重
  removeDuplicateCardBacks(cardBackArray) {
    const seen = new Set();
    return cardBackArray.filter(cardBack => {
      if (seen.has(cardBack.id)) {
        console.log('发现重复卡背，已过滤:', cardBack.name, cardBack.id);
        return false;
      }
      seen.add(cardBack.id);
      return true;
    });
  },

  // 加载边框数据
  loadBorderData() {
    // 如果已经有边框数据，则不重复加载
    if (this.data.allBorderImages.length > 0) {
      console.log('边框数据已存在，跳过加载');
      return;
    }

    console.log('loadBorderData: 开始加载边框数据');
    // 获取边框分类
    wx.request({
      url: config.rootUrl + '/border-categories',
      method: 'GET',
      success: (res) => {
        console.log('边框分类API响应:', res);
        if (res.data.code === 200) {
          // 分类category_id转字符串
          const categories = res.data.data.map(cat => ({
            ...cat,
            id: String(cat.category_id)
          }));
          console.log('处理后的边框分类:', categories);
          this.setData({
            borderCategories: categories
          });
        } else {
          console.error('边框分类API返回错误:', res.data);
          // 使用模拟数据
          this.setData({
            borderCategories: [
              { id: '5', name: '每日更新' },
              { id: '7', name: '夏日清凉' },
              { id: '9', name: '国色天香' }
            ]
          });
        }
      },
      fail: (err) => {
        console.error('获取边框分类失败:', err);
        // 使用模拟数据
        this.setData({
          borderCategories: [
            { id: '5', name: '每日更新' },
            { id: '7', name: '夏日清凉' },
            { id: '9', name: '国色天香' }
          ]
        });
      }
    });

    // 获取所有边框素材
    wx.request({
      url: config.rootUrl + '/borders',
      method: 'GET',
      success: (res) => {
        console.log('边框素材API响应:', res);
        if (res.data.code === 200 || res.data.success === true) {
          // 图片category_id转字符串，并处理图片URL
          const images = res.data.data.map(img => ({
            ...img,
            category_id: String(img.category_id),
            image_url: getImageUrl(img.image_url),
            thumbnail_url: getImageUrl(img.thumbnail_url)
          }));
          console.log('处理后的边框素材:', images);
          // 去重处理
          const uniqueImages = this.removeDuplicateBorders(images);
          console.log('去重后的边框素材:', uniqueImages);
          this.setData({
            allBorderImages: uniqueImages,
            currentBorderImages: uniqueImages
          });
        } else {
          console.error('边框素材API返回错误:', res.data);
          // 使用模拟数据
          this.setData({
            allBorderImages: [
              { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
              { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
              { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
              { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
              { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
            ],
            currentBorderImages: [
              { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
              { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
              { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
              { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
              { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
            ]
          });
        }
      },
      fail: (err) => {
        console.error('获取边框图片失败:', err);
        // 使用模拟数据
        const mockBorderImages = [
          { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
          { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
          { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
          { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
          { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
        ].map(item => ({
          ...item,
          image_url: getImageUrl(item.image_url)
        }));

        // 去重处理
        const uniqueMockImages = this.removeDuplicateBorders(mockBorderImages);
        console.log('去重后的模拟边框素材:', uniqueMockImages);
        this.setData({
          allBorderImages: uniqueMockImages,
          currentBorderImages: uniqueMockImages
        });
      }
    });
  },

  // 关闭边框选择弹窗
  closeBorderModal() {
    this.setData({
      showBorderModal: false,
      currentBorderGroup: null,
      currentBorderImgIdx: null
    });
  },

  // 切换分类
  switchCategory(e) {
    const category = e.currentTarget.dataset.category;
    let currentBorderImages = [];
    if (category === 'all') {
      currentBorderImages = this.data.allBorderImages;
    } else {
      currentBorderImages = this.data.allBorderImages.filter(item =>
        item.category_id === category
      );
    }

    // 确保去重
    const uniqueImages = this.removeDuplicateBorders(currentBorderImages);
    console.log(`切换到分类 ${category}，去重前: ${currentBorderImages.length}，去重后: ${uniqueImages.length}`);

    this.setData({
      currentCategory: category,
      currentBorderImages: uniqueImages
    });
  },

  // 选择边框
  selectBorder(e) {
    const border = e.currentTarget.dataset.border;
    const { currentBorderGroup, currentBorderImgIdx, originalImgList } = this.data;

    if (currentBorderGroup !== null) {
      // 记录边框到对应分组
      let borderList = this.data.borderList.slice();
      borderList[currentBorderGroup] = border;
      this.setData({ borderList });

      // 获取用户原始上传的图片（而不是处理后的图片）
      const imgIndex = currentBorderImgIdx !== null ? currentBorderImgIdx : 0;
      const realIndex = currentBorderGroup * 2 + imgIndex;
      const originalImage = originalImgList[realIndex];

      console.log('选择边框 - 生成预览:', {
        currentBorderGroup,
        imgIndex,
        realIndex,
        originalImage,
        borderUrl: border.image_url
      });

      if (originalImage) {
        // 生成预览图而不是跳转到编辑页面
        this.generateBorderPreview(originalImage, border.image_url, realIndex);

        wx.showToast({
          title: `已选择边框: ${border.name}`,
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '请先上传图片',
          icon: 'none'
        });
      }
    } else {
      // 从底部按钮点击，暂存选择的边框
      this.setData({
        selectedBorder: border
      });

      wx.showToast({
        title: `已选择边框: ${border.name}`,
        icon: 'success'
      });
    }

    this.closeBorderModal();
  },

  loadBackData() {
    // 如果已经有卡背数据，则不重复加载
    if (this.data.allBackImages.length > 0) {
      console.log('卡背数据已存在，跳过加载');
      return;
    }

    console.log('loadBackData: 开始加载卡背数据');
    // 获取卡背分类
    wx.request({
      url: config.rootUrl + '/card-back-categories',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200) {
          const categories = res.data.data.map(cat => ({
            ...cat,
            id: String(cat.category_id)
          }));
          this.setData({
            backCategories: categories
          });
        }
      },
      fail: (err) => {
        console.error('获取卡背分类失败:', err);
      }
    });

    // 获取所有卡背素材
    wx.request({
      url: config.rootUrl + '/card-backs',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200) {
          const images = res.data.data.map(img => ({
            ...img,
            category_id: String(img.category_id),
            image_url: getImageUrl(img.image_url),
            thumbnail_url: getImageUrl(img.thumbnail_url)
          }));
          console.log('处理后的卡背素材:', images);
          // 去重处理
          const uniqueImages = this.removeDuplicateCardBacks(images);
          console.log('去重后的卡背素材:', uniqueImages);
          this.setData({
            allBackImages: uniqueImages,
            currentBackImages: uniqueImages
          });
        }
      },
      fail: (err) => {
        console.error('获取卡背图片失败:', err);
      }
    });
  },

  closeBackModal() {
    this.setData({
      showBackModal: false,
      currentBackGroup: null,
      currentBackImgIdx: null
    });
  },

  switchBackCategory(e) {
    const category = e.currentTarget.dataset.category;
    let currentBackImages = [];
    if (category === 'all') {
      currentBackImages = this.data.allBackImages;
    } else {
      currentBackImages = this.data.allBackImages.filter(item =>
        item.category_id === category
      );
    }

    // 确保去重
    const uniqueImages = this.removeDuplicateCardBacks(currentBackImages);
    console.log(`切换到卡背分类 ${category}，去重前: ${currentBackImages.length}，去重后: ${uniqueImages.length}`);

    this.setData({
      currentBackCategory: category,
      currentBackImages: uniqueImages
    });
  },

  // 选择卡背
  selectBack(e) {
    const back = e.currentTarget.dataset.back;
    const { currentBackGroup } = this.data;

    if (currentBackGroup !== null) {
      let backList = this.data.backList.slice();
      backList[currentBackGroup] = back;
      this.setData({ backList });

      // 生成卡背预览图
      const imgIndex = this.data.currentBackImgIdx || 1;
      const realIndex = currentBackGroup * 2 + imgIndex;

      console.log('选择卡背 - 生成预览:', {
        currentBackGroup,
        imgIndex,
        realIndex,
        cardBackUrl: back.image_url
      });

      this.generateCardBackPreview(back.image_url, realIndex);

      wx.showToast({
        title: `已选择卡背: ${back.name}`,
        icon: 'success'
      });
    } else {
      // 从底部按钮点击，暂存选择的卡背
      this.setData({
        selectedCardBack: back
      });

      wx.showToast({
        title: `已选择卡背: ${back.name}`,
        icon: 'success'
      });
    }

    this.closeBackModal();
  },

  onBackTap() {
    wx.navigateBack();
  },

  // 新增方法
  fetchProductDetail(productId) {
    wx.request({
      url: `${config.rootUrl}/product/${productId}`,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          const product = res.data.data;

          // 基础价格优先使用售价，其次标价，转换为元
          const basePrice = (Number(product.price_sale) || Number(product.price_tag) || 0) / 100;

          // 根据产品尺寸计算图片展示框大小
          const { imgCardWidth, imgWidth, imgHeight, previewWidth, previewHeight } = this.calculateImageCardSize(product.size);

          this.setData({
            product,
            limit: product.limit,
            productPrice: basePrice,
            imgCardWidth,
            imgWidth,
            imgHeight,
            previewWidth,
            previewHeight
          });
          this.updateTotalPrice();
        }
      },
      fail: () => {
        wx.showToast({ title: '获取商品信息失败', icon: 'none' });
      }
    });
  },

  updateTotalCount() {
    const total = this.data.groupCounts.reduce((sum, n) => sum + Number(n), 0);
    this.setData({ totalCount: total }, () => {
      this.updateTotalPrice();
    });
  },

  updateTotalPrice() {
    // 计算总价格 = (产品单价 + 工艺单价) * 总数量
    const productPrice = Number(this.data.productPrice) || 0; // 产品单价（元）
    const processPrice = Number(this.data.processPrice) / 100 || 0; // 工艺单价（转换为元）
    const totalCount = Number(this.data.totalCount) || 0;

    const totalPrice = ((productPrice + processPrice) * totalCount).toFixed(2);
    this.setData({ totalPrice });
  },

  onAddToCart() {
    const that = this;
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';
    if (!mobile) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }
    const { product, processName, processPrice, limit, title, size } = that.data;
    // 组装每组卡片明细
    const cartItems = that.data.groupedImgList.map((group, idx) => {
      const serverPathList = that.data.serverPathList || [];
      const realIndex = idx * 2; // 计算在图片列表中的真实索引

      return {
        quantity: that.data.groupCounts[idx],
        // 优先使用服务器路径，如果没有则使用本地路径
        front_image: serverPathList[realIndex] || group[0] || '',
        back_image: serverPathList[realIndex + 1] || group[1] || '',
        border: that.data.borderList[idx] ? that.data.borderList[idx].name : '',
        card_back: that.data.backList[idx] ? that.data.backList[idx].name : '',
      };
    });
    if (!cartItems.length) {
      wx.showToast({ title: '请先选择图片', icon: 'none' });
      return;
    }

    // 调试日志：检查图片路径
    console.log('🛒 加入购物车 - 图片路径信息:');
    console.log('serverPathList:', that.data.serverPathList);
    console.log('groupedImgList:', that.data.groupedImgList);
    console.log('构建的cartItems:', cartItems);

    // 详细显示每组的图片路径信息
    cartItems.forEach((item, index) => {
      console.log(`第${index + 1}组:`, {
        数量: item.quantity,
        正面图片: item.front_image,
        反面图片: item.back_image,
        边框: item.border,
        卡背: item.card_back,
        是否使用服务器路径: {
          正面: item.front_image.startsWith('/miniapp/'),
          反面: item.back_image.startsWith('/miniapp/')
        }
      });
    });
    // 处理 limit 字段，保证为数字
    let limitValue = limit;
    if (typeof limit === 'string') {
      const match = limit.match(/\d+/);
      limitValue = match ? parseInt(match[0]) : 0;
    }
    // 获取商品主图
    let img = product && product.img ? product.img : '';
    // 发送请求
    wx.request({
      url: config.rootUrl + '/shoppingcar/add',
      method: 'POST',
      header: { 'content-type': 'application/json' },
      data: {
        mobile,
        product_id: product ? product.id : '',
        title: product ? product.title : title,
        size: product ? product.size : size,
        limit: limitValue,
        process_name: processName,
        process_price: processPrice,
        img: img, // 新增主图字段
        items: cartItems
      },
      success(res) {
        if (res.data && res.data.success) {
          wx.showToast({
            title: '已加入购物车',
            icon: 'success',
            duration: 800,
            success() {
              setTimeout(() => {
                wx.switchTab({ url: '/pages/shopping/shopping' });
              }, 800);
            }
          });
        } else {
          wx.showToast({ title: '加入购物车失败', icon: 'none' });
        }
      },
      fail() {
        wx.showToast({ title: '网络错误', icon: 'none' });
      }
    });
  },

  // 立即定制功能已取消，现在只能通过购物车下单
  onImmediateCustomize() {
    wx.showToast({
      title: '请先加入购物车，然后在购物车中下单',
      icon: 'none',
      duration: 2000
    });
  },

  // 底部边框模板按钮点击事件
  onBottomBorderTemplate() {
    console.log('点击边框模板按钮');

    // 检查是否已经有边框数据，如果没有则加载
    if (this.data.allBorderImages.length === 0) {
      console.log('边框数据为空，重新加载');
      this.loadBorderData();
    }

    // 直接显示弹窗，使用已加载的数据
    this.setData({
      showBorderModal: true,
      currentBorderGroup: null,
      currentBorderImgIdx: null,
      currentCategory: 'all',
      currentBorderImages: this.data.allBorderImages // 使用已加载的数据
    });

    console.log('边框弹窗已显示，当前边框数量:', this.data.allBorderImages.length);
  },

  // 底部卡背模板按钮点击事件
  onBottomCardBackTemplate() {
    console.log('点击卡背模板按钮');

    // 检查是否已经有卡背数据，如果没有则加载
    if (this.data.allBackImages.length === 0) {
      console.log('卡背数据为空，从API加载');
      this.loadBackData();
    }

    // 直接显示弹窗，使用已加载的数据
    this.setData({
      showBackModal: true,
      currentBackGroup: null,
      currentBackImgIdx: null,
      currentBackCategory: 'all',
      currentBackImages: this.data.allBackImages // 使用已加载的数据
    });

    console.log('卡背弹窗已显示，当前卡背数量:', this.data.allBackImages.length);
  },

  // 处理编辑后的图片
  updateEditedImage(editedImagePath, groupIndex, imgIndex, editType, serverPath = null) {
    console.log('更新编辑后的图片:', {
      editedImagePath,
      groupIndex,
      imgIndex,
      editType,
      serverPath
    });
    console.log('当前图片列表:', this.data.imgList);
    console.log('原始图片列表:', this.data.originalImgList);

    let imgList = this.data.imgList.slice();
    let serverPathList = this.data.serverPathList || [];

    // 确保服务器路径列表长度与图片列表一致
    while (serverPathList.length < imgList.length) {
      serverPathList.push(null);
    }

    // 注意：不修改originalImgList，保持用户原始图片不变

    // 计算在imgList中的真实索引
    const realIndex = groupIndex * 2 + imgIndex;

    console.log('计算的真实索引:', realIndex, '图片列表长度:', imgList.length);

    if (realIndex < imgList.length) {
      // 更新对应位置的图片（只更新显示用的imgList）
      const oldImage = imgList[realIndex];
      const oldServerPath = serverPathList[realIndex];
      const originalImage = this.data.originalImgList[realIndex];

      imgList[realIndex] = editedImagePath;
      serverPathList[realIndex] = serverPath;

      console.log('图片更新:', {
        position: `第${groupIndex + 1}组第${imgIndex + 1}张`,
        originalImage, // 原始图片保持不变
        oldImage,
        newImage: editedImagePath,
        oldServerPath,
        newServerPath: serverPath
      });

      // 重新分组
      const groupedImgList = groupArray(imgList, 2);

      // 同时更新预览图
      let previewImages = this.data.previewImages.slice();
      previewImages[realIndex] = editedImagePath;

      // 如果是边框编辑，清除对应组的边框记录（因为图片已经包含边框效果）
      let borderList = this.data.borderList.slice();
      let backList = this.data.backList.slice();

      if (editType === 'border') {
        // 清除边框记录，因为编辑后的图片已经包含边框
        borderList[groupIndex] = null;
        console.log('清除边框记录，组索引:', groupIndex);
      } else if (editType === 'cardback') {
        // 清除卡背记录，因为编辑后的图片已经包含卡背
        backList[groupIndex] = null;
        console.log('清除卡背记录，组索引:', groupIndex);
      }

      this.setData({
        imgList, // 只更新显示列表
        groupedImgList,
        previewImages,
        borderList,
        backList,
        serverPathList // 保存服务器路径列表
      });

      console.log('更新后的分组列表:', groupedImgList);
      console.log('服务器路径列表:', serverPathList);

      wx.showToast({
        title: serverPath ? '图片已保存并上传' : (editType === 'border' ? '边框效果已应用' : '卡背效果已应用'),
        icon: 'success'
      });
    } else {
      console.error('图片索引超出范围:', { realIndex, imgListLength: imgList.length });

      // 如果索引超出范围，扩展数组
      let originalImgList = this.data.originalImgList.slice();
      while (imgList.length <= realIndex) {
        imgList.push('');
        originalImgList.push(''); // 同步扩展原始图片列表
      }
      imgList[realIndex] = editedImagePath;
      // 注意：这里不设置originalImgList[realIndex]，因为这是处理后的图片

      const groupedImgList = groupArray(imgList, 2);
      let groupCounts = this.data.groupCounts.slice();
      let borderList = this.data.borderList.slice();
      let backList = this.data.backList.slice();

      // 为新增的组设置数量为1
      while (groupCounts.length < groupedImgList.length) {
        groupCounts.push(1);
        borderList.push(null); // 为新组添加空边框
        backList.push(null);   // 为新组添加空卡背
      }

      // 同时扩展预览图数组
      let previewImages = this.data.previewImages.slice();
      while (previewImages.length <= realIndex) {
        previewImages.push(null);
      }
      previewImages[realIndex] = editedImagePath;

      // 如果是边框编辑，清除对应组的边框记录
      if (editType === 'border') {
        borderList[groupIndex] = null;
        console.log('清除边框记录，组索引:', groupIndex);
      } else if (editType === 'cardback') {
        backList[groupIndex] = null;
        console.log('清除卡背记录，组索引:', groupIndex);
      }

      this.setData({
        imgList,
        originalImgList,
        groupedImgList,
        groupCounts,
        previewImages,
        borderList,
        backList
      });

      console.log('扩展后的图片列表:', imgList);

      wx.showToast({
        title: editType === 'border' ? '边框效果已应用' : '卡背效果已应用',
        icon: 'success'
      });
    }
  },

  // 测试图片编辑器
  testImageEditor() {
    wx.showActionSheet({
      itemList: ['测试边框效果', '测试卡背效果'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 测试边框：先确保有测试图片，然后应用边框
          const testImage = getImageUrl('/miniapp/material/20250802170919_26682bd8.jpeg');
          const testBorder = getImageUrl('/miniapp/material/20250802170903_b8b3ea65.jpg');

          // 确保有图片数据用于测试
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          if (imgList.length === 0) {
            imgList = [testImage, ''];
            originalImgList = [testImage, '']; // 同时设置原始图片列表
            const groupedImgList = groupArray(imgList, 2);
            this.setData({
              imgList,
              originalImgList,
              groupedImgList,
              groupCounts: [1]
            });
          }

          const url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent(testImage)}&backImage=${encodeURIComponent('')}&borderImage=${encodeURIComponent(testBorder)}&editType=border&groupIndex=0&imgIndex=0&currentSide=front`;

          wx.navigateTo({
            url: url
          });
        } else if (res.tapIndex === 1) {
          // 测试卡背：直接使用卡背图片
          const testCardBack = getImageUrl('/miniapp/material/20250802170903_b8b3ea65.jpg');

          // 确保有图片数据用于测试
          let imgList = this.data.imgList.slice();
          let originalImgList = this.data.originalImgList.slice();
          if (imgList.length < 2) {
            imgList = ['', testCardBack];
            originalImgList = ['', testCardBack]; // 同时设置原始图片列表
            const groupedImgList = groupArray(imgList, 2);
            this.setData({
              imgList,
              originalImgList,
              groupedImgList,
              groupCounts: [1]
            });
          }

          const url = `/pages/image-editor/image-editor?frontImage=${encodeURIComponent('')}&backImage=${encodeURIComponent(testCardBack)}&cardBackImage=${encodeURIComponent(testCardBack)}&editType=cardback&groupIndex=0&imgIndex=1&currentSide=back`;

          wx.navigateTo({
            url: url
          });
        }
      }
    });
  },

  // 加载图片为Canvas Image对象
  loadCanvasImage(src) {
    return new Promise((resolve, reject) => {
      if (!this.data.previewCanvas) {
        reject(new Error('预览Canvas未初始化'));
        return;
      }

      const img = this.data.previewCanvas.createImage();
      img.onload = () => resolve(img);
      img.onerror = (err) => reject(err);
      img.src = src;
    });
  },

  // 下载网络图片到本地
  downloadImage(url) {
    return new Promise((resolve, reject) => {
      if (!url) {
        reject(new Error('图片URL为空'));
        return;
      }

      // 如果是本地路径或临时文件，直接返回
      if (url.startsWith('/') || url.startsWith('wxfile://')) {
        resolve(url);
        return;
      }

      // 下载网络图片
      wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath);
          } else {
            reject(new Error(`下载失败，状态码：${res.statusCode}`));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  // 生成边框预览图
  async generateBorderPreview(originalImage, borderImage, realIndex) {
    console.log('开始生成边框预览:', { originalImage, borderImage, realIndex });

    const { previewCanvas, previewCtx } = this.data;

    if (!previewCanvas || !previewCtx) {
      console.error('预览Canvas未初始化');
      return;
    }

    try {
      // 下载图片
      const [localOriginal, localBorder] = await Promise.all([
        this.downloadImage(originalImage),
        this.downloadImage(borderImage)
      ]);

      // 加载为Canvas图片对象
      const [originalImg, borderImg] = await Promise.all([
        this.loadCanvasImage(localOriginal),
        this.loadCanvasImage(localBorder)
      ]);

      // 清空画布 - 使用新的尺寸300x400
      previewCtx.fillStyle = '#ffffff';
      previewCtx.fillRect(0, 0, 300, 400);

      // 计算原图在画布中的显示尺寸
      const imageAspectRatio = originalImg.width / originalImg.height;
      const canvasAspectRatio = 300 / 400; // 3:4 比例

      // 图片填充整个画布，不缩小
      let drawWidth, drawHeight;

      if (imageAspectRatio > canvasAspectRatio) {
        // 图片更宽，以高度为准填充
        drawHeight = 400;
        drawWidth = drawHeight * imageAspectRatio;
      } else {
        // 图片更高，以宽度为准填充
        drawWidth = 300;
        drawHeight = drawWidth / imageAspectRatio;
      }

      const drawX = (300 - drawWidth) / 2;
      const drawY = (400 - drawHeight) / 2;

      console.log('边框预览 - 图片不缩小，边框自动缩放:', {
        drawSize: `${drawWidth}x${drawHeight}`,
        drawPosition: `${drawX},${drawY}`
      });

      // 绘制原图，填充整个画布
      previewCtx.drawImage(originalImg, drawX, drawY, drawWidth, drawHeight);

      // 边框自动缩放覆盖整个画布
      previewCtx.drawImage(borderImg, 0, 0, 300, 400);

      // 将合成结果转为临时文件
      const tempFilePath = await new Promise((resolve, reject) => {
        wx.canvasToTempFilePath({
          canvas: previewCanvas,
          width: 300,
          height: 400,
          success: (res) => resolve(res.tempFilePath),
          fail: (err) => reject(err)
        });
      });

      // 更新预览图
      let previewImages = this.data.previewImages.slice();
      previewImages[realIndex] = tempFilePath;

      this.setData({
        previewImages: previewImages
      });

      console.log('边框预览生成完成:', tempFilePath);

    } catch (error) {
      console.error('边框预览生成失败:', error);

      // 降级处理：直接使用原图
      let previewImages = this.data.previewImages.slice();
      previewImages[realIndex] = originalImage;

      this.setData({
        previewImages: previewImages
      });
    }
  },

  // 生成卡背预览图
  generateCardBackPreview(cardBackImage, realIndex) {
    console.log('开始生成卡背预览:', { cardBackImage, realIndex });

    // 卡背模式直接使用卡背图片作为预览
    let previewImages = this.data.previewImages.slice();
    previewImages[realIndex] = cardBackImage;

    this.setData({
      previewImages: previewImages
    });

    console.log('卡背预览生成完成');
  },

  // 清除预览图
  clearPreview(realIndex) {
    let previewImages = this.data.previewImages.slice();
    previewImages[realIndex] = null;

    this.setData({
      previewImages: previewImages
    });
  },

  // 测试边框预览功能
  testBorderPreview() {
    const testOriginal = getImageUrl('/miniapp/material/20250802170919_26682bd8.jpeg');
    const testBorder = getImageUrl('/miniapp/material/20250802170903_b8b3ea65.jpg');

    this.generateBorderPreview(testOriginal, testBorder, 0);
  }
})