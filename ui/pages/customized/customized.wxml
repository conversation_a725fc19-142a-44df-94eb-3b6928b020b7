<!--pages/customized/customized.wxml-->
<!-- 统一的导航栏 -->
<navigation-bar title="{{isDIY ? '咔咔DIY' : '产品定制'}}" back="{{true}}" color="black" background="#FFF"></navigation-bar>

<!-- DIY产品的提示信息 -->
<view wx:if="{{isDIY}}">
  <view class="tip-bar">
    温馨提示：红色透明胶边为出血位（印刷后会进行裁剪）
  </view>

  <view class="tips">
    <text>温馨提示：{{processName}}{{displayPrice ? (' ￥' + displayPrice) : ''}}</text>
  </view>

  <!-- 只在没有图片时显示上传图片框 -->
  <view class="upload-area" wx:if="{{imgList.length === 0}}">

    <view class="upload-text" bindtap="openUploadModal">【上传图片】</view>
  </view>
</view>

<!-- READY产品的提示信息 -->
<view wx:if="{{isREADY}}">
  <view class="tip-bar">
    现货产品，选择数量即可下单
  </view>

  <view class="tips">
    <text>{{processName}}{{displayPrice ? (' ￥' + displayPrice) : ''}}</text>
  </view>
</view>

<!-- DIY产品：图片分组回显，每两张为一组 -->
<view class="group-list" wx:if="{{isDIY}}">
  <block wx:for="{{groupedImgList}}" wx:key="index">
    <view class="card-group">
      <view class="card-close" bindtap="onDeleteGroup" data-group-idx="{{index}}">×</view>
      <view class="group-title">第{{index + 1}}张</view>
      <view class="img-row">
        <block wx:for="{{item}}" wx:key="imgIdx" wx:for-index="imgIdx">
          <view class="img-card" style="width: {{imgCardWidth}}rpx;">
            <block wx:if="{{item}}">
              <!-- 预览区域：显示合成后的效果 -->
              <view class="preview-container" style="width: {{previewWidth}}rpx; height: {{previewHeight}}rpx;">
                <!-- 如果有边框或卡背预览，显示预览图 -->
                <block wx:if="{{previewImages[index * 2 + imgIdx]}}">
                  <image src="{{previewImages[index * 2 + imgIdx]}}" class="img preview-img" style="width: {{imgWidth}}rpx; height: {{imgHeight}}rpx;"/>

                </block>
                <!-- 否则显示原图 -->
                <block wx:else>
                  <image src="{{item}}" class="img" style="width: {{imgWidth}}rpx; height: {{imgHeight}}rpx;"/>
                </block>
              </view>



              <view class="edit-btn" bindtap="onEdit" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">点击编辑</view>
              <view class="btn-row">
                <button class="mini-btn" bindtap="onUploadBtn" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">上传</button>
                <button class="mini-btn" wx:if="{{imgIdx == 0}}" bindtap="onBorderBtn" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">边框</button>
                <button class="mini-btn" wx:if="{{imgIdx == 1}}" bindtap="onBackBtn" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">卡背</button>
              </view>
            </block>
            <block wx:else>
              <view class="img-placeholder" bindtap="chooseImage" style="width: {{imgWidth}}rpx; height: {{imgHeight}}rpx;">
                <text>+</text>
              </view>
              <view class="edit-btn" bindtap="onEdit" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">点击编辑</view>
              <view class="btn-row">
                <button class="mini-btn" bindtap="onUploadBtn" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">上传</button>
                <button class="mini-btn" wx:if="{{imgIdx == 0}}" bindtap="onBorderBtn" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">边框</button>
                <button class="mini-btn" wx:if="{{imgIdx == 1}}" bindtap="onBackBtn" data-group-idx="{{index}}" data-img-idx="{{imgIdx}}">卡背</button>
              </view>
            </block>
          </view>
        </block>
      </view>
      <view class="count-row">
        <button bindtap="changeCount" data-group-idx="{{index}}" data-delta="-1">-</button>
        <input
          class="count-input"
          type="number"
          value="{{groupCounts[index]}}"
          data-group-idx="{{index}}"
          min="1"
          bindinput="onCountInput"
          bindblur="onCountBlur"
        />
        <button bindtap="changeCount" data-group-idx="{{index}}" data-delta="1">+</button>
      </view>
    </view>
  </block>
</view>

<!-- READY产品：简化的数量选择 -->
<view class="ready-product-section" wx:if="{{isREADY}}">
  <view class="ready-product-card">
    <view class="ready-product-info">
      <view class="ready-product-title">{{product.title || '现货产品'}}</view>
      <view class="ready-product-desc">{{product.detail || '选择数量即可下单'}}</view>
    </view>
    <view class="ready-count-row">
      <text class="count-label">数量：</text>
      <button class="count-btn" bindtap="changeCount" data-group-idx="0" data-delta="-1">-</button>
      <input
        class="ready-count-input"
        type="number"
        value="{{groupCounts[0] || 1}}"
        data-group-idx="0"
        min="1"
        bindinput="onCountInput"
        bindblur="onCountBlur"
      />
      <button class="count-btn" bindtap="changeCount" data-group-idx="0" data-delta="1">+</button>
    </view>
  </view>
</view>

<!-- DIY产品：底部操作按钮栏 -->
<view class="bottom-actions-bar" wx:if="{{isDIY}}">
  <button class="bottom-btn" bindtap="openUploadModal">上传图片</button>
  <button class="bottom-btn" bindtap="onBottomBorderTemplate">边框模板</button>
  <button class="bottom-btn" bindtap="onBottomCardBackTemplate">卡背模板</button>
  <button class="bottom-btn" bindtap="testImageEditor">测试编辑</button>
</view>

<!-- 底部结算栏 -->
<view class="bottom-summary-bar">
  <view class="summary-left">
    <text class="price">￥{{totalPrice}}</text>
    <text class="count">已选 {{totalCount}} 张</text>
  </view>
  <view class="summary-right">
    <button class="cart-btn" bind:tap="onAddToCart">加购物车</button>
    <button class="order-btn">{{isDIY ? '立即定制' : '立即下单'}}</button>
  </view>
</view>

<!-- 批量上传弹窗遮罩 -->
<view class="modal-mask" wx:if="{{showUploadModal}}" bindtap="closeUploadModal"></view>
<!-- 批量上传弹窗 -->
<view class="upload-modal" wx:if="{{showUploadModal}}">
  <view class="modal-close" bindtap="closeUploadModal">×</view>
  <block wx:for="{{uploadGroups}}" wx:key="index">
    <view class="upload-group">
      <image class="preview-img" src="{{item.img}}" mode="aspectFill"/>
      <view class="upload-info">
        <view class="upload-title">{{item.title}}</view>
        <view class="upload-desc">{{item.desc}}</view>
        <button class="upload-btn" bindtap="onBatchUpload" data-key="{{item.key}}">立即上传</button>
      </view>
    </view>
  </block>
  <view class="upload-tip">一次最多多选9张</view>
</view>

<!-- 边框选择弹窗遮罩 -->
<view class="modal-mask" wx:if="{{showBorderModal}}" bindtap="closeBorderModal"></view>
<!-- 边框选择弹窗 -->
<view class="border-modal" wx:if="{{showBorderModal}}">
  <view class="modal-close" bindtap="closeBorderModal">×</view>
  <view class="border-title">选择边框</view>
  
  <!-- 分类栏 -->
  <view class="category-tabs">
    <view 
      class="category-tab {{currentCategory === 'all' ? 'active' : ''}}" 
      bindtap="switchCategory" 
      data-category="all"
    >
      全部分类
    </view>
    <view 
      wx:for="{{borderCategories}}" 
      wx:key="id"
      class="category-tab {{currentCategory === item.id ? 'active' : ''}}" 
      bindtap="switchCategory" 
      data-category="{{item.id}}"
    >
      {{item.name}}
    </view>
  </view>
  
  <!-- 边框图片列表 -->
  <view class="border-images">
    <view 
      wx:for="{{currentBorderImages}}" 
      wx:key="id"
      class="border-image-item"
      bindtap="selectBorder"
      data-border="{{item}}"
    >
      <image src="{{item.image_url}}" class="border-img" mode="aspectFill"/>
      <view class="border-name">{{item.name}}</view>
    </view>
  </view>
</view>

<!-- 卡背选择弹窗遮罩 -->
<view class="modal-mask" wx:if="{{showBackModal}}" bindtap="closeBackModal"></view>
<!-- 卡背选择弹窗 -->
<view class="border-modal" wx:if="{{showBackModal}}">
  <view class="modal-close" bindtap="closeBackModal">×</view>
  <view class="border-title">选择卡背</view>
  
  <!-- 分类栏 -->
  <view class="category-tabs">
    <view 
      class="category-tab {{currentBackCategory === 'all' ? 'active' : ''}}" 
      bindtap="switchBackCategory" 
      data-category="all"
    >
      全部分类
    </view>
    <view 
      wx:for="{{backCategories}}" 
      wx:key="id"
      class="category-tab {{currentBackCategory === item.id ? 'active' : ''}}" 
      bindtap="switchBackCategory" 
      data-category="{{item.id}}"
    >
      {{item.name}}
    </view>
  </view>
  
  <!-- 卡背图片列表 -->
  <view class="border-images">
    <view 
      wx:for="{{currentBackImages}}" 
      wx:key="id"
      class="border-image-item"
      bindtap="selectBack"
      data-back="{{item}}"
    >
      <image src="{{item.image_url}}" class="border-img" mode="aspectFill"/>
      <view class="border-name">{{item.name}}</view>
    </view>
  </view>
</view>

<!-- 隐藏的预览合成画布 -->
<canvas
  type="2d"
  id="previewCanvas"
  style="position: absolute; left: -9999px; top: -9999px; width: 300px; height: 400px;">
</canvas>