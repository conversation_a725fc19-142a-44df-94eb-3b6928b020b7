/* pages/customized/customized.wxss */
.iconfont {
  font-size: 36rpx;
  color: #fff;
  margin: 0 8rpx;
}

/* 温馨提示 */
.tip-bar {
  background: #f5f5f5;
  color: #888;
  font-size: 24rpx;
  padding: 12rpx 24rpx;
}

/* 工艺价格提示 */
.tips {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  color: #666;
}

/* 上传图片区域 */
.upload-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  margin-top: 40rpx;
}
.upload-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 16rpx;
  opacity: 0.5;
}
.upload-text {
  color: #888;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.04);
  padding-bottom: env(safe-area-inset-bottom);
}
.bottom-actions {
  display: flex;
  align-items: center;
  padding: 16rpx 0 0 16rpx;
}
.bottom-btn {
  background: #f5f5f5;
  color: #888;
  border: none;
  border-radius: 24rpx;
  font-size: 24rpx;
  margin-right: 16rpx;
  padding: 8rpx 24rpx;
}
.save-btn {
  background: #ffb6c1;
  color: #fff;
  border-radius: 24rpx;
  font-size: 24rpx;
  margin-left: 16rpx;
  padding: 8rpx 24rpx;
}
.bottom-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
}
.summary-left {
  display: flex;
  align-items: center;
}
.price {
  color: #ff4d6b;
  font-size: 32rpx;
  font-weight: bold;
  margin-right: 16rpx;
}
.count, .min-tip {
  color: #888;
  font-size: 24rpx;
  margin-right: 12rpx;
}
.summary-right {
  display: flex;
  align-items: center;
}
.cart-btn {
  background: #fff;
  color: #ffb6c1;
  border: 1rpx solid #ffb6c1;
  border-radius: 32rpx;
  font-size: 28rpx;
  margin-right: 16rpx;
  padding: 8rpx 32rpx;
}
.order-btn {
  background: #ffb6c1;
  color: #fff;
  border-radius: 32rpx;
  font-size: 28rpx;
  padding: 8rpx 32rpx;
}

.main-content {
  /* 预留底部空间，避免内容被遮挡 */
  padding-bottom: 200rpx; /* 约等于两个底部栏高度之和 */
}

/* 操作按钮栏 */
.bottom-actions-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 120rpx; /* 高度等于下方结算栏高度 */
  background: #fff;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12rpx 0;
  z-index: 1001;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.04);
  border-top: 1rpx solid #f0f0f0;
}
.bottom-btn {
  background: #f5f5f5;
  color: #888;
  border: none;
  border-radius: 24rpx;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
}

/* 结算栏 */
.bottom-summary-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  z-index: 1002;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.04);
  border-top: 1rpx solid #f0f0f0;
  white-space: nowrap;
}
.summary-left {
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
}
.price {
  color: #ff4d6b;
  font-size: 28rpx;
  font-weight: bold;
  margin-right: 8rpx;
}
.count, .min-tip {
  color: #888;
  font-size: 20rpx;
  margin-right: 8rpx;
}
.summary-right {
  display: flex;
  align-items: center;
}
.cart-btn {
  background: #fff;
  color: #ffb6c1;
  border: 1rpx solid #ffb6c1;
  border-radius: 32rpx;
  font-size: 28rpx;
  margin-right: 16rpx;
  padding: 8rpx 32rpx;
}
.order-btn {
  background: #ffb6c1;
  color: #fff;
  border-radius: 32rpx;
  font-size: 28rpx;
  padding: 8rpx 32rpx;
}

.modal-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1000;
}

.upload-modal {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 85vw;
  max-width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  z-index: 1001;
  padding: 50rpx 30rpx 30rpx 30rpx;
  box-sizing: border-box;
  box-shadow: 0 6rpx 24rpx rgba(0,0,0,0.12);
}

.modal-close {
  position: absolute;
  right: 20rpx;
  top: 15rpx;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #666;
  z-index: 1002;
  font-weight: bold;
}

.upload-group {
  display: flex;
  align-items: stretch; /* 改为stretch让子元素等高 */
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #fafafa;
  border-radius: 16rpx;
  min-height: 120rpx; /* 设置最小高度 */
}

.upload-preview {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-right: 24rpx;
}
.upload-group .preview-img {
  width: 120rpx;
  height: auto; /* 自动高度，与容器等高 */
  min-height: 80rpx; /* 最小高度 */
  flex-shrink: 0; /* 不缩小 */
  background: #fff;
  border-radius: 12rpx;
  object-fit: cover;
  margin-right: 24rpx;
  border: 1rpx solid #e5e5e5;
  align-self: stretch; /* 拉伸到容器高度 */
}

.upload-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between; /* 改为space-between分布内容 */
  min-height: 80rpx; /* 最小高度 */
  padding: 8rpx 0; /* 添加上下内边距 */
}

.upload-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 6rpx; /* 减小底边距 */
}

.upload-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx; /* 减小底边距 */
  line-height: 1.4;
  flex: 1; /* 让描述文字占据剩余空间 */
}

.upload-btn {
  background: #ff69b4;
  color: #fff;
  border-radius: 20rpx; /* 稍微减小圆角 */
  font-size: 26rpx; /* 稍微减小字体 */
  padding: 12rpx 40rpx; /* 减小上下内边距，增加左右内边距 */
  min-width: 200rpx; /* 增加最小宽度 */
  height: 56rpx; /* 固定高度，让按钮更矮 */
  line-height: 32rpx; /* 设置行高 */
  text-align: center;
  border: none;
  margin: 0;
  align-self: flex-start;
  box-shadow: 0 2rpx 8rpx rgba(255, 105, 180, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-tip {
  text-align: center;
  color: #999;
  font-size: 24rpx;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

/* 主体内容区按钮，仅示例 */
.upload-btn-main {
  margin: 40rpx auto;
  display: block;
  background: #ffb6c1;
  color: #fff;
  border-radius: 24rpx;
  font-size: 28rpx;
  padding: 16rpx 48rpx;
  border: none;
}

.image-group {
  background: #fff;
  border-radius: 16rpx;
  margin: 24rpx 0;
  padding: 24rpx;
}
.group-title {
  font-size: 28rpx;
  color: #888;
  margin-bottom: 12rpx;
}
.image-row {
  display: flex;
  gap: 16rpx;
  margin-bottom: 12rpx;
}
.preview-img {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  object-fit: cover;
  background: #f5f5f5;
}

.img-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin: 32rpx 0;
}
.img-item {
  width: 200rpx;
  height: 300rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  object-fit: cover;
}

/* 图片分组卡片布局 */
.group-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
  margin-bottom: 320rpx; /* 增大底部空间，避免被遮挡 */
}
.card-group {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx #eee;
  padding: 24rpx;
  margin-bottom: 24rpx;
  position: relative;
}
.group-title {
  font-size: 28rpx;
  color: #888;
  margin-bottom: 12rpx;
}
.img-row {
  display: flex;
  gap: 24rpx;
  justify-content: center;
  margin-bottom: 12rpx;
}
.img-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 280rpx; /* 从200rpx增加到280rpx */
}
.img {
  width: 260rpx; /* 从180rpx增加到260rpx */
  height: 360rpx; /* 从260rpx增加到360rpx */
  border-radius: 8rpx;
  background: #f5f5f5;
  object-fit: cover;
}

/* 预览容器 */
.preview-container {
  position: relative;
  width: 260rpx; /* 从180rpx增加到260rpx */
  height: 360rpx; /* 从260rpx增加到360rpx */
}

.preview-img {
  width: 260rpx; /* 从180rpx增加到260rpx */
  height: 360rpx; /* 从260rpx增加到360rpx */
  border-radius: 8rpx;
  background: #f5f5f5;
  object-fit: cover;
  border: 2rpx solid #007aff;
}

.preview-badge {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: #007aff;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  z-index: 10;
}

/* 选中信息 */
.selected-info {
  width: 260rpx; /* 从180rpx增加到260rpx */
  margin: 4rpx 0;
  text-align: center;
}

.selected-text {
  font-size: 22rpx;
  color: #007aff;
  background: rgba(0, 122, 255, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.edit-btn {
  margin: 8rpx 0 4rpx 0;
  color: #888;
  font-size: 24rpx;
  text-align: center;
}
.btn-row {
  display: flex;
  gap: 24rpx;
  margin-bottom: 12rpx;
  min-height: 48rpx;
  justify-content: center;
  align-items: center;
}
.mini-btn {
  min-width: 100rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  height: 56rpx;
  line-height: 56rpx;
  margin: 0;
  background: #ffb6c1;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  box-sizing: border-box;
  overflow: visible;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}
.count-row {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  border: 1rpx solid #f0f0f0;
  margin: 24rpx auto 0 auto;
  padding: 12rpx 0;
  width: 320rpx;
}
.count-row button {
  width: 48rpx;
  height: 48rpx;
  border: none;
  background: #f5f5f5;
  color: #888;
  font-size: 32rpx;
  border-radius: 12rpx;
  margin: 0 16rpx;
  box-shadow: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.count-row text {
  min-width: 48rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background: #f9f9f9;
  border-radius: 8rpx;
  padding: 0 16rpx;
  margin: 0 8rpx;
  border: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
}

.count-input {
  width: 60rpx;
  height: 48rpx;
  text-align: center;
  font-size: 28rpx;
  color: #333;
  background: #f9f9f9;
  border-radius: 8rpx;
  border: 1rpx solid #eee;
  margin: 0 8rpx;
  padding: 0;
  box-sizing: border-box;
}

.card-close {
  position: absolute;
  right: 16rpx;
  top: 8rpx;
  font-size: 40rpx;
  color: #bbb;
  z-index: 10;
  cursor: pointer;
  font-weight: bold;
  background: #fff;
  border-radius: 50%;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  transition: color 0.2s;
}
.card-close:active {
  color: #ff4d6b;
}

.img-placeholder {
  width: 260rpx; /* 从180rpx增加到260rpx */
  height: 360rpx; /* 从260rpx增加到360rpx */
  border-radius: 8rpx;
  background: #fafafa;
  border: 2rpx dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
  font-size: 80rpx;
  margin-bottom: 8rpx;
}

/* 边框选择弹窗样式 */
.border-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: white;
  border-radius: 20rpx;
  z-index: 1001;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.border-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  padding: 30rpx 0 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.category-tabs {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  overflow-x: auto;
  white-space: nowrap;
}

.category-tab {
  padding: 15rpx 25rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  border-radius: 25rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s;
}

.category-tab.active {
  background: #007aff;
  color: white;
}

.border-images {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.border-image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 10rpx;
  background: #f9f9f9;
  transition: all 0.3s;
}

.border-image-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.border-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.border-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

/* READY产品样式 */
.ready-product-section {
  padding: 32rpx 24rpx;
}

.ready-product-card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

.ready-product-info {
  margin-bottom: 32rpx;
}

.ready-product-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.ready-product-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.ready-count-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 24rpx;
}

.count-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.count-btn {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #ffb6c1;
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.3s ease;
}

.count-btn:active {
  transform: scale(0.9);
  background: #ff69b4;
}

.ready-count-input {
  width: 120rpx;
  height: 60rpx;
  text-align: center;
  font-size: 28rpx;
  border: 2rpx solid #ffb6c1;
  border-radius: 12rpx;
  background: #fff;
}