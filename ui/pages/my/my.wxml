<view class="container">
  <view class="my-page">
    <!-- 头像昵称 -->
    <view class="user-info">
      <image class="avatar" wx:if="{{!isLogin}}" bindtap="goToNumberLogin" src="https://img.yzcdn.cn/vant/empty-image-default.png" mode="aspectFill"/>
      <image class="avatar" wx:if="{{isLogin}}" src="{{isLogin && userInfo.avatar ? userInfo.avatar : 'https://img.yzcdn.cn/vant/empty-image-default.png'}}" mode="aspectFill"/>
      <view class="user-meta">
        <view class="nickname" wx:if="{{!isLogin}}" bindtap="goToNumberLogin">登录/注册</view>

         <view class="nickname" wx:if="{{isLogin}}">{{userInfo.name}}</view>
        <view class="phone" wx:if="{{!isLogin}}" bindtap="goToNumberLogin">支持微信快速登录</view>

        <view class="phone" wx:if="{{isLogin}}" bindtap="logout">退出登录</view>
      </view>
    </view>
    
    <!-- 订单区块 -->
    <block wx:for="{{orderBlocks}}" wx:key="type">
      <view class="order-block">
        <view class="order-header">
          <text>{{item.title}}</text>
          <view class="order-all" bind:tap="navigateToOrderList" data-id="{{item.id}}">全部订单 ></view>
        </view>
        <view class="order-status-row">
          <block wx:for="{{item.statusList}}" wx:for-item="status" wx:for-index="statusIndex" wx:key="statusIndex">
            <view class="order-status" bindtap="navigateToOrderStatus" data-type="{{item.type}}" data-status="{{statusIndex}}">
              <view class="order-icon-container">
                <image class="order-icon" src="{{status.icon}}"/>
                <view class="order-badge" wx:if="{{status.count > 0}}">{{status.count}}</view>
              </view>
              <view class="order-status-text">{{status.text}}</view>
            </view>
          </block>
        </view>
      </view>
    </block>

    <!-- 菜单区块 -->
    <view class="menu-block">
      <view class="menu-item" bind:tap="navigateToAbout"><text>地址管理</text></view>
      <view class="menu-item" bind:tap="navigateToAgreement"><text>用户协议</text></view>
      <view class="menu-item" bind:tap="navigateToPrivacy"><text>隐私协议</text></view>
      <view class="menu-item contact">
        <text>联系方式</text>
        <text class="online-time">在线时间：(9:30-22:30)</text>
      </view>
    </view>
  </view>
</view>