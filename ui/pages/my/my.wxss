.container {
  padding-top: 80rpx;
}
.my-page {
  background: #f7f7f7;
  min-height: 100vh;
  padding-top:env(safe-area-inset-top);
}
.user-info {
  display: flex;
  align-items: center;
  padding: 32rpx 24rpx 16rpx 24rpx;
  background: #fff;
  border-radius: 0 0 24rpx 24rpx;
  margin-bottom: 24rpx;
}
.avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  margin-right: 24rpx;
  background: #f3f3f3;
}
.user-meta {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.phone {
  font-size: 24rpx;
  color: #888;
}
.order-block {
  background: #fff;
  border-radius: 24rpx;
  margin: 0 16rpx 20rpx 16rpx;
  padding: 20rpx 0 8rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24rpx 12rpx 24rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}
.order-all {
  font-size: 24rpx;
  color: #888;
  font-weight: normal;
}
.order-status-row {
  display: flex;
  justify-content: space-around;
  padding: 0 12rpx 12rpx 12rpx;
}
.order-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.order-icon-container {
  position: relative;
  margin-bottom: 8rpx;
}

.order-icon {
  width: 48rpx;
  height: 48rpx;
}

.order-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4d6b;
  color: white;
  font-size: 18rpx;
  font-weight: bold;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
  box-shadow: 0 2rpx 4rpx rgba(255, 77, 107, 0.3);
}

.order-status-text {
  font-size: 22rpx;
  color: #888;
}
.menu-block {
  background: #fff;
  border-radius: 24rpx;
  margin: 0 16rpx;
  padding: 8rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.03);
}
.menu-item {
  display: flex;
  align-items: center;
  padding: 24rpx 24rpx;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}
.menu-item:last-child {
  border-bottom: none;
}
.contact {
  justify-content: space-between;
}
.online-time {
  color: #ff4081;
  font-size: 22rpx;
  margin-left: 12rpx;
}