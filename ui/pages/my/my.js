const config = require('../../config/setting');
const { getImageUrl } = config;
var app = getApp();

Page({
  data: {
    userInfo: null,
    isLogin: false,
    orderBlocks: [
      {
        type: 'normal',
        title: '我的订单',
        id:'1',
        statusList: [
          { text: '待付款', icon: '/images/待付款.png', count: 0 },
          { text: '待发货', icon: '/images/待发货.png', count: 0 },
          { text: '待收货', icon: '/images/待收货.png', count: 0 },
          { text: '已签收', icon: '/images/已签收.png', count: 0 }
        ],
      }
    ]
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    this.checkLoginStatus();
    this.loadOrderStats();
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLogin = app.checkLogin();
    const userInfo = app.getUserInfo();

    // 处理头像URL
    const avatarUrl = isLogin && userInfo && userInfo.avatar
      ? getImageUrl(userInfo.avatar)
      : 'https://img.yzcdn.cn/vant/empty-image-default.png';

    this.setData({
      isLogin: isLogin,
      userInfo: userInfo,
      avatarUrl: avatarUrl
    });
  },

  // 加载订单统计信息
  loadOrderStats() {
    const userInfo = app.getUserInfo();
    if (!userInfo || !userInfo.id) {
      console.log('用户未登录，无法获取订单统计');
      return;
    }

    wx.request({
      url: `${config.rootUrl}/orders/stats/${userInfo.id}`,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          const stats = res.data.data;

          // 更新订单状态数量
          const updatedOrderBlocks = this.data.orderBlocks.map(block => {
            if (block.type === 'normal') {
              return {
                ...block,
                statusList: [
                  { ...block.statusList[0], count: stats.pending },
                  { ...block.statusList[1], count: stats.paid },
                  { ...block.statusList[2], count: stats.shipped },
                  { ...block.statusList[3], count: stats.completed }
                ]
              };
            }
            return block;
          });

          this.setData({
            orderBlocks: updatedOrderBlocks
          });
        }
      },
      fail: (err) => {
        console.error('获取订单统计失败:', err);
      }
    });
  },

  // 登出
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearUserInfo();
          this.setData({
            isLogin: false,
            userInfo: null
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  navigateToOrderStatus(event) {
    const { type, status } = event.currentTarget.dataset;
    const statusId = status + 1; // The status index is 0-based, so we add 1
    let url = '';

    if (type === 'normal') {
      url = `/pages/order/RegularOrder/RegularOrder?id=${statusId}`;
    } else if (type === 'merge') {
      url = `/pages/order/MergeOrders/MergeOrders?id=${statusId}`;
    } else if (type === 'batch') {
      url = `/pages/order/BulkOrder/BulkOrder?id=${statusId}`;
    } else if (type === 'group') {
      url = `/pages/order/GroupOrder/GroupOrder?id=${statusId}`;
    }

    if (url) {
      wx.navigateTo({ url });
    }
  },

  navigateToOrderList(event){
    var index = event.currentTarget.dataset.id; // 获取当前活动的索引
    console.log(index);
    if (index == '1') {
      wx.navigateTo({
        url: '/pages/order/RegularOrder/RegularOrder',
      })
    } else if (index == '2') {
      wx.navigateTo({
        url: '/pages/order/MergeOrders/MergeOrders', // 示例路径
      })
    } else if (index == '3') {
      wx.navigateTo({
        url: '/pages/order/BulkOrder/BulkOrder', // 示例路径
      })
    } else if (index == '4') { // 明确判断 index 为 '4' 的情况
      wx.navigateTo({
        url: '/pages/order/GroupOrder/GroupOrder', // 示例路径
      })
    }
  },

  navigateToAbout() {
    wx.navigateTo({
      url: '/pages/about/about',
    });
  },

  navigateToPrivacy() {
    wx.navigateTo({
      url: '/pages/privacy/privacy',
    });
  },

  navigateToAgreement() {
    wx.navigateTo({
      url: '/pages/agreement/agreement',
    })
  },

  goToNumberLogin() {
    wx.navigateTo({
      url: '/pages/number_login/number_login',
    });
  },
})