.product-detail-page {
  position: relative;
  padding-bottom: 120rpx; /* 给底部按钮留空间 */
}

.detail-img {
  width: 100%;
  height: 400rpx;
  display: block;
  margin-bottom: 24rpx;
}
.detail-title {
  font-size: 36rpx;
  font-weight: bold;
  margin: 12rpx 0 12rpx 0;
  color: #222;
  padding: 0 24rpx;
}
.detail-content {
  font-size: 28rpx;
  color: #888;
  padding: 16rpx 24rpx 24rpx 24rpx;
  white-space: pre-line;
  line-height: 1.6;
}
.bottom-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  border-top: 1rpx solid #eee;
  z-index: 100;
}
.bar-btn {
  flex: 1;
  margin: 0 8rpx;
  border-radius: 40rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  background: #f8f8f8;
  color: #333;
  border: none;
}
.add-cart {
  background: #ffd700;
  color: #fff;
}
.buy-now {
  background: #d94f4f;
  color: #fff;
}
/* 主图容器 */
.main-image-container {
  width: 100%;
  height: 400rpx;
  margin-top: 0;
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.main-image {
  width: 100%;
  height: 400rpx;
  display: block;
}

/* 图片画廊样式 */
.image-gallery {
  background: #fff;
  margin: 20rpx 24rpx;
  padding: 24rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

.gallery-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.gallery-grid {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.gallery-item {
  width: 100%;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f9f9f9;
  margin-bottom: 16rpx;
  position: relative;
}

.gallery-image {
  width: 100%;
  height: 300rpx;
  display: block;
  object-fit: cover;
}
/* 产品信息栏样式 */
/* 产品信息卡片样式 */
.product-info-card {
  background: #fff;
  margin: 20rpx 24rpx;
  padding: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
}

.card-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.card-price-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-price-group {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.card-price {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.card-sale-price {
  font-size: 32rpx;
  color: #ff4d4f;
  font-weight: bold;
}

.card-original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
}

.card-limit-tag {
  background: #ffb6c1;
  color: #fff;
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  white-space: nowrap;
}

.card-detail {
  font-size: 28rpx;
  color: #888;
  line-height: 1.6;
  white-space: pre-line;
}
.sold {
  color: #888;
  font-size: 24rpx;
}
.print-btn {
  position: fixed;
  left: 50%;
  bottom: 48rpx;
  transform: translateX(-50%);
  width: 80vw;
  height: 88rpx;
  background: #e91e63;
  color: #fff;
  font-size: 32rpx;
  border: none;
  border-radius: 44rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
  z-index: 999;
}

.delete-btn {
  position: fixed;
  left: 50%;
  bottom: 160rpx;
  transform: translateX(-50%);
  width: 80vw;
  height: 88rpx;
  background: #ff4757;
  color: #fff;
  font-size: 32rpx;
  border: none;
  border-radius: 44rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 71, 87, 0.3);
  z-index: 999;
}

.popup-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.3);
  z-index: 1000;
}

.popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 32rpx 0 24rpx 0;
  z-index: 1001;
  box-sizing: border-box;
  transform: translateY(100%);
  transition: transform 0.3s cubic-bezier(.25,.8,.25,1);
}
.popup.show {
  transform: translateY(0);
}

.popup-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 16rpx;
}

.popup-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 12rpx;
  object-fit: cover;
  margin-right: 20rpx;
  background: #f5f5f5;
}

.popup-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: #333;
  text-align: left;
}

.popup-size {
  font-size: 26rpx;
  color: #888;
  margin-bottom: 8rpx;
  text-align: left;
}

.popup-price-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.popup-price {
  font-size: 28rpx;
  color: #e64340;
  font-weight: bold;
  text-align: left;
}

.popup-original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  font-weight: normal;
}

/* 工艺组样式 */
.attribute-groups {
  margin-bottom: 24rpx;
}

.attribute-group {
  margin-bottom: 32rpx;
}

.attribute-group-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 16rpx;
  padding-left: 4rpx;
}

.attribute-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.attribute-option {
  width: 30%;
  min-width: 0;
  margin-right: 5%;
  margin-bottom: 10rpx;
  background: #f5f5f5;
  border-radius: 10rpx;
  padding: 5rpx;
  box-sizing: border-box;
  transition: background 0.2s, border 0.2s;
  border: 2rpx solid transparent;
}

.attribute-option:nth-child(3n) {
  margin-right: 0;
}

.attribute-option.selected {
  background: #ffb6c1;
  border-color: #ff4d6b;
}

.option-image {
  width: 100%;
  height: 140rpx;
  border-radius: 10rpx;
  margin-bottom: 0rpx;
}

.option-content {
  text-align: center;
}

.option-value {
  font-size: 20rpx;
  color: #333;
  margin-bottom: 1rpx;
}

.option-only-value {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.attribute-option.selected .option-value {
  color: #fff;
}

.option-price {
  font-size: 22rpx;
  color: #ff4d6b;
  font-weight: bold;
}

.attribute-option.selected .option-price {
  color: #fff;
}
.popup-total {
  font-size: 28rpx;
  margin-bottom: 24rpx;
  text-align: right;
}
.popup-total-price {
  color: #ff4d6b;
  font-weight: bold;
}
.popup-confirm-btn {
  width: 100%;
  background: #ffb6c1;
  color: #fff;
  border-radius: 44rpx;
  font-size: 32rpx;
  height: 88rpx;
  margin-top: 8rpx;
}