<view class="product-detail-page">
  <!-- 统一的导航栏 -->
  <navigation-bar title="产品详情" back="{{true}}" color="black" background="#FFF"></navigation-bar>

  <!-- 顶部主图 -->
  <view class="main-image-container">
    <image class="main-image" src="{{mainImage}}" mode="aspectFill" lazy-load="{{false}}" show-menu-by-longpress="{{false}}"/>
  </view>

  <!-- 产品信息卡片 -->
  <view class="product-info-card">
    <view class="card-title">{{product.title}}</view>

    <view class="card-price-section">
      <view class="card-price-group">
        <!-- 当有售价且售价低于标价时，显示售价和划线标价 -->
        <block wx:if="{{price_sale && price_tag && price_sale < price_tag}}">
          <view class="card-sale-price">￥{{price_sale}} 起</view>
          <view class="card-original-price">￥{{price_tag}}</view>
        </block>
        <!-- 当只有售价或售价等于标价时，只显示售价 -->
        <block wx:elif="{{price_sale}}">
          <view class="card-price">￥{{price_sale}} 起</view>
        </block>
        <!-- 当只有标价时，显示标价 -->
        <block wx:else>
          <view class="card-price">￥{{price_tag || '0.00'}} 起</view>
        </block>
      </view>
      <!-- 起订数量信息已隐藏，现在所有产品都支持1张起订 -->
    </view>

    <view class="card-detail">
      {{product.detail}}
    </view>
  </view>

  <!-- 图片列表 -->
  <view class="image-gallery">
    <view class="gallery-title">产品图片</view>
    <view class="debug-info" style="font-size: 24rpx; color: #666; margin-bottom: 20rpx;">
    </view>
    <view class="gallery-grid">
      <block wx:for="{{imageList}}" wx:key="*this" wx:for-index="idx">
        <view class="gallery-item">
          <view class="debug-item" style="font-size: 20rpx; color: #999; padding: 5rpx;"></view>
          <image class="gallery-image" src="{{item}}" mode="aspectFill" lazy-load="{{false}}" bindtap="previewImage" data-src="{{item}}" data-urls="{{imageList}}" binderror="onImageError" bindload="onImageLoad"/>
        </view>
      </block>
    </view>
  </view>

  <!-- 底部操作栏 -->

  <!-- 浮动打印按钮 -->
  <button class="print-btn" bindtap="onPrint">立即打印</button>

  <!-- 管理员删除按钮 (仅在管理模式下显示) -->
  <button class="delete-btn" wx:if="{{isAdmin}}" bindtap="onDeleteProduct">删除产品</button>

  <!-- 弹窗 -->
  <view class="popup-mask" wx:if="{{showPopup}}" bindtap="closePopup"></view>
  <view class="popup{{popupShowClass}}" wx:if="{{showPopup}}">
    <view class="popup-header">
      <image class="popup-img" src="{{popupProduct.img}}" mode="aspectFill"/>
      <view class="popup-info">
        <view class="popup-title">{{popupProduct.title}}</view>
        <view class="popup-size">尺寸：{{popupProduct.size}}</view>
        <view class="popup-price-section">
          <view class="popup-price">价格：￥{{popupTotalPrice}}</view>
          <view class="popup-original-price" wx:if="{{popupOriginalPrice}}">￥{{popupOriginalPrice}}</view>
        </view>
      </view>
    </view>
    <!-- 工艺组列表 -->
    <view class="attribute-groups">
      <block wx:for="{{popupAttributeGroups}}" wx:key="attribute_id" wx:for-item="group" wx:for-index="groupIndex">
        <view class="attribute-group">
          <view class="attribute-group-title">{{group.name}}：</view>
          <view class="attribute-options">
            <block wx:for="{{group.options}}" wx:key="id" wx:for-item="option" wx:for-index="optionIndex">
              <view class="attribute-option {{option.selected ? 'selected' : ''}}"
                    bindtap="selectProcess"
                    data-group-index="{{groupIndex}}"
                    data-option-index="{{optionIndex}}">
                <image wx:if="{{option.image_url}}" class="option-image" src="{{option.image_url}}" mode="aspectFill"/>
                <view class="option-content">
                  <view wx:if="{{option.image_url}}" class="option-value">{{option.value}}</view>
                  <view wx:if="{{!option.image_url}}" class="option-only-value">{{option.value}}</view>
                  <!-- <view class="option-price" wx:if="{{option.price_adjustment > 0}}">+￥{{option.price_display}}</view> -->
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>
    <button class="popup-confirm-btn" bindtap="confirmPrint">确认打印</button>
  </view>
</view>