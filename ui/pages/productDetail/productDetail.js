const config = require('../../config/setting');
const { getImageUrl } = config;

Page({
  data: {
    product: {},
    showPopup: false,
    popupProduct: {},
    popupAttributeGroups: [], // 工艺组列表
    popupTotalPrice: 0,
    popupOriginalPrice: '', // 弹窗中的原价（price_tag + 工艺价格）
    popupShowClass: '',
    basePrice: 0, // 产品基础价格
    currentTotalPrice: '0.00', // 当前总价格（基础价格+工艺价格）
    mainImage: '', // 顶部主图
    imageList: [], // 底部图片列表
    isAdmin: false // 是否为管理员模式
  },
  onLoad(options) {
    const id = options.id;
    console.log('页面加载，产品ID:', id);
    console.log('页面参数:', options);
    if (!id) {
      console.error('产品ID为空');
      wx.showToast({
        title: '产品ID缺失',
        icon: 'none'
      });
      return;
    }
    this.loadProductDetail(id);
    this.checkAdminMode();
  },

  // 检查是否为管理员模式（可以通过URL参数或用户信息判断）
  checkAdminMode() {
    // 这里可以根据实际需求设置管理员判断逻辑
    // 例如：检查用户权限、特定参数等
    // 暂时设置为true以便测试，实际使用时应该根据用户权限判断
    const userInfo = wx.getStorageSync('userInfo');
    const isAdmin = userInfo && userInfo.role === 'admin'; // 或其他判断逻辑
    this.setData({ isAdmin });
  },
  loadProductDetail(id) {
    console.log('开始加载产品详情，ID:', id);
    const apiUrl = `${config.rootUrl}/product/${id}`;
    console.log('API URL:', apiUrl);
    
    // 使用正确的单个产品详情API
    wx.request({
      url: apiUrl,
      method: 'GET',
      success: (res) => {
        console.log('API返回数据:', res.data);
        if (res.data.success) {
          const product = res.data.data;

          // 处理价格数据，确保数字类型
          product.price_sale = product.price_sale ? Number(product.price_sale) : null;
          product.price_tag = product.price_tag ? Number(product.price_tag) : null;

          // 基础价格优先使用售价，其次标价
          const basePrice = product.price_sale || product.price_tag || 0;

          // 处理图片列表：使用 img_list 字段，多张图片用 | 分割
          const defaultImage = 'https://img.yzcdn.cn/vant/empty-image-default.png';
          const mainImage = getImageUrl(product.img);

          let imageList = [];
          if (product.img_list && product.img_list.trim()) {
            // 将 img_list 按 | 分割成数组，并过滤空字符串，然后处理图片URL
            imageList = product.img_list.split('|')
              .filter(url => url.trim())
              .map(url => getImageUrl(url.trim()));
          }

          // 如果 img_list 为空，则使用主图作为备选
          if (imageList.length === 0) {
            imageList = product.img ? [getImageUrl(product.img)] : [defaultImage];
          }

          console.log('产品数据:', {
            id: product.id,
            title: product.title,
            price_sale: product.price_sale,
            price_tag: product.price_tag,
            basePrice: basePrice,
            img: product.img,
            img_list: product.img_list,
            imageCount: imageList.length,
            imageList: imageList
          });

          this.setData({
            product,
            basePrice: basePrice,
            currentTotalPrice: basePrice.toFixed(2),
            popupTotalPrice: basePrice.toFixed(2),
            price_sale: (product.price_sale / 100).toFixed(2),
            price_tag: (product.price_tag / 100).toFixed(2),
            mainImage: mainImage,
            imageList: imageList
          });

          console.log('产品详情加载完成');
          console.log('设置后的数据:', {
            mainImage: this.data.mainImage,
            imageList: this.data.imageList,
            imageListLength: this.data.imageList.length
          });
        } else {
          wx.showToast({
            title: res.data.message || '未找到该产品',
            icon: 'none'
          });
        }
      },
      fail: () => {
        console.log('API请求失败');
        wx.showToast({
          title: '获取产品信息失败',
          icon: 'none'
        });
      }
    });
  },

  onBack() {
    wx.navigateBack();
  },

  // 图片预览功能
  previewImage(e) {
    const current = e.currentTarget.dataset.src;
    const urls = e.currentTarget.dataset.urls;
    wx.previewImage({
      current: current,
      urls: urls
    });
  },

  // 图片加载成功
  onImageLoad(e) {
    console.log('图片加载成功:', e.currentTarget.dataset.src);
  },

  // 图片加载失败
  onImageError(e) {
    console.log('图片加载失败:', e.currentTarget.dataset.src, e.detail);
  },



  onPrint() {
    const productId = this.data.product.id;
    // 获取产品信息
    wx.request({
      url: `${config.rootUrl}/product/${productId}`,
      method: 'GET',
      success: (res) => {
        if (res.data.success) {
          const product = res.data.data;
          this.setData({
            popupProduct: {
              ...product,
              img: getImageUrl(product.img) // 处理弹窗中的产品图片URL
            }
          });
        }
      }
    });
    // 获取规格模版信息
    wx.request({
      url: `${config.rootUrl}/template-attribute/${productId}`,
      method: 'GET',
      success: (res) => {
        console.log('template-attribute:', res.data.data);
        if (res.data.success) {
          const attributeGroups = res.data.data.map(group => ({
            ...group,
            options: group.options.map((option, index) => ({
              ...option,
              selected: index === 0, // 默认选择每组的第一个选项
              price_display: (Number(option.price_adjustment || 0) / 100).toFixed(2), // 格式化价格显示
              image_url: option.image_url ? getImageUrl(option.image_url) : '' // 处理工艺选项图片URL
            }))
          }));
          
          // 计算初始总价（基础价格 + 所有默认选项的价格调整）
          const product = this.data.product;
          const priceSale = Number(product.price_sale || 0);
          const priceTag = Number(product.price_tag || 0);
          
          let totalPriceAdjustment = 0;
          attributeGroups.forEach(group => {
            const selectedOption = group.options.find(option => option.selected);
            if (selectedOption) {
              totalPriceAdjustment += Number(selectedOption.price_adjustment || 0);
            }
          });

          const totalPrice = (priceSale + totalPriceAdjustment) / 100;
          const originalTotalPrice = (priceTag + totalPriceAdjustment) / 100;

          this.setData({
            popupAttributeGroups: attributeGroups,
            popupTotalPrice: totalPrice.toFixed(2),
            popupOriginalPrice: (priceTag && priceTag !== priceSale) ? originalTotalPrice.toFixed(2) : ''
          });
        } else {
          const product = this.data.product;
          const priceSale = Number(product.price_sale || 0);
          this.setData({
            popupAttributeGroups: [],
            popupTotalPrice: (priceSale / 100).toFixed(2),
            popupOriginalPrice: ''
          });
        }
      }
    });
    this.openPopup();
  },

  openPopup() {
    this.setData({ showPopup: true, popupShowClass: '' });
    setTimeout(() => {
      this.setData({ popupShowClass: ' show' });
    }, 10); // 10ms后加show类，触发动画
  },

  closePopup() {
    this.setData({ popupShowClass: '' });
    setTimeout(() => {
      this.setData({ showPopup: false });
    }, 300); // 等动画结束后再隐藏
  },

  selectProcess(e) {
    const { groupIndex, optionIndex } = e.currentTarget.dataset;
    let attributeGroups = [...this.data.popupAttributeGroups];
    
    // 更新选中状态：在指定组内只能选择一个选项
    attributeGroups[groupIndex].options = attributeGroups[groupIndex].options.map((option, idx) => ({
      ...option,
      selected: idx === optionIndex
    }));

    // 计算总价格调整（所有选中选项的价格调整之和）
    let totalPriceAdjustment = 0;
    attributeGroups.forEach(group => {
      const selectedOption = group.options.find(option => option.selected);
      if (selectedOption) {
        totalPriceAdjustment += Number(selectedOption.price_adjustment || 0);
      }
    });

    const product = this.data.product;
    const priceSale = Number(product.price_sale || 0);
    const priceTag = Number(product.price_tag || 0);

    const totalPrice = (priceSale + totalPriceAdjustment) / 100;
    const originalTotalPrice = (priceTag + totalPriceAdjustment) / 100;

    this.setData({
      popupAttributeGroups: attributeGroups,
      popupTotalPrice: totalPrice.toFixed(2),
      popupOriginalPrice: (priceTag && priceTag !== priceSale) ? originalTotalPrice.toFixed(2) : ''
    });
  },

  confirmPrint() {
    const { popupAttributeGroups, product } = this.data;
    
    // 检查是否所有工艺组都有选中的选项
    const selectedOptions = [];
    let hasUnselected = false;

    popupAttributeGroups.forEach(group => {
      const selectedOption = group.options.find(option => option.selected);
      if (selectedOption) {
        selectedOptions.push({
          groupId: group.attribute_id,
          groupName: group.name,
          optionId: selectedOption.id,
          optionValue: selectedOption.value,
          priceAdjustment: selectedOption.price_adjustment
        });
      } else {
        hasUnselected = true;
      }
    });
    
    if (hasUnselected) {
      wx.showToast({ title: '请选择所有工艺选项', icon: 'none' });
      return;
    }
    let processName = '';
    let processPrice = 0;
    selectedOptions.forEach(option => {
      console.log('option', option);
      processName += option.optionValue + '×';
      processPrice += Number(option.priceAdjustment);
    })
    processName = processName.slice(0, -1);
    // 将选中的工艺信息传递给定制页面
    const selectedOptionsStr = JSON.stringify(selectedOptions);
    wx.navigateTo({
      url: `/pages/customized/customized?processName=${encodeURIComponent(processName)}&processPrice=${encodeURIComponent(processPrice)}&selectedOptions=${encodeURIComponent(selectedOptionsStr)}&productId=${product.id}&category=${encodeURIComponent(product.category || 'DIY')}`,

    });
  },

  // 删除产品
  onDeleteProduct() {
    const product = this.data.product;
    if (!product.id) {
      wx.showToast({ title: '产品信息错误', icon: 'none' });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除产品"${product.title}"吗？此操作不可撤销。`,
      confirmText: '删除',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          this.deleteProduct(product.id);
        }
      }
    });
  },

  // 执行删除操作
  deleteProduct(productId) {
    wx.showLoading({ title: '删除中...' });

    wx.request({
      url: `${config.rootUrl}/products/delete/${productId}`,
      method: 'POST',
      success: (res) => {
        wx.hideLoading();
        console.log('删除响应:', res);

        if (res.statusCode === 200 && res.data.success) {
          wx.showToast({
            title: '删除成功',
            icon: 'success',
            duration: 2000
          });

          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        } else {
          wx.showToast({
            title: res.data.error || '删除失败',
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('删除请求失败:', err);
        wx.showToast({
          title: '网络错误，删除失败',
          icon: 'none',
          duration: 3000
        });
      }
    });
  }
});