.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
}

.loading {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
}

/* 订单状态 */
.status-section {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 40rpx;
  font-weight: bold;
  margin-right: 30rpx;
}

.status-info {
  flex: 1;
}

.status-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 28rpx;
  color: #666;
}

/* 通用section样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 订单信息 */
.order-info-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.value.status {
  color: #ff6b6b;
  font-weight: bold;
}

.copy-hint {
  font-size: 24rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 商品信息 */
.product-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.product-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  font-size: 28rpx;
  color: #666;
  background: #f8f8f8;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
}

.detail-item.discount {
  color: #00c851;
  font-weight: bold;
  background: #f0fff4;
}

.detail-item.total-price {
  color: #ff6b6b;
  font-weight: bold;
  background: #fff5f5;
}

/* 价格汇总 */
.price-summary {
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.price-summary .detail-item {
  display: block;
  text-align: right;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.price-summary .detail-item:last-child {
  margin-bottom: 0;
}

.price-summary .detail-item.total-price {
  font-size: 32rpx;
}

/* 收货地址 */
.address-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.address-info {
  border: 1rpx solid #f0f0f0;
  border-radius: 8rpx;
  padding: 20rpx;
}

.recipient {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.address {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

/* 操作按钮 */
.action-section {
  padding: 30rpx 0;
}

.action-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
}

.action-btn.primary {
  background: #007aff;
  color: white;
}
