<!-- 价格格式化已在后端处理，不需要WXS模块 -->

<view class="container">
  <!-- 统一的导航栏 -->
  <navigation-bar title="订单详情" back="{{true}}" color="black" background="#FFF"></navigation-bar>

  <view wx:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>
  
  <view wx:else class="order-detail">
    <!-- 订单状态 -->
    <view class="status-section">
      <view class="status-icon">✓</view>
      <view class="status-info">
        <text class="status-title">订单提交成功</text>
        <text class="status-desc">您的订单已提交，请等待处理</text>
      </view>
    </view>

    <!-- 订单信息 -->
    <view class="order-info-section">
      <view class="section-title">订单信息</view>
      <view class="info-item">
        <text class="label">订单号：</text>
        <text class="value" bindtap="copyOrderNo">{{order.order_no}}</text>
        <text class="copy-hint">（点击复制）</text>
      </view>
      <view class="info-item">
        <text class="label">下单时间：</text>
        <text class="value">{{order.created_at}}</text>
      </view>
      <view class="info-item">
        <text class="label">订单状态：</text>
        <text class="value status">{{order.status === 'pending' ? '待处理' : order.status}}</text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="product-section">
      <view class="section-title">商品信息</view>
      <view wx:for="{{order.items}}" wx:key="id" class="product-item">
        <image class="product-image" src="{{item.product_image_snapshot}}" mode="aspectFill"/>
        <view class="product-info">
          <text class="product-name">{{item.product_name_snapshot}}</text>
          <view class="product-details">
            <text class="detail-item">数量：{{item.quantity}}</text>
            <text class="detail-item">单价：￥{{item.price_at_purchase}}</text>
            <text class="detail-item">小计：￥{{item.line_total_amount}}</text>
          </view>
        </view>
      </view>

      <!-- 价格汇总 -->
      <view class="price-summary">
        <text class="detail-item">商品总价：￥{{order.total_product_amount}}</text>
        <text wx:if="{{order.discount_amount && order.discount_amount > 0}}" class="detail-item discount">优惠券折扣：-￥{{order.discount_amount}}</text>
        <text class="detail-item total-price">实付金额：￥{{order.total_order_amount}}</text>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-section">
      <view class="section-title">收货地址</view>
      <view class="address-info">
        <text class="recipient">{{order.address_name}} {{order.address_phone}}</text>
        <text class="address">{{order.province}}{{order.city}}{{order.district}}{{order.detail}}</text>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn primary" bindtap="goHome">返回首页</button>
    </view>
  </view>
</view>
