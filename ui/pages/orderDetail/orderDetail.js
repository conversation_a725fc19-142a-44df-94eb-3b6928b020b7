const config = require('../../config/setting');

Page({
  data: {
    order: null,
    loading: true
  },

  onLoad(options) {
    const orderId = options.orderId;
    if (orderId) {
      this.loadOrderDetail(orderId);
    } else {
      wx.showToast({
        title: '订单ID不存在',
        icon: 'none'
      });
    }
  },

  loadOrderDetail(orderId) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: `${config.rootUrl}/orders/${orderId}`,
      method: 'GET',
      data: {
        user_id: userInfo.id
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            order: res.data.data,
            loading: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取订单详情失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 复制订单号
  copyOrderNo() {
    if (this.data.order && this.data.order.order_no) {
      wx.setClipboardData({
        data: this.data.order.order_no,
        success: () => {
          wx.showToast({
            title: '订单号已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 返回首页
  goHome() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});
