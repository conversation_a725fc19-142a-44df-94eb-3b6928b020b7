<!--pages/image-editor/image-editor.wxml-->
<view class="container {{modalActive ? 'modal-active' : ''}}">
  <!-- 统一的导航栏 -->
  <navigation-bar title="{{editType === 'border' ? '边框编辑' : '卡背编辑'}}" back="{{true}}" color="black" background="#FFF"></navigation-bar>

  <!-- 正面反面切换按钮 -->
  <view class="side-switch">
    <button class="side-btn {{currentSide === 'front' ? 'active' : ''}} {{!frontImage ? 'disabled' : ''}}" bindtap="switchToFront">正面</button>
    <button class="side-btn {{currentSide === 'back' ? 'active' : ''}} {{!backImage ? 'disabled' : ''}}" bindtap="switchToBack">反面</button>
  </view>

  <!-- 画布区域 -->
  <view class="canvas-container">
    <!-- 卡片编辑区域 -->
    <view class="card-edit-area">
      <!-- 装订线区域标识 -->
      <view class="bleed-area" style="width: {{editAreaWidth + bleedWidth * 2}}rpx; height: {{editAreaHeight + bleedWidth * 2}}rpx;">
        <!-- 装订线区域 - 上下左右四个边 -->
        <view class="bleed-line bleed-line-top"></view>
        <view class="bleed-line bleed-line-bottom"></view>
        <view class="bleed-line bleed-line-left"></view>
        <view class="bleed-line bleed-line-right"></view>

        <!-- 安全区域 -->
        <view class="safe-area"
              bindtouchstart="onTouchStart"
              bindtouchmove="onTouchMove"
              bindtouchend="onTouchEnd"
              bindtouchcancel="onTouchEnd">
          <canvas
            type="2d"
            id="imageCanvas"
            class="image-canvas"
            style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;">
          </canvas>
        </view>

      </view>

    </view>

    <!-- 临时画布用于处理边框图片 -->
    <canvas
      type="2d"
      id="tempCanvas"
      class="temp-canvas"
      style="width: {{canvasWidth}}px; height: {{canvasHeight}}px; position: absolute; left: -9999px; top: -9999px;">
    </canvas>

    <!-- 操作提示 -->
    <view class="canvas-tip">
      拖动、缩放、旋转图片，红色区域为装订线（印刷后会裁剪）
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="bottom-actions">
    <!-- 功能按钮行 - 根据当前选中的类别显示 -->
    <view class="function-buttons" wx:if="{{currentCategory === 'image'}}">
      <button class="function-btn" bindtap="reuploadImage">{{currentSide === 'back' ? '重新上传卡背' : '重新上传'}}</button>
      <button class="function-btn" bindtap="resetTransform">重置位置</button>
      <button class="function-btn" bindtap="rotateImage">旋转90度</button>
      <button class="function-btn" bindtap="flipHorizontal">水平翻转</button>
      <button class="function-btn" bindtap="flipVertical">垂直翻转</button>
    </view>

    <view class="function-buttons" wx:if="{{currentCategory === 'border'}}">
      <button class="function-btn" wx:if="{{currentSide === 'front'}}" bindtap="selectBorder">重新选择边框</button>
      <button class="function-btn" wx:if="{{currentSide === 'back'}}" bindtap="selectSticker">重新选择贴纸</button>
    </view>

    <!-- 主操作按钮行 -->
    <view class="action-row">
      <button class="action-item {{currentCategory === 'image' ? 'active' : ''}}" bindtap="switchToImage">
        <view class="action-icon">
          <text class="icon-text">{{currentSide === 'back' ? '🎴' : '🖼'}}</text>
        </view>
        <text class="action-text">{{currentSide === 'back' ? '卡背' : '底图'}}</text>
      </button>

      <button class="action-item {{currentCategory === 'border' ? 'active' : ''}}" bindtap="switchToBorder">
        <view class="action-icon">
          <text class="icon-text">{{currentSide === 'back' ? '🏷' : '⬜'}}</text>
        </view>
        <text class="action-text">{{currentSide === 'back' ? '贴纸' : '边框'}}</text>
      </button>

      <button class="action-item" bindtap="saveImage">
        <view class="action-icon">
          <text class="icon-text">💾</text>
        </view>
        <text class="action-text">保存</text>
      </button>
    </view>
  </view>

</view>

<!-- 边框选择弹窗遮罩 -->
<view class="modal-mask" wx:if="{{showBorderModal}}" bindtap="closeBorderModal"></view>
<!-- 边框选择弹窗 -->
<view class="border-modal" wx:if="{{showBorderModal}}">
  <view class="modal-close" bindtap="closeBorderModal">×</view>
  <view class="border-title">{{currentSide === 'back' ? '选择贴纸' : '选择边框'}}</view>

  <!-- 分类栏 -->
  <view class="category-tabs">
    <view
      class="category-tab {{currentBorderCategory === 'all' ? 'active' : ''}}"
      bindtap="switchBorderCategory"
      data-category="all"
    >
      全部分类
    </view>
    <view
      wx:for="{{borderCategories}}"
      wx:key="id"
      class="category-tab {{currentBorderCategory === item.id ? 'active' : ''}}"
      bindtap="switchBorderCategory"
      data-category="{{item.id}}"
    >
      {{item.name}}
    </view>
  </view>

  <!-- 边框图片列表 -->
  <view class="border-images">
    <view
      wx:for="{{currentBorderImages}}"
      wx:key="id"
      class="border-image-item"
      bindtap="selectNewBorder"
      data-border="{{item}}"
    >
      <image src="{{item.image_url}}" class="border-img" mode="aspectFill"/>
      <view class="border-name">{{item.name}}</view>
    </view>
  </view>
</view>

<!-- 卡背选择弹窗遮罩 -->
<view class="modal-mask" wx:if="{{showCardBackModal}}" bindtap="closeCardBackModal"></view>
<!-- 卡背选择弹窗 -->
<view class="border-modal" wx:if="{{showCardBackModal}}">
  <view class="modal-close" bindtap="closeCardBackModal">×</view>
  <view class="border-title">选择卡背</view>

  <!-- 分类栏 -->
  <view class="category-tabs">
    <view
      class="category-tab {{currentCardBackCategory === 'all' ? 'active' : ''}}"
      bindtap="switchCardBackCategory"
      data-category="all"
    >
      全部分类
    </view>
    <view
      wx:for="{{cardBackCategories}}"
      wx:key="id"
      class="category-tab {{currentCardBackCategory === item.id ? 'active' : ''}}"
      bindtap="switchCardBackCategory"
      data-category="{{item.id}}"
    >
      {{item.name}}
    </view>
  </view>

  <!-- 卡背图片列表 -->
  <view class="border-images">
    <view
      wx:for="{{currentCardBackImages}}"
      wx:key="id"
      class="border-image-item"
      bindtap="selectNewCardBack"
      data-cardback="{{item}}"
    >
      <image src="{{item.image_url}}" class="border-img" mode="aspectFill"/>
      <view class="border-name">{{item.name}}</view>
    </view>
  </view>
</view>
