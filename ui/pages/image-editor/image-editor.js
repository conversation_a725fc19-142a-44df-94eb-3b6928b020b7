// pages/image-editor/image-editor.js
const config = require('../../config/setting');
const { getImageUrl } = config;

Page({
  data: {
    originalImage: '', // 当前显示的原始图片路径
    frontImage: '',    // 正面图片路径
    backImage: '',     // 反面图片路径
    borderImage: '',   // 边框图片路径
    cardBackImage: '', // 卡背图片路径
    editType: '',      // 编辑类型：border 或 cardback
    canvasWidth: 300,  // 画布宽度（会在onLoad中重新计算）
    canvasHeight: 400, // 画布高度（会在onLoad中重新计算）
    cardWidth: 90,     // 卡片实际宽度（mm）
    cardHeight: 125,   // 卡片实际高度（mm）
    finalImage: '',    // 最终合成的图片

    // 边框内部区域尺寸
    innerFrameWidth: 270,  // 边框内部区域宽度
    innerFrameHeight: 360, // 边框内部区域高度
    currentBorderData: null, // 当前选中的边框数据（包含inner_frame_size）

    // 底图变换相关状态
    imageTransform: {
      x: 0,           // X轴偏移
      y: 0,           // Y轴偏移
      scale: 1,       // 缩放比例
      rotation: 0,    // 旋转角度（弧度）
      flipH: false,   // 水平翻转
      flipV: false    // 垂直翻转
    },

    // 触摸交互状态
    touchState: {
      touching: false,
      startDistance: 0,
      startScale: 1,
      startX: 0,
      startY: 0,
      lastTouchX: 0,
      lastTouchY: 0
    },



    // Canvas 2D 相关
    canvas: null,
    ctx: null,
    tempCanvas: null,
    tempCtx: null,

    // 缓存的图片对象，用于快速渲染
    cachedOriginalImg: null,
    cachedBorderImg: null,

    // 渲染节流
    renderTimer: null,

    // 保存状态
    saving: false,

    // 当前选中的类别
    currentCategory: 'image', // image, border, sticker, ai, save

    // 当前显示的面（正面/反面）
    currentSide: 'front', // front, back

    // 边框选择弹窗相关
    showBorderModal: false,
    currentBorderCategory: 'all',
    borderCategories: [],
    allBorderImages: [],
    currentBorderImages: [],

    // 卡背选择弹窗相关
    showCardBackModal: false,
    currentCardBackCategory: 'all',
    cardBackCategories: [],
    allCardBackImages: [],
    currentCardBackImages: [],

    // 弹窗状态控制
    modalActive: false
  },

  // 根据产品尺寸计算编辑区域大小
  calculateEditAreaSize(productSize) {
    // 默认尺寸（rpx）
    let editAreaWidth = 540;
    let editAreaHeight = 740;

    if (productSize) {
      // 清理尺寸字符串，移除可能的单位（如mm）
      let cleanSize = productSize.replace(/mm$/i, '').trim();

      // 解析产品尺寸，支持多种格式：
      // "90x125", "90*125", "50x50", "50*50" 等
      let sizeParts = [];
      if (cleanSize.includes('x')) {
        sizeParts = cleanSize.split('x');
      } else if (cleanSize.includes('*')) {
        sizeParts = cleanSize.split('*');
      }

      if (sizeParts.length === 2) {
        const widthMm = parseFloat(sizeParts[0]);
        const heightMm = parseFloat(sizeParts[1]);

        if (widthMm > 0 && heightMm > 0) {
          // 将mm转换为rpx，这里使用一个合理的比例
          // 假设90mm对应540rpx，计算比例
          const mmToRpxRatio = 1080 / 90; // 6 rpx/mm

          editAreaWidth = Math.round(widthMm * mmToRpxRatio);
          editAreaHeight = Math.round(heightMm * mmToRpxRatio);

          console.log('产品尺寸计算:', {
            originalProductSize: productSize,
            cleanSize,
            widthMm,
            heightMm,
            editAreaWidth,
            editAreaHeight,
            mmToRpxRatio
          });
        } else {
          console.warn('产品尺寸解析失败，宽度或高度无效:', { widthMm, heightMm });
        }
      } else {
        console.warn('产品尺寸格式不正确，无法解析:', productSize);
      }
    } else {
      console.log('未提供产品尺寸，使用默认尺寸');
    }

    return { editAreaWidth, editAreaHeight };
  },

  onLoad(options) {
    // 获取传入的参数
    const {
      frontImage = '',
      backImage = '',
      borderImage = '',
      cardBackImage = '',
      editType = 'border',
      groupIndex = '',
      imgIndex = '',
      currentSide = 'front',
      productSize = ''
    } = options;

    // 解码URL参数
    const decodedFront = frontImage ? decodeURIComponent(frontImage) : '';
    const decodedBack = backImage ? decodeURIComponent(backImage) : '';
    const decodedBorder = borderImage ? decodeURIComponent(borderImage) : '';
    const decodedCardBack = cardBackImage ? decodeURIComponent(cardBackImage) : '';

    // 根据当前面确定显示的图片（不受编辑类型影响）
    let originalImage;
    if (currentSide === 'front') {
      originalImage = decodedFront;
    } else {
      // 反面：如果有卡背图片则使用卡背，否则使用反面图片
      originalImage = decodedCardBack || decodedBack;
    }

    // 解析产品尺寸
    const decodedProductSize = productSize ? decodeURIComponent(productSize) : '';

    console.log('页面加载参数:', {
      productSize,
      decodedProductSize,
      editType,
      currentSide
    });

    this.setData({
      frontImage: decodedFront,
      backImage: decodedBack,
      originalImage: originalImage,
      borderImage: decodedBorder,
      cardBackImage: decodedCardBack,
      editType,
      groupIndex: groupIndex ? Number(groupIndex) : null,
      imgIndex: imgIndex ? Number(imgIndex) : null,
      currentSide: currentSide,
      productSize: decodedProductSize
    });

    // 根据产品尺寸计算编辑区域大小
    const { editAreaWidth, editAreaHeight } = this.calculateEditAreaSize(decodedProductSize);

    // 根据设备屏幕计算画布尺寸
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const rpxToPx = screenWidth / 750;

    // 计算画布尺寸：编辑区域 + 装订线宽度
    const bleedWidth = 20; // 装订线宽度（rpx）
    const canvasWidthRpx = editAreaWidth + bleedWidth * 2;
    const canvasHeightRpx = editAreaHeight + bleedWidth * 2;

    const canvasWidth = Math.round(canvasWidthRpx * rpxToPx);
    const canvasHeight = Math.round(canvasHeightRpx * rpxToPx);

    this.setData({
      editAreaWidth,
      editAreaHeight,
      canvasWidth,
      canvasHeight,
      bleedWidth,
      // 初始化底图变换状态
      'imageTransform.x': 0,
      'imageTransform.y': 0,
      'imageTransform.scale': 1,
      'imageTransform.rotation': 0
    });

    // 如果有边框图片，需要先获取边框的inner_frame_size信息
    if (decodedBorder) {
      this.loadBorderFrameSize(decodedBorder).then(() => {
        // 初始化Canvas 2D
        this.initCanvas().then(() => {
          // 延迟执行合成，确保画布已渲染
          setTimeout(() => {
            this.composeImage();
          }, 500);
        });
      });
    } else {
      // 初始化Canvas 2D
      this.initCanvas().then(() => {
        // 延迟执行合成，确保画布已渲染
        setTimeout(() => {
          this.composeImage();
        }, 500);
      });
    }
  },

  // 加载边框的inner_frame_size信息
  async loadBorderFrameSize(borderImageUrl) {
    try {
      console.log('加载边框frame size信息:', borderImageUrl);

      // 从API获取边框数据
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: config.rootUrl + '/borders',
          method: 'GET',
          success: (res) => {
            if (res.data.code === 200) {
              resolve(res.data.data);
            } else {
              reject(new Error('API返回错误'));
            }
          },
          fail: reject
        });
      });

      // 查找匹配的边框
      const { getImageUrl } = require('../../config/setting');
      const matchedBorder = response.find(border => {
        const fullUrl = getImageUrl(border.image_url);
        return fullUrl === borderImageUrl || border.image_url === borderImageUrl;
      });

      if (matchedBorder && matchedBorder.inner_frame_size) {
        const sizeParts = matchedBorder.inner_frame_size.split('x');
        if (sizeParts.length === 2) {
          const innerFrameWidth = parseInt(sizeParts[0]) || 270;
          const innerFrameHeight = parseInt(sizeParts[1]) || 360;

          console.log('找到边框frame size:', {
            border: matchedBorder.name,
            inner_frame_size: matchedBorder.inner_frame_size,
            innerFrameWidth,
            innerFrameHeight
          });

          this.setData({
            currentBorderData: matchedBorder,
            innerFrameWidth: innerFrameWidth,
            innerFrameHeight: innerFrameHeight
          });
        }
      } else {
        console.log('未找到匹配的边框或inner_frame_size，使用默认值');
      }
    } catch (error) {
      console.error('加载边框frame size失败:', error);
      // 使用默认值
    }
  },

  // 初始化Canvas 2D
  async initCanvas() {
    try {
      console.log('开始初始化Canvas');

      // 获取主画布
      const query = wx.createSelectorQuery().in(this);
      const canvas = await new Promise((resolve, reject) => {
        query.select('#imageCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            console.log('Canvas查询结果:', res);
            if (res[0] && res[0].node) {
              resolve(res[0].node);
            } else {
              reject(new Error('获取画布失败'));
            }
          });
      });

      // 获取临时画布
      const tempCanvas = await new Promise((resolve, reject) => {
        query.select('#tempCanvas')
          .fields({ node: true, size: true })
          .exec((res) => {
            console.log('临时Canvas查询结果:', res);
            if (res[0] && res[0].node) {
              resolve(res[0].node);
            } else {
              reject(new Error('获取临时画布失败'));
            }
          });
      });

      const ctx = canvas.getContext('2d');
      const tempCtx = tempCanvas.getContext('2d');

      console.log('Canvas上下文获取成功:', { ctx: !!ctx, tempCtx: !!tempCtx });

      // 设置画布尺寸
      const dpr = wx.getSystemInfoSync().pixelRatio;
      canvas.width = this.data.canvasWidth * dpr;
      canvas.height = this.data.canvasHeight * dpr;
      ctx.scale(dpr, dpr);

      tempCanvas.width = this.data.canvasWidth * dpr;
      tempCanvas.height = this.data.canvasHeight * dpr;
      tempCtx.scale(dpr, dpr);

      console.log('Canvas尺寸设置完成:', {
        canvasSize: `${canvas.width}x${canvas.height}`,
        tempCanvasSize: `${tempCanvas.width}x${tempCanvas.height}`,
        dpr
      });

      this.setData({
        canvas,
        ctx,
        tempCanvas,
        tempCtx
      });

      console.log('Canvas初始化完成');

    } catch (error) {
      console.error('Canvas 2D 初始化失败:', error);
      wx.showToast({
        title: 'Canvas初始化失败',
        icon: 'none'
      });
    }
  },

  // 下载网络图片到本地或处理本地图片
  downloadImage(url) {
    return new Promise((resolve, reject) => {
      if (!url) {
        reject(new Error('图片URL为空'));
        return;
      }

      // 如果是本地路径（以 / 开头）或临时文件（wxfile://），直接返回
      if (url.startsWith('/') || url.startsWith('wxfile://')) {
        resolve(url);
        return;
      }

      // 如果是网络图片，进行下载
      wx.downloadFile({
        url: url,
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.tempFilePath);
          } else {
            reject(new Error(`下载失败，状态码：${res.statusCode}`));
          }
        },
        fail: (err) => {
          console.error('图片下载失败:', err);
          reject(err);
        }
      });
    });
  },

  // 加载图片为Canvas Image对象
  loadCanvasImage(src) {
    return new Promise((resolve, reject) => {
      if (!this.data.canvas) {
        reject(new Error('Canvas未初始化'));
        return;
      }

      const img = this.data.canvas.createImage();
      img.onload = () => resolve(img);
      img.onerror = (err) => reject(err);
      img.src = src;
    });
  },



  // 绘制边框效果：直接叠加已镂空的边框图片
  drawBorderOverlay(ctx, originalImage, borderImage, canvasWidth, canvasHeight) {
    // 清空画布并设置背景为白色
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // 保存当前画布状态
    ctx.save();

    // 应用底图变换
    const { imageTransform, editAreaWidth, editAreaHeight, bleedWidth } = this.data;

    // 计算rpx到px的转换比例
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const rpxToPx = screenWidth / 750;

    // 计算编辑区域的实际像素尺寸
    const actualEditWidth = editAreaWidth * rpxToPx;
    const actualEditHeight = editAreaHeight * rpxToPx;
    const actualBleedWidth = bleedWidth * rpxToPx;

    // 计算编辑区域在画布中的位置（居中，考虑装订线）
    const editAreaX = actualBleedWidth;
    const editAreaY = actualBleedWidth;

    console.log('绘制边框 - 编辑区域尺寸:', {
      editAreaWidth,
      editAreaHeight,
      actualEditWidth,
      actualEditHeight,
      editAreaX,
      editAreaY,
      bleedWidth,
      actualBleedWidth
    });

    // 用户图片等比例缩放填充满编辑区域
    let drawWidth = actualEditWidth;
    let drawHeight = actualEditHeight;

    // 移动到编辑区域的中心
    ctx.translate(editAreaX + actualEditWidth / 2, editAreaY + actualEditHeight / 2);

    // 应用用户的变换
    ctx.translate(imageTransform.x, imageTransform.y);
    ctx.rotate(imageTransform.rotation);

    // 应用翻转和缩放
    const transformScaleX = imageTransform.flipH ? -imageTransform.scale : imageTransform.scale;
    const transformScaleY = imageTransform.flipV ? -imageTransform.scale : imageTransform.scale;
    ctx.scale(transformScaleX, transformScaleY);

    // 移回图片中心
    ctx.translate(-drawWidth / 2, -drawHeight / 2);

    // 绘制变换后的原图
    ctx.drawImage(originalImage, 0, 0, drawWidth, drawHeight);

    // 恢复画布状态
    ctx.restore();

    // 边框填充满整个画布（包括装订线区域，非等比例，保证边框完整性）
    ctx.drawImage(borderImage, 0, 0, canvasWidth, canvasHeight);
  },

  // 绘制卡背图片并应用变换效果
  drawCardBackWithTransform(ctx, cardBackImage, canvasWidth, canvasHeight) {
    // 保存当前画布状态
    ctx.save();

    // 应用底图变换
    const { imageTransform, editAreaWidth, editAreaHeight, bleedWidth } = this.data;

    // 计算rpx到px的转换比例
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const rpxToPx = screenWidth / 750;

    // 计算编辑区域的实际像素尺寸
    const actualEditWidth = editAreaWidth * rpxToPx;
    const actualEditHeight = editAreaHeight * rpxToPx;
    const actualBleedWidth = bleedWidth * rpxToPx;

    // 计算编辑区域在画布中的位置（居中，考虑装订线）
    const editAreaX = actualBleedWidth;
    const editAreaY = actualBleedWidth;

    // 卡背图片等比例缩放填充满编辑区域
    let drawWidth = actualEditWidth;
    let drawHeight = actualEditHeight;

    // 移动到编辑区域的中心
    ctx.translate(editAreaX + actualEditWidth / 2, editAreaY + actualEditHeight / 2);

    // 应用用户的变换
    ctx.translate(imageTransform.x, imageTransform.y);
    ctx.rotate(imageTransform.rotation);

    // 应用翻转和缩放
    const transformScaleX = imageTransform.flipH ? -imageTransform.scale : imageTransform.scale;
    const transformScaleY = imageTransform.flipV ? -imageTransform.scale : imageTransform.scale;
    ctx.scale(transformScaleX, transformScaleY);

    // 移回图片中心
    ctx.translate(-drawWidth / 2, -drawHeight / 2);

    // 绘制变换后的卡背图片
    ctx.drawImage(cardBackImage, 0, 0, drawWidth, drawHeight);

    // 恢复画布状态
    ctx.restore();
  },

  // 绘制纯图片并应用变换效果（没有边框的情况）
  drawImageWithTransform(ctx, image, canvasWidth, canvasHeight) {
    // 保存当前画布状态
    ctx.save();

    // 应用底图变换
    const { imageTransform, editAreaWidth, editAreaHeight, bleedWidth } = this.data;

    // 计算rpx到px的转换比例
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const rpxToPx = screenWidth / 750;

    // 计算编辑区域的实际像素尺寸
    const actualEditWidth = editAreaWidth * rpxToPx;
    const actualEditHeight = editAreaHeight * rpxToPx;
    const actualBleedWidth = bleedWidth * rpxToPx;

    // 计算编辑区域在画布中的位置（居中，考虑装订线）
    const editAreaX = actualBleedWidth;
    const editAreaY = actualBleedWidth;

    // 图片等比例缩放填充满编辑区域
    let drawWidth = actualEditWidth;
    let drawHeight = actualEditHeight;

    // 移动到编辑区域的中心
    ctx.translate(editAreaX + actualEditWidth / 2, editAreaY + actualEditHeight / 2);

    // 应用用户的变换
    ctx.translate(imageTransform.x, imageTransform.y);
    ctx.rotate(imageTransform.rotation);

    // 应用翻转和缩放
    const transformScaleX = imageTransform.flipH ? -imageTransform.scale : imageTransform.scale;
    const transformScaleY = imageTransform.flipV ? -imageTransform.scale : imageTransform.scale;
    ctx.scale(transformScaleX, transformScaleY);

    // 移回图片中心
    ctx.translate(-drawWidth / 2, -drawHeight / 2);

    // 绘制变换后的图片
    ctx.drawImage(image, 0, 0, drawWidth, drawHeight);

    // 恢复画布状态
    ctx.restore();
  },

  // 合成图片
  async composeImage() {
    const { originalImage, borderImage, cardBackImage, editType, canvasWidth, canvasHeight, ctx } = this.data;


    if (!ctx) {
      console.error('Canvas未初始化');
      wx.showToast({
        title: 'Canvas未初始化',
        icon: 'none'
      });
      return;
    }

    // 清空画布并设置背景为白色
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    try {
      if (editType === 'cardback') {
        // 卡背模式：使用当前显示的图片（已经在切换面时正确设置）
        if (originalImage) {
          const localImage = await this.downloadImage(originalImage);
          const img = await this.loadCanvasImage(localImage);

          // 缓存图片以供快速渲染使用
          this.setData({
            cachedOriginalImg: img
          });

          // 应用编辑操作到图片
          this.drawCardBackWithTransform(ctx, img, canvasWidth, canvasHeight);
        } else {
          wx.showToast({
            title: '缺少图片',
            icon: 'none'
          });
        }
        return;
      }
    } catch (error) {
      console.error('图片处理失败:', error);
      wx.showToast({
        title: '图片加载失败',
        icon: 'none'
      });
      return;
    }

    // 边框模式：需要原图，边框可选
    if (!originalImage) {
      wx.showToast({
        title: '缺少原始图片',
        icon: 'none'
      });
      return;
    }

    try {


      // 下载原图（必需）
      const localOriginal = await this.downloadImage(originalImage);
      const originalImg = await this.loadCanvasImage(localOriginal);

      // 缓存原图
      this.setData({
        cachedOriginalImg: originalImg
      });

      if (borderImage) {
        // 如果有边框，下载并应用边框
        const localBorder = await this.downloadImage(borderImage);
        const borderImg = await this.loadCanvasImage(localBorder);

        console.log('图片加载到Canvas完成:', {
          originalImgSize: `${originalImg.width}x${originalImg.height}`,
          borderImgSize: `${borderImg.width}x${borderImg.height}`
        });

        // 缓存边框图片
        this.setData({
          cachedBorderImg: borderImg
        });

        // 绘制带边框的图片
        this.drawBorderOverlay(ctx, originalImg, borderImg, canvasWidth, canvasHeight);
      } else {
        // 没有边框，直接绘制用户图片（可以进行变换操作）
        console.log('没有边框，直接绘制用户图片:', {
          originalImgSize: `${originalImg.width}x${originalImg.height}`
        });

        // 绘制纯图片（支持所有变换操作）
        this.drawImageWithTransform(ctx, originalImg, canvasWidth, canvasHeight);
      }

      console.log('图片合成完成');

    } catch (error) {
      console.error('图片处理失败:', error);
      console.error('错误详情:', {
        message: error.message,
        stack: error.stack,
        originalImage,
        borderImage
      });
      wx.showToast({
        title: `图片加载失败: ${error.message}`,
        icon: 'none',
        duration: 3000
      });
    }
  },

  // 上传图片到服务器
  uploadImageToServer(tempFilePath) {
    return new Promise((resolve, reject) => {
      wx.uploadFile({
        url: 'https://upload.fb-software.cn/miniapp/order_picture',
        filePath: tempFilePath,
        name: 'file',
        header: {
          'Content-Type': 'multipart/form-data'
        },
        success: (res) => {
          console.log('上传响应完整信息:', {
            statusCode: res.statusCode,
            data: res.data,
            header: res.header
          });

          // 检查HTTP状态码
          if (res.statusCode !== 200) {
            console.error('HTTP状态码错误:', res.statusCode);
            reject(new Error(`HTTP错误: ${res.statusCode}`));
            return;
          }

          // 检查响应数据是否存在
          if (!res.data) {
            console.error('响应数据为空');
            reject(new Error('服务器响应为空'));
            return;
          }

          // 直接使用正则表达式提取路径，不解析JSON
          console.log('原始响应数据:', res.data);

          if (typeof res.data === 'string') {
            // 使用正则表达式提取路径
            // 匹配格式: "File uploaded to /var/www/uploads/miniapp/order_picture/filename.png"
            const pathMatch = res.data.match(/\/var\/www\/uploads(\/miniapp\/order_picture\/[^\\s]+)/);

            if (pathMatch && pathMatch[1]) {
              const relativePath = pathMatch[1];
              const fullUrl = 'https://files.fb-software.cn' + relativePath;
              console.log('✅ 图片上传成功!');
              console.log('相对路径:', relativePath);
              console.log('完整访问URL:', fullUrl);
              resolve(relativePath);
            } else {
              // 尝试其他可能的路径格式
              const altMatch = res.data.match(/(\/miniapp\/order_picture\/[^\\s]+)/);
              if (altMatch && altMatch[1]) {
                const relativePath = altMatch[1];
                const fullUrl = 'https://files.fb-software.cn' + relativePath;
                console.log('✅ 图片上传成功!(备用匹配)');
                console.log('相对路径:', relativePath);
                console.log('完整访问URL:', fullUrl);
                resolve(relativePath);
              } else {
                console.error('无法从响应中提取路径:', res.data);
                reject(new Error('无法提取文件路径'));
              }
            }
          } else {
            console.error('响应数据不是字符串格式:', typeof res.data);
            reject(new Error('响应格式错误'));
          }
        },
        fail: (error) => {
          console.error('上传请求失败:', error);
          reject(new Error(`网络请求失败: ${error.errMsg || '未知错误'}`));
        }
      });
    });
  },

  // 保存合成后的图片
  saveImage() {
    // 防止重复保存
    if (this.data.saving) {
      return;
    }

    this.setData({ saving: true });

    const { canvasWidth, canvasHeight, canvas, tempCanvas, tempCtx, innerFrameWidth, innerFrameHeight, editType } = this.data;

    if (!canvas || !tempCanvas || !tempCtx) {
      wx.showToast({
        title: 'Canvas未初始化',
        icon: 'none'
      });
      this.setData({ saving: false });
      return;
    }

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    if (editType === 'border') {
      // 边框模式：保存完整的合成图片（图片+边框）
      this.saveFullCanvas();
    } else {
      // 卡背模式：保存整个画布
      this.saveFullCanvas();
    }
  },

  // 保存边框内部区域的图片
  async saveInnerAreaImage() {
    try {
      const { canvasWidth, canvasHeight, tempCanvas, tempCtx, innerFrameWidth, innerFrameHeight, cachedOriginalImg, imageTransform } = this.data;

      // 计算内部区域的实际尺寸和位置
      const canvasScaleX = canvasWidth / 540;
      const canvasScaleY = canvasHeight / 740;
      const actualInnerWidth = innerFrameWidth * canvasScaleX;
      const actualInnerHeight = innerFrameHeight * canvasScaleY;
      const innerAreaX = (canvasWidth - actualInnerWidth) / 2;
      const innerAreaY = (canvasHeight - actualInnerHeight) / 2;

      console.log('保存内部区域图片:', {
        innerFrameWidth,
        innerFrameHeight,
        actualInnerWidth,
        actualInnerHeight,
        innerAreaX,
        innerAreaY
      });

      // 设置临时画布尺寸为内部区域尺寸
      const dpr = wx.getSystemInfoSync().pixelRatio;
      tempCanvas.width = actualInnerWidth * dpr;
      tempCanvas.height = actualInnerHeight * dpr;
      tempCtx.scale(dpr, dpr);

      // 清空临时画布
      tempCtx.fillStyle = '#ffffff';
      tempCtx.fillRect(0, 0, actualInnerWidth, actualInnerHeight);

      if (cachedOriginalImg) {
        // 保存画布状态
        tempCtx.save();

        // 移动到画布中心
        tempCtx.translate(actualInnerWidth / 2, actualInnerHeight / 2);

        // 应用用户的变换
        tempCtx.translate(imageTransform.x, imageTransform.y);
        tempCtx.rotate(imageTransform.rotation);

        // 应用翻转和缩放
        const transformScaleX = imageTransform.flipH ? -imageTransform.scale : imageTransform.scale;
        const transformScaleY = imageTransform.flipV ? -imageTransform.scale : imageTransform.scale;
        tempCtx.scale(transformScaleX, transformScaleY);

        // 移回图片中心
        tempCtx.translate(-actualInnerWidth / 2, -actualInnerHeight / 2);

        // 绘制图片填满整个内部区域
        tempCtx.drawImage(cachedOriginalImg, 0, 0, actualInnerWidth, actualInnerHeight);

        // 恢复画布状态
        tempCtx.restore();
      }

      // 将临时画布内容转为图片
      wx.canvasToTempFilePath({
        canvas: tempCanvas,
        width: actualInnerWidth,
        height: actualInnerHeight,
        destWidth: actualInnerWidth * 2, // 提高输出质量
        destHeight: actualInnerHeight * 2,
        success: async (res) => {
          const editAreaImagePath = res.tempFilePath;

          // 导出完整画布（包含装订线）用于上传服务器
          let serverPath = null;
          try {
            const { canvas, canvasWidth, canvasHeight } = this.data;
            const fullImagePath = await new Promise((resolve, reject) => {
              wx.canvasToTempFilePath({
                canvas: canvas,
                width: canvasWidth,
                height: canvasHeight,
                destWidth: canvasWidth * 2,
                destHeight: canvasHeight * 2,
                fileType: 'png',
                quality: 1,
                success: (fullRes) => {
                  console.log('完整画布导出成功:', fullRes.tempFilePath);
                  resolve(fullRes.tempFilePath);
                },
                fail: (err) => {
                  console.error('完整画布导出失败:', err);
                  reject(err);
                }
              }, this);
            });

            // 上传完整画布到服务器
            serverPath = await this.uploadImageToServer(fullImagePath);
            console.log('完整图片上传成功:', serverPath);
          } catch (uploadError) {
            console.error('图片上传失败，仅保存本地:', uploadError);
          }

          // 返回编辑区域图片用于页面显示
          this.handleSaveSuccess(editAreaImagePath, serverPath);
        },
        fail: (err) => {
          this.handleSaveError(err);
        }
      }, this);

    } catch (error) {
      console.error('保存内部区域图片失败:', error);
      this.handleSaveError(error);
    }
  },

  // 保存完整画布
  async saveFullCanvas() {
    const { canvasWidth, canvasHeight, canvas, editAreaWidth, editAreaHeight, bleedWidth } = this.data;

    try {
      // 1. 导出包含装订线的完整图片（用于上传服务器）
      const fullImagePath = await new Promise((resolve, reject) => {
        wx.canvasToTempFilePath({
          canvas: canvas,
          width: canvasWidth,
          height: canvasHeight,
          destWidth: canvasWidth * 2, // 提高输出质量
          destHeight: canvasHeight * 2,
          fileType: 'png',
          quality: 1,
          success: (res) => {
            console.log('完整画布导出成功:', res.tempFilePath);
            resolve(res.tempFilePath);
          },
          fail: (err) => {
            console.error('完整画布导出失败:', err);
            reject(err);
          }
        }, this);
      });

      // 2. 导出编辑区域图片（用于页面显示）
      const systemInfo = wx.getSystemInfoSync();
      const screenWidth = systemInfo.windowWidth;
      const rpxToPx = screenWidth / 750;
      const actualBleedWidth = bleedWidth * rpxToPx;
      const actualEditWidth = editAreaWidth * rpxToPx;
      const actualEditHeight = editAreaHeight * rpxToPx;

      const editAreaImagePath = await new Promise((resolve, reject) => {
        wx.canvasToTempFilePath({
          canvas: canvas,
          x: actualBleedWidth,
          y: actualBleedWidth,
          width: actualEditWidth,
          height: actualEditHeight,
          destWidth: actualEditWidth * 2, // 提高输出质量
          destHeight: actualEditHeight * 2,
          fileType: 'png',
          quality: 1,
          success: (res) => {
            console.log('编辑区域导出成功:', res.tempFilePath);
            resolve(res.tempFilePath);
          },
          fail: (err) => {
            console.error('编辑区域导出失败:', err);
            reject(err);
          }
        }, this);
      });

      // 3. 上传完整图片到服务器
      let serverPath = null;
      try {
        serverPath = await this.uploadImageToServer(fullImagePath);
        console.log('完整图片上传成功:', serverPath);
      } catch (uploadError) {
        console.error('图片上传失败，仅保存本地:', uploadError);
        // 上传失败不影响本地保存，继续处理
      }

      // 4. 返回编辑区域图片用于页面显示，同时传递服务器路径
      this.handleSaveSuccess(editAreaImagePath, serverPath);

    } catch (err) {
      this.handleSaveError(err);
    }
  },

  // 处理保存成功
  handleSaveSuccess(tempFilePath, serverPath = null) {
    wx.hideLoading();
    this.setData({ saving: false });

    this.setData({
      finalImage: tempFilePath,
      serverImagePath: serverPath // 保存服务器路径
    });

    console.log('💾 保存成功:', {
      localPath: tempFilePath,
      serverPath: serverPath
    });

    if (serverPath) {
      const fullUrl = 'https://files.fb-software.cn' + serverPath;
      console.log('🌐 服务器图片完整URL:', fullUrl);
      console.log('📋 可复制此URL到浏览器查看图片:', fullUrl);
    }

    // 返回上一页并传递结果
    const pages = getCurrentPages();
    const prevPage = pages[pages.length - 2];

    if (prevPage) {
      const { groupIndex, imgIndex, editType, currentSide } = this.data;

      // 如果有分组和图片索引信息，直接更新对应位置的图片
      if (groupIndex !== null && imgIndex !== null && prevPage.updateEditedImage) {
        // 根据当前编辑的面来确定实际的图片索引
        let actualImgIndex = imgIndex;
        if (currentSide === 'back' && editType === 'border') {
          // 如果在边框模式下编辑反面，实际应该更新反面位置（索引1）
          actualImgIndex = 1;
        } else if (currentSide === 'front' && editType === 'cardback') {
          // 如果在卡背模式下编辑正面，实际应该更新正面位置（索引0）
          actualImgIndex = 0;
        }

        // 传递本地路径和服务器路径
        prevPage.updateEditedImage(tempFilePath, groupIndex, actualImgIndex, editType, serverPath);
      } else {
        // 兼容旧的方式
        prevPage.setData({
          editedImage: tempFilePath,
          editedImageServerPath: serverPath
        });
      }
    }

    wx.showToast({
      title: '保存并上传成功',
      icon: 'success'
    });

    // 延迟返回
    setTimeout(() => {
      wx.navigateBack();
    }, 1500);
  },

  // 处理保存错误
  handleSaveError(err) {
    wx.hideLoading();
    this.setData({ saving: false });
    console.error('保存图片失败:', err);
    wx.showToast({
      title: '保存失败',
      icon: 'none'
    });
  },

  // 重新选择边框
  selectBorder() {
    this.setData({
      showBorderModal: true,
      modalActive: true
    });
    this.loadBorderData();
  },

  // 重新选择卡背
  selectCardBack() {
    this.setData({
      showCardBackModal: true,
      modalActive: true
    });
    this.loadCardBackData();
  },

  // 重新选择贴纸（反面模式下使用卡背数据）
  selectSticker() {
    this.setData({
      showCardBackModal: true,
      modalActive: true
    });
    this.loadCardBackData(); // 使用卡背数据作为贴纸
  },

  // 去重函数 - 根据id去重
  removeDuplicateBorders(borderArray) {
    const seen = new Set();
    return borderArray.filter(border => {
      if (seen.has(border.id)) {
        console.log('发现重复边框，已过滤:', border.name, border.id);
        return false;
      }
      seen.add(border.id);
      return true;
    });
  },

  // 加载边框数据
  loadBorderData() {
    // 如果已经有边框数据，则不重复加载
    if (this.data.allBorderImages.length > 0) {
      console.log('边框数据已存在，跳过加载');
      return;
    }

    // 获取边框分类
    wx.request({
      url: config.rootUrl + '/border-categories',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.code === 200) {
          const categories = res.data.data.map(cat => ({
            ...cat,
            id: String(cat.category_id)
          }));
          this.setData({
            borderCategories: categories
          });
        } else {
          // 使用模拟数据
          this.setData({
            borderCategories: [
              { id: '5', name: '每日更新' },
              { id: '7', name: '夏日清凉' },
              { id: '9', name: '国色天香' }
            ]
          });
        }
      },
      fail: (err) => {
        console.error('获取边框分类失败:', err);
        // 使用模拟数据
        this.setData({
          borderCategories: [
            { id: '5', name: '每日更新' },
            { id: '7', name: '夏日清凉' },
            { id: '9', name: '国色天香' }
          ]
        });
      }
    });

    // 获取边框素材
    wx.request({
      url: config.rootUrl + '/borders',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200 || res.data.success === true) {
          // 图片category_id转字符串，并处理图片URL
          const images = res.data.data.map(img => ({
            ...img,
            category_id: String(img.category_id),
            image_url: getImageUrl(img.image_url),
            thumbnail_url: getImageUrl(img.thumbnail_url)
          }));
          // 去重处理
          const uniqueImages = this.removeDuplicateBorders(images);
          console.log('图片编辑器 - 去重后的边框素材:', uniqueImages);
          this.setData({
            allBorderImages: uniqueImages,
            currentBorderImages: uniqueImages
          });
        } else {
          // 使用模拟数据
          this.setData({
            allBorderImages: [
              { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
              { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
              { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
              { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
              { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
            ],
            currentBorderImages: [
              { id: '1', name: '边框1', image_url: '/images/borders/fg1.jpg', category_id: '5' },
              { id: '2', name: '边框2', image_url: '/images/borders/guang1.jpg', category_id: '5' },
              { id: '3', name: '边框3', image_url: '/images/borders/guang2.jpg', category_id: '7' },
              { id: '4', name: '边框4', image_url: '/images/borders/guang3.jpg', category_id: '7' },
              { id: '5', name: '边框5', image_url: '/images/borders/neiyu1.jpg', category_id: '9' }
            ]
          });
        }
      },
      fail: (err) => {
        console.error('获取边框图片失败:', err);
        // 使用模拟数据
        const mockBorderImages = [
          // 每日更新分类
          { id: 14, name: '008', image_url: '/miniapp/material/20250802170919_26682bd8.jpeg', category_id: '5' },
          { id: 13, name: '007', image_url: '/miniapp/material/20250802170903_b8b3ea65.jpg', category_id: '5' },
          { id: 12, name: '006', image_url: '/miniapp/material/20250802170851_c998b592.jpeg', category_id: '5' },
          // 夏日清凉分类
          { id: 9, name: '003', image_url: '/miniapp/material/20250802170811_d47d72a7.jpg', category_id: '7' },
          { id: 8, name: '002', image_url: '/miniapp/material/20250802170756_79f6a560.jpeg', category_id: '7' },
          { id: 7, name: '001', image_url: '/miniapp/material/20250802170736_eda208dd.jpg', category_id: '7' },
          // 国色天香分类
          { id: 10, name: '004', image_url: '/miniapp/material/20250802170821_e6e2de74.jpg', category_id: '9' },
          { id: 11, name: '005', image_url: '/miniapp/material/20250802170832_6dc7f998.jpg', category_id: '9' }
        ].map(item => ({
          ...item,
          image_url: getImageUrl(item.image_url)
        }));

        this.setData({
          allBorderImages: mockBorderImages,
          currentBorderImages: mockBorderImages
        });
      }
    });
  },

  // 加载卡背数据
  loadCardBackData() {

    // 获取卡背分类
    wx.request({
      url: config.rootUrl + '/card-back-categories',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.code === 200) {
          const categories = res.data.data.map(cat => ({
            ...cat,
            id: String(cat.category_id)
          }));
          this.setData({
            cardBackCategories: categories
          });
        } else {
          // 使用模拟数据
          this.setData({
            cardBackCategories: [
              { id: '6', name: '每日更新' },
              { id: '8', name: '夏日清凉' },
              { id: '10', name: '国色天香' }
            ]
          });
        }
      },
      fail: (err) => {
        console.error('获取卡背分类失败:', err);
        // 使用模拟数据
        this.setData({
          cardBackCategories: [
            { id: '6', name: '每日更新' },
            { id: '8', name: '夏日清凉' },
            { id: '10', name: '国色天香' }
          ]
        });
      }
    });

    // 获取卡背素材
    wx.request({
      url: config.rootUrl + '/card-backs',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 200 || res.data.success === true) {
          // 图片category_id转字符串，并处理图片URL
          const images = res.data.data.map(img => ({
            ...img,
            category_id: String(img.category_id),
            image_url: getImageUrl(img.image_url),
            thumbnail_url: getImageUrl(img.thumbnail_url)
          }));
          this.setData({
            allCardBackImages: images,
            currentCardBackImages: images
          });
        } else {
          // 使用模拟数据
          const mockCardBackImages = [
            // 每日更新分类
            { id: 27, name: '020', image_url: '/miniapp/material/20250802171209_2504b0fe.jpg', category_id: '6' },
            { id: 26, name: '019', image_url: '/miniapp/material/20250802171156_88a4499c.jpeg', category_id: '6' },
            { id: 25, name: '018', image_url: '/miniapp/material/20250802171145_1bce21a1.jpeg', category_id: '6' },
            // 夏日清凉分类
            { id: 23, name: '016', image_url: '/miniapp/material/20250802171122_ecb036eb.jpeg', category_id: '8' },
            { id: 22, name: '015', image_url: '/miniapp/material/20250802171110_e82bbbfc.jpeg', category_id: '8' },
            // 国色天香分类
            { id: 21, name: '014', image_url: '/miniapp/material/20250802171058_b5b8b8b8.jpeg', category_id: '10' },
            { id: 20, name: '013', image_url: '/miniapp/material/20250802171046_c9c9c9c9.jpeg', category_id: '10' }
          ].map(item => ({
            ...item,
            image_url: getImageUrl(item.image_url)
          }));

          this.setData({
            allCardBackImages: mockCardBackImages,
            currentCardBackImages: mockCardBackImages
          });
        }
      },
      fail: (err) => {
        console.error('获取卡背素材失败:', err);
        // 使用模拟数据
        const mockCardBackImages = [
          // 每日更新分类
          { id: 27, name: '020', image_url: '/miniapp/material/20250802171209_2504b0fe.jpg', category_id: '6' },
          { id: 26, name: '019', image_url: '/miniapp/material/20250802171156_88a4499c.jpeg', category_id: '6' },
          { id: 25, name: '018', image_url: '/miniapp/material/20250802171145_1bce21a1.jpeg', category_id: '6' },
          // 夏日清凉分类
          { id: 23, name: '016', image_url: '/miniapp/material/20250802171122_ecb036eb.jpeg', category_id: '8' },
          { id: 22, name: '015', image_url: '/miniapp/material/20250802171110_e82bbbfc.jpeg', category_id: '8' },
          // 国色天香分类
          { id: 21, name: '014', image_url: '/miniapp/material/20250802171058_b5b8b8b8.jpeg', category_id: '10' },
          { id: 20, name: '013', image_url: '/miniapp/material/20250802171046_c9c9c9c9.jpeg', category_id: '10' }
        ].map(item => ({
          ...item,
          image_url: getImageUrl(item.image_url)
        }));

        this.setData({
          allCardBackImages: mockCardBackImages,
          currentCardBackImages: mockCardBackImages
        });
      }
    });
  },

  // 关闭边框选择弹窗
  closeBorderModal() {
    this.setData({
      showBorderModal: false,
      modalActive: false
    });

    // Canvas被隐藏后重新显示，需要重新初始化以确保正常显示
    setTimeout(() => {
      if (this.data.ctx && this.data.cachedOriginalImg && this.data.cachedBorderImg) {
        // 如果有缓存的图片，直接重新绘制
        this.composeImage();
      }
    }, 300);
  },

  // 关闭卡背选择弹窗
  closeCardBackModal() {
    this.setData({
      showCardBackModal: false,
      modalActive: false
    });

    // Canvas被隐藏后重新显示，需要重新初始化以确保正常显示
    setTimeout(() => {
      if (this.data.ctx && (this.data.cachedOriginalImg || this.data.cardBackImage)) {
        // 如果有缓存的图片或卡背图片，直接重新绘制
        this.composeImage();
      }
    }, 300);
  },



  // 切换边框分类
  switchBorderCategory(e) {
    const category = e.currentTarget.dataset.category;
    let currentBorderImages = [];
    if (category === 'all') {
      currentBorderImages = this.data.allBorderImages;
    } else {
      currentBorderImages = this.data.allBorderImages.filter(item =>
        item.category_id === category
      );
    }

    // 确保去重
    const uniqueImages = this.removeDuplicateBorders(currentBorderImages);
    console.log(`图片编辑器 - 切换到分类 ${category}，去重前: ${currentBorderImages.length}，去重后: ${uniqueImages.length}`);

    this.setData({
      currentBorderCategory: category,
      currentBorderImages: uniqueImages
    });
  },

  // 切换卡背分类
  switchCardBackCategory(e) {
    const category = e.currentTarget.dataset.category;
    let currentCardBackImages = [];
    if (category === 'all') {
      currentCardBackImages = this.data.allCardBackImages;
    } else {
      currentCardBackImages = this.data.allCardBackImages.filter(item =>
        item.category_id === category
      );
    }
    this.setData({
      currentCardBackCategory: category,
      currentCardBackImages: currentCardBackImages
    });
  },

  // 选择新边框
  selectNewBorder(e) {
    const border = e.currentTarget.dataset.border;

    console.log('选择新边框:', border);
    console.log('当前原始图片:', this.data.originalImage);
    console.log('当前画布状态:', {
      canvas: !!this.data.canvas,
      ctx: !!this.data.ctx,
      canvasWidth: this.data.canvasWidth,
      canvasHeight: this.data.canvasHeight
    });

    // 检查原始图片是否存在
    if (!this.data.originalImage) {
      console.error('原始图片丢失，无法更换边框');
      wx.showToast({
        title: '原始图片丢失，请重新上传',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 解析边框的inner_frame_size
    let innerFrameWidth = 270; // 默认值
    let innerFrameHeight = 360; // 默认值

    if (border.inner_frame_size) {
      const sizeParts = border.inner_frame_size.split('x');
      if (sizeParts.length === 2) {
        innerFrameWidth = parseInt(sizeParts[0]) || 270;
        innerFrameHeight = parseInt(sizeParts[1]) || 360;
      }
    }

    console.log('边框内部区域尺寸:', {
      inner_frame_size: border.inner_frame_size,
      innerFrameWidth,
      innerFrameHeight
    });

    // 更新边框图片和内部区域尺寸
    this.setData({
      borderImage: border.image_url,
      currentBorderData: border,
      innerFrameWidth: innerFrameWidth,
      innerFrameHeight: innerFrameHeight,
      showBorderModal: false,
      modalActive: false,
      // 清除缓存的图片，强制重新加载
      cachedOriginalImg: null,
      cachedBorderImg: null
    });

    // Canvas被隐藏后重新显示，需要等待DOM更新完成后再重新初始化
    // 使用更长的延迟确保Canvas完全重新显示
    setTimeout(() => {
      console.log('开始重新初始化Canvas和合成图片');
      this.initCanvas().then(() => {
        console.log('Canvas初始化完成，开始合成图片');
        // 再次延迟确保Canvas完全准备好
        setTimeout(() => {
          this.composeImage();
        }, 100);
      }).catch(error => {
        console.error('Canvas初始化失败:', error);
        wx.showToast({
          title: 'Canvas初始化失败',
          icon: 'none'
        });
      });
    }, 500); // 增加延迟时间从200ms到500ms

    wx.showToast({
      title: '边框已更换',
      icon: 'success',
      duration: 1000
    });
  },

  // 选择新卡背
  selectNewCardBack(e) {
    const cardBack = e.currentTarget.dataset.cardback;

    console.log('选择新卡背:', cardBack);
    console.log('当前原始图片:', this.data.originalImage);

    // 更新卡背图片和原始图片
    this.setData({
      cardBackImage: cardBack.image_url,
      originalImage: cardBack.image_url, // 在卡背模式下，原始图片就是卡背图片
      showCardBackModal: false,
      modalActive: false,
      // 清除缓存的图片，强制重新加载
      cachedOriginalImg: null,
      cachedBorderImg: null
    });

    // Canvas被隐藏后重新显示，需要等待DOM更新完成后再重新初始化
    setTimeout(() => {
      console.log('开始重新初始化Canvas和合成图片');
      this.initCanvas().then(() => {
        console.log('Canvas初始化完成，开始合成图片');
        setTimeout(() => {
          this.composeImage();
        }, 100);
      }).catch(error => {
        console.error('Canvas初始化失败:', error);
        wx.showToast({
          title: 'Canvas初始化失败',
          icon: 'none'
        });
      });
    }, 500); // 增加延迟时间

    wx.showToast({
      title: '卡背已更换',
      icon: 'success',
      duration: 1000
    });
  },



  // 触摸开始事件
  onTouchStart(e) {
    const touches = e.touches || [];
    const { imageTransform } = this.data;

    if (touches.length === 1) {
      // 单指拖动
      this.setData({
        'touchState.touching': true,
        'touchState.lastTouchX': touches[0].x,
        'touchState.lastTouchY': touches[0].y,
        'touchState.startX': imageTransform.x,
        'touchState.startY': imageTransform.y
      });
    } else if (touches.length === 2) {
      // 双指缩放（移除旋转功能）
      const touch1 = touches[0];
      const touch2 = touches[1];

      const distance = Math.sqrt(
        Math.pow(touch2.x - touch1.x, 2) + Math.pow(touch2.y - touch1.y, 2)
      );

      this.setData({
        'touchState.touching': true,
        'touchState.startDistance': distance,
        'touchState.startScale': imageTransform.scale,
        'touchState.startX': imageTransform.x,
        'touchState.startY': imageTransform.y
      });
    }
  },

  // 延迟渲染方法 - 只在操作暂停后渲染
  delayedRender() {
    if (this.data.renderTimer) {
      clearTimeout(this.data.renderTimer);
    }

    this.setData({
      renderTimer: setTimeout(() => {
        this.composeImage();
        this.setData({ renderTimer: null });
      }, 200) // 操作暂停200ms后再渲染，减少频繁刷新
    });
  },

  // 快速渲染图片变换 - 用于实时反馈，不重新加载图片
  renderImageTransform() {
    const { ctx, cachedOriginalImg, cachedBorderImg, canvasWidth, canvasHeight, editType } = this.data;

    if (!ctx || !cachedOriginalImg) {
      // 如果缓存的图片不存在，回退到完整渲染
      this.composeImage();
      return;
    }

    // 立即清空画布并绘制，确保即时反馈
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    if (editType === 'cardback') {
      // 卡背模式：使用卡背图片进行快速渲染
      this.drawCardBackWithTransform(ctx, cachedOriginalImg, canvasWidth, canvasHeight);
    } else if (cachedBorderImg) {
      // 边框模式：使用缓存的图片进行快速渲染
      this.drawBorderOverlayFromCache(ctx, cachedOriginalImg, cachedBorderImg, canvasWidth, canvasHeight);
    } else {
      // 没有边框：直接渲染用户图片
      this.drawImageWithTransform(ctx, cachedOriginalImg, canvasWidth, canvasHeight);
    }
  },

  // 使用缓存图片绘制边框效果
  drawBorderOverlayFromCache(ctx, originalImage, borderImage, canvasWidth, canvasHeight) {
    // 保存当前画布状态
    ctx.save();

    // 应用底图变换
    const { imageTransform, editAreaWidth, editAreaHeight, bleedWidth } = this.data;

    // 计算rpx到px的转换比例
    const systemInfo = wx.getSystemInfoSync();
    const screenWidth = systemInfo.windowWidth;
    const rpxToPx = screenWidth / 750;

    // 计算编辑区域的实际像素尺寸
    const actualEditWidth = editAreaWidth * rpxToPx;
    const actualEditHeight = editAreaHeight * rpxToPx;
    const actualBleedWidth = bleedWidth * rpxToPx;

    // 计算编辑区域在画布中的位置（居中，考虑装订线）
    const editAreaX = actualBleedWidth;
    const editAreaY = actualBleedWidth;

    // 用户图片等比例缩放填充满编辑区域
    let drawWidth = actualEditWidth;
    let drawHeight = actualEditHeight;

    // 移动到编辑区域的中心
    ctx.translate(editAreaX + actualEditWidth / 2, editAreaY + actualEditHeight / 2);

    // 应用用户的变换
    ctx.translate(imageTransform.x, imageTransform.y);
    ctx.rotate(imageTransform.rotation);

    // 应用翻转和缩放
    const transformScaleX = imageTransform.flipH ? -imageTransform.scale : imageTransform.scale;
    const transformScaleY = imageTransform.flipV ? -imageTransform.scale : imageTransform.scale;
    ctx.scale(transformScaleX, transformScaleY);

    // 移回图片中心
    ctx.translate(-drawWidth / 2, -drawHeight / 2);

    // 绘制变换后的原图
    ctx.drawImage(originalImage, 0, 0, drawWidth, drawHeight);

    // 恢复画布状态
    ctx.restore();

    // 边框填充满整个画布（包括装订线区域，非等比例，保证边框完整性）
    ctx.drawImage(borderImage, 0, 0, canvasWidth, canvasHeight);
  },

  // 触摸移动事件
  onTouchMove(e) {
    const touches = e.touches || [];
    const { touchState, imageTransform } = this.data;

    if (!touchState.touching) return;

    let needsRender = false;

    if (touches.length === 1) {
      // 单指拖动
      const deltaX = touches[0].x - touchState.lastTouchX;
      const deltaY = touches[0].y - touchState.lastTouchY;

      this.setData({
        'imageTransform.x': imageTransform.x + deltaX,
        'imageTransform.y': imageTransform.y + deltaY,
        'touchState.lastTouchX': touches[0].x,
        'touchState.lastTouchY': touches[0].y
      });

      needsRender = true;

    } else if (touches.length === 2) {
      // 双指缩放（移除旋转功能）
      const touch1 = touches[0];
      const touch2 = touches[1];

      const distance = Math.sqrt(
        Math.pow(touch2.x - touch1.x, 2) + Math.pow(touch2.y - touch1.y, 2)
      );

      // 防止除零错误
      if (touchState.startDistance <= 0) {
        return;
      }

      // 计算缩放比例
      const scaleRatio = distance / touchState.startDistance;
      const newScale = Math.max(0.5, Math.min(3, touchState.startScale * scaleRatio));

      this.setData({
        'imageTransform.scale': newScale
      });

      needsRender = true;
    }

    // 实时渲染以提供即时反馈
    if (needsRender) {
      this.renderImageTransform();
    }
  },

  // 触摸结束事件
  onTouchEnd() {
    this.setData({
      'touchState.touching': false
    });

    // 取消待执行的渲染
    if (this.data.renderTimer) {
      clearTimeout(this.data.renderTimer);
      this.setData({ renderTimer: null });
    }

    // 触摸结束时延迟一点渲染最终结果，避免卡顿感
    const renderTimer = setTimeout(() => {
      this.composeImage();
      this.setData({ renderTimer: null });
    }, 50); // 50ms延迟，既保证及时更新又避免卡顿

    this.setData({ renderTimer });
  },

  // 旋转图片90度
  rotateImage() {
    const { imageTransform } = this.data;
    const newRotation = imageTransform.rotation + Math.PI / 2; // 90度 = π/2 弧度

    this.setData({
      'imageTransform.rotation': newRotation
    });

    // 重新绘制
    this.composeImage();

    // 显示提示
    wx.showToast({
      title: '已旋转90°',
      icon: 'none',
      duration: 1000
    });
  },

  // 水平翻转
  flipHorizontal() {
    const { imageTransform } = this.data;

    this.setData({
      'imageTransform.flipH': !imageTransform.flipH
    });

    // 重新绘制
    this.composeImage();

    // 显示提示
    wx.showToast({
      title: imageTransform.flipH ? '取消水平翻转' : '水平翻转',
      icon: 'none',
      duration: 1000
    });
  },

  // 垂直翻转
  flipVertical() {
    const { imageTransform } = this.data;

    this.setData({
      'imageTransform.flipV': !imageTransform.flipV
    });

    // 重新绘制
    this.composeImage();

    // 显示提示
    wx.showToast({
      title: imageTransform.flipV ? '取消垂直翻转' : '垂直翻转',
      icon: 'none',
      duration: 1000
    });
  },







  // 重置底图变换
  resetTransform() {
    this.setData({
      'imageTransform.x': 0,
      'imageTransform.y': 0,
      'imageTransform.scale': 1,
      'imageTransform.rotation': 0,
      'imageTransform.flipH': false,
      'imageTransform.flipV': false
    });

    // 重新绘制
    this.composeImage();

    // 显示提示
    wx.showToast({
      title: '已重置位置',
      icon: 'none',
      duration: 1000
    });
  },

  // 切换到底图类别
  switchToImage() {
    this.setData({
      currentCategory: 'image'
    });
  },

  // 切换到边框类别
  switchToBorder() {
    this.setData({
      currentCategory: 'border'
    });
  },

  // 切换到贴纸类别
  switchToSticker() {
    this.setData({
      currentCategory: 'sticker'
    });
    wx.showToast({
      title: '贴纸功能开发中',
      icon: 'none'
    });
  },

  // 切换到AI抠图类别
  switchToAI() {
    this.setData({
      currentCategory: 'ai'
    });
    wx.showToast({
      title: 'AI抠图功能开发中',
      icon: 'none'
    });
  },

  // 切换到正面
  switchToFront() {
    const { frontImage } = this.data;

    if (!frontImage) {
      wx.showToast({
        title: '没有正面图片',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    this.setData({
      currentSide: 'front',
      originalImage: frontImage, // 正面始终使用正面图片
      editType: 'border', // 正面始终使用边框模式
      // 重置变换状态
      'imageTransform.x': 0,
      'imageTransform.y': 0,
      'imageTransform.scale': 1,
      'imageTransform.rotation': 0,
      'imageTransform.flipH': false,
      'imageTransform.flipV': false,
      // 清除缓存的图片，强制重新加载
      cachedOriginalImg: null
    });

    // 重新合成图片
    this.composeImage();

    wx.showToast({
      title: '切换到正面',
      icon: 'none',
      duration: 1000
    });
  },

  // 切换到反面
  switchToBack() {
    const { backImage, cardBackImage } = this.data;

    // 反面：优先使用卡背图片，否则使用反面图片
    const imageToUse = cardBackImage || backImage;

    if (!imageToUse) {
      wx.showToast({
        title: '没有反面图片',
        icon: 'none',
        duration: 1000
      });
      return;
    }

    this.setData({
      currentSide: 'back',
      originalImage: imageToUse,
      editType: 'cardback', // 反面始终使用卡背模式
      // 重置变换状态
      'imageTransform.x': 0,
      'imageTransform.y': 0,
      'imageTransform.scale': 1,
      'imageTransform.rotation': 0,
      'imageTransform.flipH': false,
      'imageTransform.flipV': false,
      // 清除缓存的图片，强制重新加载
      cachedOriginalImg: null
    });

    // 重新合成图片
    this.composeImage();

    wx.showToast({
      title: '切换到反面',
      icon: 'none',
      duration: 1000
    });
  },

  // 重新上传图片
  reuploadImage() {
    const { currentSide } = this.data;

    if (currentSide === 'back') {
      // 反面模式：重新上传卡背
      this.reuploadCardBack();
    } else {
      // 正面模式：重新上传底图
      this.reuploadFrontImage();
    }
  },

  // 重新上传正面底图
  reuploadFrontImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          frontImage: tempFilePath,
          originalImage: tempFilePath,
          // 重置变换状态
          'imageTransform.x': 0,
          'imageTransform.y': 0,
          'imageTransform.scale': 1,
          'imageTransform.rotation': 0,
          'imageTransform.flipH': false,
          'imageTransform.flipV': false
        });

        // 重新合成图片
        this.composeImage();

        wx.showToast({
          title: '底图已更新',
          icon: 'success',
          duration: 1000
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 重新上传卡背
  reuploadCardBack() {
    wx.chooseImage({
      count: 1,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.setData({
          backImage: tempFilePath,
          originalImage: tempFilePath,
          cardBackImage: tempFilePath, // 同时更新卡背图片
          // 重置变换状态
          'imageTransform.x': 0,
          'imageTransform.y': 0,
          'imageTransform.scale': 1,
          'imageTransform.rotation': 0,
          'imageTransform.flipH': false,
          'imageTransform.flipV': false,
          // 清除缓存的图片，强制重新加载
          cachedOriginalImg: null,
          cachedBorderImg: null
        });

        // 重新合成图片
        this.composeImage();

        wx.showToast({
          title: '卡背已更新',
          icon: 'success',
          duration: 1000
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },


});
