/* pages/image-editor/image-editor.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 正面反面切换按钮 */
.side-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 10rpx 20rpx 5rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 50rpx;
  padding: 4rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.side-btn {
  flex: 1;
  max-width: 200rpx;
  height: 50rpx;
  border-radius: 50rpx;
  font-size: 26rpx;
  border: none;
  background-color: transparent;
  color: #666;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.side-btn.active {
  background-color: #ff6b9d;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.3);
}

.side-btn.disabled {
  background-color: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
  opacity: 0.6;
}

.side-btn:active {
  transform: scale(0.95);
}

.side-btn.disabled:active {
  transform: none;
}

/* 画布容器 */
.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 30rpx 15rpx 160rpx 15rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
  margin: 5rpx 15rpx 10rpx 15rpx;
  border-radius: 12rpx;
}

/* 卡片编辑区域 */
.card-edit-area {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15rpx;
}

/* 装订线区域 */
.bleed-area {
  position: relative;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

/* 装订线边框 */
.bleed-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 6rpx dashed #ff4757;
  border-radius: 12rpx;
  pointer-events: none;
  z-index: 10;
}

/* 装订线区域 */
.bleed-line {
  position: absolute;
  background-color: rgba(255, 71, 87, 0.3);
  border: 2rpx solid #ff4757;
  pointer-events: none;
  z-index: 15;
}

.bleed-line-top {
  top: 0;
  left: 0;
  right: 0;
  height: 30rpx;
  border-bottom: 2rpx dashed #ff4757;
  border-top: none;
  border-left: none;
  border-right: none;
}

.bleed-line-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  height: 30rpx;
  border-top: 2rpx dashed #ff4757;
  border-bottom: none;
  border-left: none;
  border-right: none;
}

.bleed-line-left {
  top: 0;
  bottom: 0;
  left: 0;
  width: 30rpx;
  border-right: 2rpx dashed #ff4757;
  border-top: none;
  border-bottom: none;
  border-left: none;
}

.bleed-line-right {
  top: 0;
  bottom: 0;
  right: 0;
  width: 30rpx;
  border-left: 2rpx dashed #ff4757;
  border-top: none;
  border-bottom: none;
  border-right: none;
}

/* 安全区域 */
.safe-area {
  position: absolute;
  top: 30rpx;
  left: 30rpx;
  right: 30rpx;
  bottom: 30rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background-color: #f8f9fa;
  z-index: 1;
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.image-canvas {
  width: 100% !important;
  height: 100% !important;
  background-color: transparent;
  position: relative;
  z-index: 2;
  touch-action: none;
  -webkit-touch-callout: none;
}



/* 装订线提示文字 */
.bleed-tips {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
}

.bleed-tip {
  position: absolute;
  font-size: 20rpx;
  color: #ff4757;
  font-weight: 500;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  white-space: nowrap;
}

.bleed-tip-top {
  top: 8rpx;
  left: 50%;
  transform: translateX(-50%);
}

.bleed-tip-bottom {
  bottom: 8rpx;
  left: 50%;
  transform: translateX(-50%);
}

.bleed-tip-left {
  left: 8rpx;
  top: 50%;
  transform: translateY(-50%) rotate(-90deg);
}

.bleed-tip-right {
  right: 8rpx;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
}

/* 卡片信息 */
.card-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 24rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  backdrop-filter: blur(10rpx);
}

.card-size {
  font-size: 28rpx;
  font-weight: 600;
  color: #2c3e50;
}

.card-desc {
  font-size: 22rpx;
  color: #7f8c8d;
}

.canvas-tip {
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #666;
  text-align: center;
  line-height: 1.4;
}

/* 底部操作区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1rpx solid #e5e5e5;
  padding: 10rpx 0 30rpx 0;
  z-index: 100;
}

/* 功能按钮行 */
.function-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10rpx 15rpx;
  gap: 15rpx;
  flex-wrap: wrap;
}

.function-btn {
  padding: 10rpx 24rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 40rpx;
  font-size: 24rpx;
  color: #495057;
  transition: all 0.2s ease;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.function-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

/* 主操作按钮行 */
.action-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 15rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: none;
  border: none;
  padding: 10rpx;
  min-width: 80rpx;
  transition: all 0.2s ease;
}

.action-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #8e8e93;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6rpx;
  transition: all 0.2s ease;
}

.action-item.active .action-icon {
  background-color: #ff6b9d;
}

.icon-text {
  font-size: 28rpx;
  color: white;
}

.action-text {
  font-size: 20rpx;
  color: #333;
  font-weight: 400;
}

.action-item.active .action-text {
  color: #ff6b9d;
  font-weight: 500;
}

.action-item:active {
  transform: scale(0.95);
}



/* 素材信息 */
.material-info {
  padding: 30rpx;
  background-color: #fff;
  margin-top: 20rpx;
}

.info-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 15rpx;
  font-weight: bold;
}

.info-content {
  font-size: 26rpx;
  color: #666;
  padding: 25rpx;
  background-color: #f8f8f8;
  border-radius: 12rpx;
  line-height: 1.6;
  border-left: 6rpx solid #007aff;
}

/* 边框选择弹窗样式 */
.modal-mask {
  position: fixed !important;
  left: 0 !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background: rgba(0,0,0,0.6) !important;
  z-index: 9999999 !important;
  display: block !important;
}

.border-modal {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 90% !important;
  max-width: 600rpx !important;
  max-height: 80vh !important;
  background: white !important;
  border-radius: 20rpx !important;
  z-index: 10000000 !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.3) !important;
  border: 2rpx solid #007aff !important;
}

/* 当弹窗显示时，降低编辑区域的层级 */
.container {
  position: relative;
  z-index: 1;
}

/* 弹窗显示时的样式覆盖 */
page {
  position: relative;
}

/* 当弹窗显示时，隐藏Canvas原生组件（因为Canvas层级最高，无法被z-index覆盖） */
.modal-active .image-canvas,
.modal-active .temp-canvas {
  display: none !important;
  visibility: hidden !important;
}

/* 同时降低其他元素的层级 */
.modal-active .bleed-area,
.modal-active .bleed-line,
.modal-active .bleed-overlay,
.modal-active .safe-area {
  z-index: -1 !important;
}



.modal-close {
  position: absolute;
  top: 20rpx;
  right: 30rpx;
  font-size: 48rpx;
  color: #999;
  z-index: 10000001 !important;
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.border-title {
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
  padding: 30rpx 0 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.category-tabs {
  display: flex;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
  overflow-x: auto;
  white-space: nowrap;
}

.category-tab {
  padding: 15rpx 25rpx;
  margin-right: 20rpx;
  background: #f5f5f5;
  border-radius: 25rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s;
}

.category-tab.active {
  background: #007aff;
  color: white;
}

.border-images {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}

.border-image-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  border-radius: 10rpx;
  background: #f9f9f9;
  transition: all 0.3s;
}

.border-image-item:active {
  background: #e0e0e0;
  transform: scale(0.95);
}

.border-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-bottom: 10rpx;
}

.border-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
  line-height: 1.2;
}






