// 测试Canvas 2D功能的简单脚本
// 可以在开发者工具的控制台中运行

function testCanvas2D() {
  console.log('开始测试Canvas 2D功能...');
  
  // 获取页面实例
  const page = getCurrentPages()[getCurrentPages().length - 1];
  
  if (!page || !page.data.canvas) {
    console.error('Canvas未初始化或页面不存在');
    return;
  }
  
  const { canvas, ctx, canvasWidth, canvasHeight } = page.data;
  
  // 测试基本绘制功能
  console.log('测试基本绘制...');
  ctx.fillStyle = '#ff0000';
  ctx.fillRect(10, 10, 50, 50);
  
  // 测试文字绘制
  console.log('测试文字绘制...');
  ctx.fillStyle = '#000000';
  ctx.font = '20px Arial';
  ctx.fillText('Canvas 2D 测试', 70, 35);
  
  // 测试变换
  console.log('测试变换...');
  ctx.save();
  ctx.translate(150, 100);
  ctx.rotate(Math.PI / 4);
  ctx.fillStyle = '#00ff00';
  ctx.fillRect(-25, -25, 50, 50);
  ctx.restore();
  
  // 测试滤镜
  console.log('测试滤镜...');
  ctx.filter = 'blur(2px)';
  ctx.fillStyle = '#0000ff';
  ctx.fillRect(200, 10, 50, 50);
  ctx.filter = 'none';
  
  console.log('Canvas 2D 测试完成！');
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testCanvas2D };
}
