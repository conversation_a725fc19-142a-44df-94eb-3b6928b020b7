.index-page {
  background: linear-gradient(180deg, #ffeef2 0%, #fff5f8 30%, #fafafa 70%, #f8f8f8 100%);
  min-height: 100vh;
  padding-bottom: 40rpx;
}
.notice-bar {
  width: 100%;
  height: 60rpx;
  background: #ffe0e0;
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  font-size: 26rpx;
  color: #d94f4f;
  border-radius: 0 0 24rpx 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(217,79,79,0.08);
}
.banner {
  margin: 0 24rpx 24rpx 24rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.08);
}
.banner-swiper {
  width: 100%;
  height: 300rpx;
}
.banner-img {
  width: 100%;
  height: 300rpx;
  display: block;
  transition: transform 0.2s ease;
}

.banner-img:active {
  transform: scale(0.98);
}
.goods-area {
  background: linear-gradient(135deg, #ffd6e0 0%, #ffe4ec 50%, #fff0f5 100%);
  border-radius: 32rpx;
  margin: 0 20rpx 32rpx 20rpx;
  padding: 32rpx 24rpx 24rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(255,192,203,0.15);
  max-width: 100vw;
  overflow-x: hidden;
  position: relative;
  animation: slideInUp 0.6s ease-out;
}

/* 弹窗样式 */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.popup-container {
  position: relative;
  width: 90vw;
  height: 80vh;
  background: transparent;
  border-radius: 16rpx;
  overflow: hidden;
  animation: popupSlideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.popup-close {
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 120rpx;
  height: 120rpx;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10001;
  transition: all 0.2s ease;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.popup-close:active {
  transform: scale(0.9);
  background: rgba(0, 0, 0, 0.8);
}

.close-icon {
  color: white;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

.popup-image {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 16rpx;
  object-fit: contain;
  background: white;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes popupSlideIn {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.goods-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
  background-size: 40rpx 40rpx;
  border-radius: 32rpx;
  pointer-events: none;
}

.area-header {
  text-align: center;
  margin-bottom: 24rpx;
  position: relative;
  z-index: 1;
}

.area-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #d94f4f;
  margin-bottom: 4rpx;
  text-shadow: 0 2rpx 4rpx rgba(217,79,79,0.2);
}

.area-subtitle {
  display: block;
  font-size: 22rpx;
  color: #888;
  opacity: 0.8;
}

.goods-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100vw;
  overflow-x: hidden;
  gap: 16rpx;
  position: relative;
  z-index: 1;
}

.goods-card {
  flex: 1;
  background: rgba(255,255,255,0.95);
  border-radius: 24rpx;
  margin-bottom: 0;
  box-shadow: 0 6rpx 20rpx rgba(0,0,0,0.08);
  padding: 20rpx 12rpx 16rpx 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2rpx solid rgba(255,255,255,0.8);
  backdrop-filter: blur(10rpx);
  animation: fadeInUp 0.6s ease-out both;
}

.goods-card:nth-child(1) { animation-delay: 0.1s; }
.goods-card:nth-child(2) { animation-delay: 0.2s; }
.goods-card:nth-child(3) { animation-delay: 0.3s; }
.goods-card:nth-child(4) { animation-delay: 0.4s; }

.goods-card:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 12rpx 32rpx rgba(0,0,0,0.12);
  border-color: rgba(255,182,193,0.6);
}

.goods-card:active {
  transform: translateY(-2rpx) scale(0.98);
}

/* 添加点击波纹效果 */
.goods-card {
  position: relative;
  overflow: hidden;
}

.goods-card::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255,182,193,0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.goods-card:active::after {
  width: 200rpx;
  height: 200rpx;
}
.goods-img {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  margin-bottom: 12rpx;
}

/* 分类占位符样式 */
.category-placeholder {
  width: 100rpx;
  height: 100rpx;
  border-radius: 20rpx;
  margin-bottom: 12rpx;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  box-shadow: 0 4rpx 16rpx rgba(255,154,158,0.3);
  position: relative;
  overflow: hidden;
}

.category-placeholder::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.3) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.category-icon {
  font-size: 40rpx;
  opacity: 0.9;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
  position: relative;
  z-index: 1;
}

.goods-price {
  color: #d94f4f;
  font-weight: bold;
  font-size: 22rpx;
  margin-bottom: 2rpx;
}

.goods-title {
  color: #333;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.1);
}
.zone-row {
  display: flex;
  justify-content: space-between;
  margin: 0 16rpx 24rpx 16rpx;
  max-width: 100vw;
  overflow-x: hidden;
}
.zone-card {
  width: 48%;
  border-radius: 20rpx;
  padding: 24rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}
.zone-card.new {
  background: linear-gradient(135deg, #ffe0e0, #ffd6d6);
}
.zone-card.fast {
  background: linear-gradient(135deg, #e0f7ff, #d6f0ff);
}
.zone-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.zone-desc {
  font-size: 24rpx;
  color: #888;
  margin-bottom: 16rpx;
}
.zone-btn {
  background: #fff;
  color: #d94f4f;
  border-radius: 32rpx;
  padding: 8rpx 32rpx;
  font-size: 28rpx;
  font-weight: bold;
  border: none;
}
.recommend-scroll {
  margin: 0 0 24rpx 0;
}
.recommend-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #d94f4f;
  margin: 0 0 16rpx 0;
}
.recommend-list {
  display: flex;
  flex-direction: row;
  height: 180rpx;
  padding: 0 16rpx;
  max-width: 100vw;
  overflow-x: hidden;
}
.recommend-card {
  width: 140rpx;
  margin-right: 16rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx 0;
}
.recommend-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}
.recommend-desc {
  font-size: 20rpx;
  color: #888;
  margin-bottom: 4rpx;
  text-align: center;
}
.recommend-btn {
  background: #ffd6e0;
  color: #d94f4f;
  border-radius: 24rpx;
  padding: 4rpx 24rpx;
  font-size: 22rpx;
  border: none;
}
.recommend-group {
  margin: 0 16rpx 24rpx 16rpx;
}
.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #e64340;
  margin: 32rpx 0 12rpx 24rpx;
}
.product-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin: 0 0 24rpx 0;
  padding: 0 24rpx;
  /* 强制确保flex布局 */
  flex-direction: row;
  justify-content: flex-start;
}

/* 单个产品时的网格样式 */
.product-grid-single {
  justify-content: center;
  margin: 0 0 12rpx 0;
  padding: 0 12rpx;
}

/* 两个产品时的网格样式 */
.product-grid-double {
  margin: 0 0 16rpx 0;
  padding: 0 16rpx;
}

/* 多个产品时的网格样式 */
.product-grid-multi {
  margin: 0 0 24rpx 0;
  padding: 0 24rpx;
}

.product-card {
  width: calc(50% - 8rpx);
  background: rgba(255,255,255,0.95);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 20rpx rgba(255,182,193,0.15);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(255,182,193,0.2);
  backdrop-filter: blur(8rpx);
  /* 强制确保宽度和布局 */
  flex: 0 0 calc(50% - 8rpx);
  max-width: calc(50% - 8rpx);
  box-sizing: border-box;
}

/* 单个产品时的卡片样式 - 更大更突出 */
.product-card-single {
  width: 280rpx;
  max-width: 320rpx;
  box-shadow: 0 8rpx 28rpx rgba(255,182,193,0.25);
  transform: scale(1.02);
}

/* 两个产品时的卡片样式 - 稍微调整 */
.product-card-double {
  width: calc(50% - 8rpx);
  flex: 0 0 calc(50% - 8rpx);
  max-width: calc(50% - 8rpx);
}

/* 多个产品时的卡片样式 - 标准样式 */
.product-card-multi {
  width: calc(50% - 8rpx);
  flex: 0 0 calc(50% - 8rpx);
  max-width: calc(50% - 8rpx);
}

.product-card:active {
  transform: scale(0.96);
  box-shadow: 0 4rpx 16rpx rgba(255,182,193,0.2);
}

.product-card-single:active {
  transform: scale(0.98);
  box-shadow: 0 6rpx 24rpx rgba(255,182,193,0.3);
}

.product-image {
  width: 100%;
  height: 280rpx;
  background: #f5f5f5;
}

/* 单个产品时的图片样式 - 更高更突出 */
.product-card-single .product-image {
  height: 320rpx;
}

.product-info {
  padding: 16rpx;
}

/* 单个产品时的信息区域 - 更多内边距 */
.product-card-single .product-info {
  padding: 20rpx;
}

.product-title {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 单个产品时的标题样式 - 更大字体 */
.product-card-single .product-title {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.product-price .sale-price {
  color: #ff4757;
  font-size: 28rpx;
  font-weight: bold;
}

.product-price .original-price {
  color: #999;
  font-size: 22rpx;
  text-decoration: line-through;
}

.product-price .price {
  color: #ff4757;
  font-size: 28rpx;
  font-weight: bold;
}

/* 单个产品时的价格样式 - 更大更突出 */
.product-card-single .product-price .sale-price {
  font-size: 32rpx;
  font-weight: 700;
}

.product-card-single .product-price .original-price {
  font-size: 24rpx;
}

.product-card-single .product-price .price {
  font-size: 32rpx;
  font-weight: 700;
}

/* 推荐区域整体容器 */
.recommend-section {
  margin: 0 20rpx 32rpx 20rpx;
  background: rgba(255,255,255,0.8);
  border-radius: 24rpx;
  padding: 24rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(255,182,193,0.15);
  border: 1rpx solid rgba(255,255,255,0.9);
  animation: slideInUp 0.8s ease-out 0.2s both;
}

/* 标签导航区域 */
.recommend-tabs {
  display: flex;
  background: linear-gradient(135deg, #fff0f5 0%, #ffe4ec 100%);
  border-radius: 20rpx;
  padding: 6rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255,182,193,0.2);
  border: 1rpx solid rgba(255,182,193,0.3);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 18rpx 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  min-width: 0; /* 防止文字溢出 */
  position: relative;
  overflow: hidden;
  color: #8b5a6b;
  font-weight: 500;
}

.tab-item:active {
  transform: scale(0.96);
}

.tab-item.active {
  background: linear-gradient(135deg, #ff6b9d, #ff8a9b);
  color: #ffffff;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 157, 0.4);
  position: relative;
  transform: translateY(-2rpx);
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8rpx solid transparent;
  border-right: 8rpx solid transparent;
  border-top: 8rpx solid #ff6b9d;
}

.tab-title {
  font-size: 28rpx;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tab-item.active .tab-title {
  color: #ffffff;
}

.tab-item:not(.active) .tab-title {
  color: #333;
}

/* 内容区域 */
.recommend-content {
  min-height: 400rpx;
  position: relative;
  padding: 16rpx 0;
}

/* 推荐模块动画 */
.recommend-module {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recommend-modules {
  margin: 0 20rpx 32rpx 20rpx;
}

.recommend-module {
  margin-bottom: 24rpx;
  background: rgba(255,255,255,0.9);
  border-radius: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(255,182,193,0.12);
  border: 1rpx solid rgba(255,182,193,0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8rpx);
}

/* 基础内边距 */
.recommend-module {
  padding: 24rpx;
}

/* 单个产品时的样式 - 减少内边距，增加产品卡片大小 */
.recommend-module-single {
  padding: 16rpx 24rpx 20rpx 24rpx;
}

/* 两个产品时的样式 - 适中的内边距 */
.recommend-module-double {
  padding: 20rpx 24rpx;
}

/* 多个产品时的样式 - 标准内边距 */
.recommend-module-multi {
  padding: 24rpx;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 40rpx 20rpx;
  color: #999;
}

.empty-text {
  font-size: 24rpx;
  color: #ccc;
}
.hot-section {
  background: #ffe0e0;
  border-radius: 24rpx;
  margin: 24rpx 16rpx;
  padding: 24rpx;
  max-width: 100vw;
  overflow-x: hidden;
}
.hot-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #d94f4f;
  margin-bottom: 8rpx;
}
.hot-desc {
  font-size: 22rpx;
  color: #888;
  margin-bottom: 16rpx;
}
.hot-main-row {
  display: flex;
  flex-direction: row;
  gap: 16rpx;
  margin-bottom: 16rpx;
}
.hot-main-card {
  flex: 1.2;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx;
  position: relative;
}
.hot-main-img {
  width: 100%;
  height: 180rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}
.hot-main-btn {
  display: flex;
  align-items: center;
  background: #ffd6e0;
  color: #d94f4f;
  border-radius: 24rpx;
  padding: 4rpx 16rpx;
  font-size: 20rpx;
}
.hot-right-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}
.hot-right-card {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx;
  position: relative;
}
.hot-right-img {
  width: 100rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-bottom: 4rpx;
}
.hot-right-title {
  display: flex;
  align-items: center;
  font-size: 20rpx;
  color: #333;
}
.hot-tag {
  background: #d94f4f;
  color: #fff;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-right: 8rpx;
  font-size: 18rpx;
}
.hot-bottom-row {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
}
.hot-bottom-row::-webkit-scrollbar {
  display: none;
}
.hot-bottom-card {
  display: inline-block;
  vertical-align: top;
  width: 180rpx;
  margin-right: 16rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  padding: 12rpx;
}
.hot-bottom-card:last-child {
  margin-right: 0;
}
.hot-bottom-img {
  width: 100%;
  height: 180rpx;
  border-radius: 12rpx;
  margin-bottom: 8rpx;
}
.hot-bottom-title {
  font-size: 20rpx;
  color: #333;
  text-align: center;
}
.anchor-recommend {
  display: flex;
  flex-direction: row;
  background: #ffe4ec;
  border-radius: 20rpx;
  padding: 20rpx;
  margin: 20rpx 0;
  min-height: 260rpx;
  align-items: stretch;
}
.anchor-left {
  flex: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
}
.anchor-video {
  width: 340rpx;
  height: 520rpx;
  border-radius: 12rpx;
  background: #fff;
}
.anchor-right {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 20rpx;
}
.anchor-product-top,
.anchor-product-bottom {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10rpx;
}
.anchor-product-bottom {
  margin-bottom: 0;
}
.anchor-product-img {
  width: 300rpx;
  height: 250rpx;
  border-radius: 8rpx;
  background: #fff;
}