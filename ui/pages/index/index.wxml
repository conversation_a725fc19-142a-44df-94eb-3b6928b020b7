<view class="index-page">
  <!-- 首页弹窗 -->
  <view class="popup-overlay" wx:if="{{showPopup}}" bindtap="closePopup">
    <view class="popup-container" catchtap="">
      <view class="popup-close" bindtap="closePopup">
        <text class="close-icon">×</text>
      </view>
      <image class="popup-image" 
             src="{{popupData.image_url}}" 
             mode="aspectFit"
             bindtap="onPopupTap" />
    </view>
  </view>
  <!-- 滚动公告栏 -->
  <view class="notice-bar" wx:if="{{notices.length > 0}}">
    <swiper vertical autoplay interval="3000" circular>
      <block wx:for="{{notices}}" wx:key="id">
        <swiper-item>
          <text>{{item.title}}</text>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- Banner区 -->
  <view class="banner">
    <swiper indicator-dots="true" autoplay="true" interval="3000" circular="true" class="banner-swiper">
      <block wx:for="{{bannerList}}" wx:key="*this">
        <swiper-item>
          <image src="{{item.image_url}}"
                 mode="aspectFill"
                 class="banner-img"
                 bindtap="onBannerTap"
                 data-link-type="{{item.link_type}}"
                 data-link-target="{{item.link_target}}"
                 data-title="{{item.title}}"/>
        </swiper-item>
      </block>
    </swiper>
  </view>

  <!-- 商品分类区 -->
  <view class="goods-area">
    <view class="area-header">
      <text class="area-title">🌟 精选专区</text>
      <text class="area-subtitle">发现更多惊喜</text>
    </view>
    <view class="goods-list">
      <block wx:for="{{categories}}" wx:key="id">
        <view class="goods-card"
              bindtap="navigateToProduct"
              data-zone="{{item.zone_key}}">
          <view class="category-placeholder">
            <text class="category-icon">
              {{item.zone_key === 'new' ? '🎉' :
                item.zone_key === 'special' ? '🎁' :
                item.zone_key === 'valentine' ? '💝' :
                item.name.includes('新店') ? '🏪' :
                item.name.includes('特惠') ? '🎯' :
                item.name.includes('七夕') ? '💕' : '✨'}}
            </text>
          </view>
          <view class="goods-title">{{item.name}}</view>
        </view>
      </block>
    </view>
    <!-- 新品区和速发区 - 暂时隐藏 -->
    <!--
    <view class="zone-row">
      <view class="zone-card new">
        <text class="zone-title">新品区</text>
        <text class="zone-desc">睡醒吧</text>
        <button class="zone-btn" bindtap="navigateToProduct" data-zone="new">GO</button>
      </view>
      <view class="zone-card fast">
        <text class="zone-title">速发区</text>
        <text class="zone-desc">2天发货</text>
        <button class="zone-btn" bindtap="navigateToProduct" data-zone="fast">GO</button>
      </view>
    </view>
    -->
  </view>

  <!-- 推荐区标签页 -->
  <view class="recommend-section" wx:if="{{recommendModules.length > 0}}">
    <!-- 标签导航 -->
    <view class="recommend-tabs">
      <block wx:for="{{recommendModules}}" wx:key="module_id" wx:for-index="tabIndex">
        <view class="tab-item {{currentRecommendTab === tabIndex ? 'active' : ''}}"
              bindtap="switchRecommendTab"
              data-index="{{tabIndex}}">
          <view class="tab-title">{{item.module_name}}</view>
        </view>
      </block>
    </view>

    <!-- 内容区域 -->
    <view class="recommend-content">
      <block wx:for="{{recommendModules}}" wx:key="module_id" wx:for-index="contentIndex">
        <view class="recommend-module recommend-module-{{item.products.length <= 1 ? 'single' : item.products.length <= 2 ? 'double' : 'multi'}}"
              wx:if="{{currentRecommendTab === contentIndex}}">
          <view class="product-grid product-grid-{{item.products.length <= 1 ? 'single' : item.products.length <= 2 ? 'double' : 'multi'}}" wx:if="{{item.products && item.products.length > 0}}">
            <block wx:for="{{item.products}}" wx:key="product_id" wx:for-item="product">
              <view class="product-card product-card-{{item.products.length <= 1 ? 'single' : item.products.length <= 2 ? 'double' : 'multi'}}"
                    bindtap="goToDetail"
                    data-id="{{product.product_id}}">
                <image class="product-image" src="{{product.img}}" mode="aspectFill"/>
                <view class="product-info">
                  <view class="product-title">{{product.title}}</view>
                  <view class="product-price">
                    <block wx:if="{{product.price_sale && product.price_sale < product.price_tag}}">
                      <text class="sale-price">￥{{product.price_sale}}</text>
                      <text class="original-price">￥{{product.price_tag}}</text>
                    </block>
                    <block wx:else>
                      <text class="price">￥{{product.price_sale || product.price_tag}}</text>
                    </block>
                  </view>
                </view>
              </view>
            </block>
          </view>
          <!-- 空状态提示 -->
          <view class="empty-state" wx:if="{{!item.products || item.products.length === 0}}">
            <text class="empty-text">暂无商品</text>
          </view>
        </view>
      </block>
    </view>
  </view>
</view>