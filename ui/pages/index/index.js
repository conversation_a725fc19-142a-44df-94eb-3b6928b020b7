const config = require('../../config/setting');
const { getImageUrl } = config;
const app = getApp();

Page({
  data: {
    bannerList: [
      {
        id:'',
        image_url:''
      }
    ],
    categories: [], // 商品分类数据
    notices: [], // 首页滚动通知
    recommendModules: [], // 动态推荐模块
    currentRecommendTab: 0, // 当前选中的推荐标签
    anchorProducts: [], // 主播推荐商品
    // 弹窗相关数据
    showPopup: false, // 是否显示弹窗
    popupData: null, // 当前弹窗数据
    popupQueue: [], // 弹窗队列
    currentPopupIndex: 0 // 当前弹窗索引
  },
  refresh_banner(){
    wx.showLoading({
      mask:true
    })
    wx.request({
      url: config.banner,
      method:'GET',
      success:(res)=>{
          // 处理banner图片URL
          const bannerList = res.data.map(banner => ({
            ...banner,
            image_url: getImageUrl(banner.image_url)
          }));
          this.setData({
            bannerList: bannerList
          })
      },
      complete:()=>{
        wx.hideLoading()
      },
    })
  },
  // 获取商品分类数据（除了"全部"分类）
  loadCategories(){
    wx.showLoading({
      mask:true
    })
    wx.request({
      url: config.rootUrl + '/product-zones',
      method:'GET',
      success:(res)=>{
        if (res.data && Array.isArray(res.data)) {
          // 过滤掉"全部"分类
          const filteredCategories = res.data.filter(category =>
            category.zone_key !== 'all' && category.status === 1
          );
          this.setData({
            categories: filteredCategories
          });
        }
      },
      fail:(err)=>{
        console.error('获取分类失败:', err);
        wx.showToast({
          title: '获取分类失败',
          icon: 'none'
        });
      },
      complete:()=>{
        wx.hideLoading()
      },
    })
  },
  onLoad() {
    this.refresh_banner();
    this.loadCategories();
    this.getHomeNotices();
    this.getRecommendModules();
    this.getPopupNotices();
  },

  onShow() {
    // 检查是否刚登录成功
    const justLoggedIn = wx.getStorageSync('justLoggedIn');
    if (justLoggedIn) {
      // 清除登录成功标记
      wx.removeStorageSync('justLoggedIn');
      // 延迟显示弹窗，确保页面切换完成，登录成功时强制显示
      setTimeout(() => {
        this.getPopupNotices(true);
      }, 800);
    }
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const { linkType, linkTarget, title } = e.currentTarget.dataset;

    console.log('轮播图点击:', { linkType, linkTarget, title });

    if (!linkType || !linkTarget) {
      console.log('缺少跳转参数');
      return;
    }

    switch (parseInt(linkType)) {
      case 1: // 产品类目
        this.navigateToProductCategory(linkTarget, title);
        break;
      case 2: // 优惠券页面
        this.navigateToCoupon(linkTarget, title);
        break;
      case 3: // 外部链接
        this.navigateToExternal(linkTarget, title);
        break;
      default:
        console.log('未知的跳转类型:', linkType);
        wx.showToast({
          title: '暂不支持此类型跳转',
          icon: 'none'
        });
    }
  },

  // 跳转到产品类目页面
  navigateToProductCategory(categoryId, title) {
    console.log('跳转到产品类目:', categoryId);

    // 先根据categoryId查询对应的zone_key
    wx.request({
      url: config.rootUrl + '/product-zones',
      method: 'GET',
      success: (res) => {
        if (res.data && Array.isArray(res.data)) {
          // 查找对应的分类
          const category = res.data.find(item => item.id == categoryId);
          if (category) {
            console.log('找到分类:', category);
            // 设置全局数据
            app.globalData.navigateToProductZone = category.zone_key;

            // 跳转到产品页面
            wx.switchTab({
              url: '/pages/product/product',
              success: () => {
                console.log('跳转成功到产品页面，zone:', category.zone_key);
              }
            });
          } else {
            console.error('未找到对应的产品分类:', categoryId);
            wx.showToast({
              title: '产品分类不存在',
              icon: 'none'
            });
          }
        } else {
          console.error('获取产品分类失败');
          wx.showToast({
            title: '获取分类信息失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求产品分类失败:', err);
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到优惠券页面
  navigateToCoupon(couponId, title) {
    console.log('跳转到优惠券页面:', couponId);
    // 这里可以根据实际需求跳转到优惠券页面
    // 如果还没有优惠券页面，可以先显示提示
    wx.showToast({
      title: `优惠券功能开发中`,
      icon: 'none'
    });

    // 未来可以这样跳转：
    // wx.navigateTo({
    //   url: `/pages/coupon/coupon?id=${couponId}&title=${encodeURIComponent(title)}`
    // });
  },

  // 跳转到外部链接
  navigateToExternal(url, title) {
    console.log('跳转到外部链接:', url);

    // 检查URL格式
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      wx.showToast({
        title: '链接格式不正确',
        icon: 'none'
      });
      return;
    }

    // 复制链接到剪贴板并提示用户
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showModal({
          title: '外部链接',
          content: `链接已复制到剪贴板，请在浏览器中打开\n\n${url}`,
          showCancel: false,
          confirmText: '知道了'
        });
      },
      fail: () => {
        wx.showModal({
          title: '外部链接',
          content: `请复制以下链接在浏览器中打开：\n\n${url}`,
          showCancel: false,
          confirmText: '知道了'
        });
      }
    });
  },
  // 获取首页滚动通知
  getHomeNotices() {
    wx.request({
      url: config.rootUrl + '/home-notices',
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          this.setData({
            notices: res.data.data
          });
        }
      },
      fail: (err) => {
        console.error('获取首页通知失败:', err);
      }
    });
  },

  // 获取首页弹窗数据
  getPopupNotices(forceShow = false) {
    // 检查今天是否已经显示过弹窗（避免重复显示）
    const today = new Date().toDateString();
    const lastShownDate = wx.getStorageSync('popupLastShown');
 
    if (!forceShow && lastShownDate === today) {
      console.log('今天已显示过弹窗，跳过');
      return;
    }
    
    wx.request({
      url: config.rootUrl + '/home-banner-notices/2',
      method: 'GET',
      success: (res) => {
        console.log('弹窗数据:', res.data);
        if (res.data && res.data.success && res.data.data && res.data.data.length > 0) {
          // 处理所有弹窗数据的图片URL
          const processedData = res.data.data.map(popup => ({
            ...popup,
            image_url: getImageUrl(popup.image_url)
          }));
          
          // 过滤有效的弹窗数据（状态启用且在有效时间内）
          const validPopups = processedData.filter(popup => {
            if (popup.status !== 1) return false;
            
            const now = new Date();
            if (popup.start_time && new Date(popup.start_time) > now) return false;
            if (popup.end_time && new Date(popup.end_time) < now) return false;
            
            return true;
          });
          
          if (validPopups.length > 0) {
            // 按sort_order排序
            validPopups.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0));
            
            console.log('有效弹窗数量:', validPopups.length);
            console.log('弹窗队列:', validPopups);
            
            this.setData({
              popupQueue: validPopups,
              currentPopupIndex: 0
            });
            
            // 延迟显示第一个弹窗，确保页面加载完成
            setTimeout(() => {
              this.showNextPopup();
              // 记录今天已显示弹窗
              wx.setStorageSync('popupLastShown', today);
            }, 500);
          }
        }
      },
      fail: (err) => {
        console.error('获取弹窗数据失败:', err);
      }
    });
  },

  // 显示下一个弹窗
  showNextPopup() {
    const { popupQueue, currentPopupIndex } = this.data;
    
    if (currentPopupIndex < popupQueue.length) {
      const popupData = popupQueue[currentPopupIndex];
      console.log('显示弹窗:', currentPopupIndex + 1, '/', popupQueue.length, popupData);
      
      this.setData({
        popupData: popupData,
        showPopup: true
      });
    }
  },

  // 关闭当前弹窗并显示下一个
  closePopup() {
    const { popupQueue, currentPopupIndex } = this.data;
    
    this.setData({
      showPopup: false
    });
    
    // 延迟一下再显示下一个弹窗，避免动画冲突
    setTimeout(() => {
      const nextIndex = currentPopupIndex + 1;
      
      if (nextIndex < popupQueue.length) {
        // 还有下一个弹窗
        this.setData({
          currentPopupIndex: nextIndex
        });
        
        // 延迟显示下一个弹窗
        setTimeout(() => {
          this.showNextPopup();
        }, 300);
      } else {
        // 所有弹窗都显示完了
        console.log('所有弹窗显示完成');
        this.setData({
          popupData: null,
          popupQueue: [],
          currentPopupIndex: 0
        });
      }
    }, 300);
  },

  // 弹窗点击事件
  onPopupTap() {
    const popupData = this.data.popupData;
    if (!popupData || !popupData.link_type || !popupData.link_target) {
      return;
    }

    // 关闭弹窗
    this.closePopup();

    // 根据跳转类型处理跳转
    switch (parseInt(popupData.link_type)) {
      case 1: // 产品类目
        this.navigateToProductCategory(popupData.link_target, popupData.title);
        break;
      case 2: // 优惠券页面
        this.navigateToCoupon(popupData.link_target, popupData.title);
        break;
      case 3: // 外部链接
        this.navigateToExternal(popupData.link_target, popupData.title);
        break;
      default:
        console.log('未知的跳转类型:', popupData.link_type);
    }
  },

  // 获取推荐模块及商品
  getRecommendModules() {
    wx.request({
      url: config.rootUrl + '/recommend-modules-with-products',
      method: 'GET',
      success: (res) => {
        console.log('推荐模块数据:', res.data);
        if (res.data && res.data.success) {
          // 处理价格转换：从分转换为元，并处理图片URL
          const processedModules = res.data.data.map(module => {
            if (module.products && module.products.length > 0) {
              module.products = module.products.map(product => {
                return {
                  ...product,
                  img: getImageUrl(product.img), // 处理商品图片URL
                  price_sale: product.price_sale ? (product.price_sale / 100).toFixed(2) : '0.00',
                  price_tag: product.price_tag ? (product.price_tag / 100).toFixed(2) : '0.00'
                };
              });
            }
            return module;
          });

          this.setData({
            recommendModules: processedModules
          });
        } else {
          console.error('获取推荐模块失败:', res.data);
          this.setData({
            recommendModules: []
          });
        }
      },
      fail: (err) => {
        console.error('获取推荐模块失败:', err);
        this.setData({
          recommendModules: []
        });
      }
    });
  },
  navigateToProduct(event) {
    const zone = event.currentTarget.dataset.zone;
    console.log(zone)
    
    // Set the zone in global data
    app.globalData.navigateToProductZone = zone;

    wx.switchTab({
      url: `/pages/product/product`
    });
  },
  onReady() {

  },

  // 切换推荐标签
  switchRecommendTab(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentRecommendTab: index
    });
  },

  goToDetail(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/productDetail/productDetail?id=${id}`
    });
  }
})