.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 订单头部 */
.order-header {
  background: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  margin-top: 20rpx; /* 增加与顶部导航栏的间距 */
}

.order-no {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.order-actions {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.order-status {
  color: #ff6b6b;
  font-size: 28rpx;
}

/* 商品信息 */
.product-section {
  background: white;
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.product-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.product-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  color: #ff6b6b;
  font-size: 32rpx;
  font-weight: bold;
}

.quantity {
  color: #999;
  font-size: 28rpx;
}

.view-content-btn {
  padding: 20rpx 30rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
}

.view-content-btn text {
  color: #ff6b6b;
  font-size: 28rpx;
  border: 1rpx solid #ff6b6b;
  padding: 10rpx 30rpx;
  border-radius: 20rpx;
}

/* 收货地址 */
.address-section {
  background: white;
  margin-bottom: 20rpx;
}

.address-selector {
  padding: 30rpx;
  min-height: 120rpx;
  display: flex;
  align-items: center;
}

.selected-address {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.address-info {
  flex: 1;
}

.recipient {
  display: block;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.change-btn {
  color: #007aff;
  font-size: 28rpx;
  padding: 10rpx 20rpx;
  border: 1rpx solid #007aff;
  border-radius: 8rpx;
}

.no-address {
  width: 100%;
  text-align: center;
  padding: 40rpx 0;
}

.select-address-text {
  color: #ff6b6b;
  font-size: 32rpx;
  border: 2rpx dashed #ff6b6b;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
}

/* 优惠券选择区域 */
.coupon-section {
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.coupon-selector {
  display: flex;
  align-items: center;
  padding: 30rpx;
}

.coupon-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
}

.coupon-info {
  flex: 1;
}

.selected-coupon {
  display: flex;
  flex-direction: column;
}

.coupon-name {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.coupon-desc {
  font-size: 28rpx;
  color: #ff6b6b;
  margin-top: 8rpx;
}

.no-coupon {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8rpx;
}

.select-coupon-text {
  font-size: 32rpx;
  color: #333;
}

.coupon-status {
  display: flex;
  gap: 10rpx;
}

.available-count {
  font-size: 24rpx;
  color: #ff6b6b;
  background: #fff0f0;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.unavailable-count {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.arrow {
  font-size: 32rpx;
  color: #999;
}

/* 优惠券列表 */
.coupon-list {
  padding: 20rpx;
  background: #f8f8f8;
}

.coupon-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 15rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 12rpx;
  background: white;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.coupon-item.disabled {
  background: #f8f8f8;
  border-color: #e0e0e0;
  opacity: 0.6;
  box-shadow: none;
}

.coupon-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.coupon-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b6b;
  margin-bottom: 5rpx;
}

.coupon-item.disabled .coupon-value {
  color: #999;
}

.coupon-condition {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  line-height: 1.2;
}

.coupon-item.disabled .coupon-condition {
  color: #999;
}

.coupon-right {
  flex: 1;
}

.coupon-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: bold;
}

.coupon-item.disabled .coupon-title {
  color: #999;
}

.coupon-validity {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.coupon-item.disabled .coupon-validity {
  color: #999;
}

.coupon-tip {
  font-size: 22rpx;
  color: #ff6b6b;
  background: #fff5f5;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  display: inline-block;
}

.coupon-status-icon {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.selected-icon {
  color: #00c851;
  font-size: 28rpx;
  font-weight: bold;
}

.disabled-icon {
  color: #999;
  font-size: 24rpx;
}

/* 订单总价 */
.price-section {
  background: white;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.price-detail {
  margin-bottom: 20rpx;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.discount-row .price-label {
  color: #ff6b6b;
}

.discount-value {
  color: #ff6b6b;
  font-weight: bold;
}

.total-price {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1rpx solid #eee;
  padding-top: 20rpx;
}

.price-label {
  font-size: 32rpx;
  color: #333;
}

.price-value {
  font-size: 36rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.final-price {
  font-size: 40rpx;
  color: #ff6b6b;
}

/* 确认付款按钮 */
.payment-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
}

.payment-btn {
  width: 100%;
  background: #ff6b6b;
  color: white;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 8rpx;
  height: 88rpx;
  line-height: 88rpx;
  border: none;
}

.payment-btn[disabled] {
  background: #ccc;
  color: #999;
}

/* 添加地址按钮 */
.add-address-section {
  padding: 30rpx;
}

.add-address-btn {
  width: 100%;
  background: #007aff;
  color: white;
  font-size: 32rpx;
  border-radius: 8rpx;
  height: 80rpx;
  line-height: 80rpx;
  border: none;
}
