const config = require('../../config/setting');
const { getImageUrl } = config;

Page({
  data: {
    orderNo: '',
    product: null,
    processName: '',
    processPrice: 0,
    displayPrice: '',
    totalCount: 0,
    totalPrice: 0,
    originalPrice: 0,
    finalPrice: 0,
    selectedAddress: null,
    addressList: [],
    cartItems: [],
    availableCoupons: [],
    selectedCoupon: null
  },

  onLoad(options) {
    console.log('订单确认页面参数:', options);

    // 生成订单号
    const orderNo = 'pt' + new Date().getTime();

    if (options.from === 'cart') {
      // 从购物车来的订单
      const cartItems = options.items ? JSON.parse(decodeURIComponent(options.items)) : [];
      const totalPrice = options.totalPrice ? Number(options.totalPrice) : 0;
      const totalCount = cartItems.reduce((sum, item) => sum + Number(item.quantity), 0);

      // 为购物车商品添加必要的字段
      const processedCartItems = cartItems.map(item => ({
        ...item,
        product_id: item.product_id,
        cart_id: item.cart_id || item.id,
        title: item.title || '商品',
        process_name: item.process_name || '',
        price_sale: Math.round((Number(item.price_sale) || 0) * 100), // 转换为分
        process_price: Math.round((Number(item.process_price) || 0) * 100), // 转换为分
        img: item.img || '',
        processedImg: getImageUrl(item.img) // 处理图片URL用于显示
      }));

      this.setData({
        orderNo,
        totalCount,
        totalPrice,
        originalPrice: totalPrice,
        finalPrice: totalPrice,
        cartItems: processedCartItems,
        from: 'cart'
      });

      // 加载可用优惠券
      this.loadAvailableCoupons();
    } else {
      // 从定制页面来的订单（保持原有逻辑）
      const product = options.product ? JSON.parse(decodeURIComponent(options.product)) : null;
      const processName = options.processName ? decodeURIComponent(options.processName) : '';
      const processPrice = options.processPrice ? Number(decodeURIComponent(options.processPrice)) : 0;
      const totalCount = options.totalCount ? Number(options.totalCount) : 0;
      const totalPrice = options.totalPrice ? Number(options.totalPrice) : 0;
      const cartItems = options.cartItems ? JSON.parse(decodeURIComponent(options.cartItems)) : [];

      // 格式化显示价格
      const displayPrice = processPrice ? (processPrice / 100).toFixed(2) : '0.00';

      this.setData({
        orderNo,
        product: {
          ...product,
          processedImg: getImageUrl(product?.img) // 处理产品图片URL
        },
        processName,
        processPrice,
        displayPrice,
        totalCount,
        totalPrice,
        originalPrice: totalPrice,
        finalPrice: totalPrice,
        cartItems,
        from: 'custom'
      });
    }

    // 加载用户地址
    this.loadAddressList();

    // 加载可用优惠券
    this.loadAvailableCoupons();
  },

  // 加载地址列表
  loadAddressList() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: config.rootUrl + '/addresses',
      method: 'GET',
      data: {
        user_id: userInfo.id
      },
      success: (res) => {
        if (res.data.success) {
          const addressList = res.data.data;
          // 找到默认地址
          const defaultAddress = addressList.find(addr => addr.is_default === 1);
          
          this.setData({
            addressList: addressList,
            selectedAddress: defaultAddress || (addressList.length > 0 ? addressList[0] : null)
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取地址列表失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 选择收货地址
  selectAddress() {
    if (this.data.addressList.length === 0) {
      // 没有地址，跳转到添加地址页面
      wx.navigateTo({
        url: '/pages/addressEdit/addressEdit?returnUrl=' + encodeURIComponent('/pages/orderConfirm/orderConfirm')
      });
      return;
    }

    // 有地址，显示地址选择列表，包含"新增地址"选项
    const addressNames = this.data.addressList.map(addr =>
      `${addr.name} ${addr.phone}\n${addr.province}${addr.city}${addr.district}${addr.detail}`
    );
    addressNames.push('+ 新增收货地址');

    wx.showActionSheet({
      itemList: addressNames,
      success: (res) => {
        if (res.tapIndex === addressNames.length - 1) {
          // 点击了"新增收货地址"
          wx.navigateTo({
            url: '/pages/addressEdit/addressEdit?returnUrl=' + encodeURIComponent('/pages/orderConfirm/orderConfirm')
          });
        } else {
          // 选择了现有地址
          const selectedAddress = this.data.addressList[res.tapIndex];
          this.setData({
            selectedAddress: selectedAddress
          });
        }
      }
    });
  },

  // 添加新地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/addressEdit/addressEdit?returnUrl=' + encodeURIComponent('/pages/orderConfirm/orderConfirm')
    });
  },

  // 确认付款
  confirmPayment() {
    if (!this.data.selectedAddress) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return;
    }

    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 准备订单数据
    let orderData;

    // 计算优惠信息
    const discountAmount = this.data.selectedCoupon ? parseFloat(this.data.selectedCoupon.value) : 0;
    const finalPriceInCents = Math.round(this.data.finalPrice * 100); // 转换为分

    if (this.data.from === 'cart') {
      // 购物车订单
      orderData = {
        user_id: userInfo.id,
        order_no: this.data.orderNo,
        total_count: this.data.totalCount,
        total_price: Math.round(this.data.originalPrice * 100), // 原价，转换为分
        discount_amount: Math.round(discountAmount * 100), // 优惠金额，转换为分
        final_price: finalPriceInCents, // 实付金额，转换为分
        address_id: this.data.selectedAddress.id,
        cart_items: this.data.cartItems,
        coupon_id: this.data.selectedCoupon ? this.data.selectedCoupon.id : null,
        status: 'pending',
        from: 'cart'
      };
    } else {
      // 定制订单
      orderData = {
        user_id: userInfo.id,
        order_no: this.data.orderNo,
        product_id: this.data.product ? this.data.product.id : null,
        process_name: this.data.processName,
        process_price: this.data.processPrice,
        total_count: this.data.totalCount,
        total_price: Math.round(this.data.originalPrice * 100), // 原价，转换为分
        discount_amount: Math.round(discountAmount * 100), // 优惠金额，转换为分
        final_price: finalPriceInCents, // 实付金额，转换为分
        address_id: this.data.selectedAddress.id,
        cart_items: this.data.cartItems,
        coupon_id: this.data.selectedCoupon ? this.data.selectedCoupon.id : null,
        status: 'pending',
        from: 'custom'
      };
    }

    console.log('提交订单数据:', orderData);

    wx.showLoading({
      title: '提交订单中...'
    });

    // 提交订单
    wx.request({
      url: config.rootUrl + '/orders',
      method: 'POST',
      data: orderData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.success) {
          wx.showToast({
            title: '订单提交成功',
            icon: 'success'
          });
          
          // 跳转到订单详情或支付页面
          setTimeout(() => {
            wx.redirectTo({
              url: `/pages/orderDetail/orderDetail?orderId=${res.data.orderId}`
            });
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.message || '订单提交失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 返回上一页
  goBack() {
    wx.navigateBack({
      delta: 1
    });
  },

  // 加载可用优惠券
  loadAvailableCoupons() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      console.log('用户未登录，无法加载优惠券');
      return;
    }

    wx.request({
      url: `${config.baseUrl}/api/coupons/my?user_id=${userInfo.id}&phone_number=${userInfo.mobile}&status=1`,
      method: 'GET',
      success: (res) => {
        if (res.data && res.data.success) {
          const currentOrderAmount = parseFloat(this.data.originalPrice) || 0;

          // 处理所有优惠券，添加可用性标记
          const processedCoupons = res.data.data.filter(coupon => {
            const now = new Date();
            const validTo = new Date(coupon.valid_to);
            return coupon.status === 1 && validTo > now;
          }).map(coupon => {
            const thresholdAmount = parseFloat(coupon.threshold_amount) || 0;
            const isUsable = thresholdAmount === 0 || currentOrderAmount >= thresholdAmount;

            return {
              ...coupon,
              isUsable: isUsable,
              thresholdAmount: thresholdAmount,
              reasonNotUsable: !isUsable ? `需满${thresholdAmount}元才能使用` : null
            };
          });

          // 分离可用和不可用的优惠券
          const usableCoupons = processedCoupons.filter(coupon => coupon.isUsable);
          const unusableCoupons = processedCoupons.filter(coupon => !coupon.isUsable);

          // 可用的排在前面，不可用的排在后面
          const allCoupons = [...usableCoupons, ...unusableCoupons];

          this.setData({
            availableCoupons: allCoupons,
            usableCouponsCount: usableCoupons.length
          });
        }
      },
      fail: (err) => {
        console.error('加载优惠券失败:', err);
      }
    });
  },

  // 选择优惠券
  selectCoupon() {
    if (this.data.availableCoupons.length === 0) {
      wx.showToast({
        title: '暂无优惠券',
        icon: 'none'
      });
      return;
    }

    if (this.data.usableCouponsCount === 0) {
      wx.showToast({
        title: '暂无可用优惠券',
        icon: 'none'
      });
      return;
    }

    // 构建优惠券选择列表，只包含可用的优惠券
    const usableCoupons = this.data.availableCoupons.filter(coupon => coupon.isUsable);
    const itemList = ['不使用优惠券', ...usableCoupons.map(coupon => {
      const thresholdText = coupon.thresholdAmount > 0 ? `满${coupon.thresholdAmount}减` : '无门槛';
      return `${coupon.template_name} ${thresholdText}${coupon.value}元`;
    })];

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        if (res.tapIndex === 0) {
          // 不使用优惠券
          this.setData({
            selectedCoupon: null
          });
          this.calculateFinalPrice();
        } else {
          // 选择优惠券
          const selectedCoupon = usableCoupons[res.tapIndex - 1];
          this.setData({
            selectedCoupon: selectedCoupon
          });
          this.calculateFinalPrice();
        }
      }
    });
  },

  // 计算最终价格
  calculateFinalPrice() {
    let finalPrice = this.data.originalPrice;

    if (this.data.selectedCoupon) {
      const discountAmount = parseFloat(this.data.selectedCoupon.value);
      finalPrice = Math.max(0, finalPrice - discountAmount);
    }

    this.setData({
      finalPrice: finalPrice.toFixed(2)
    });
  },

  // 页面显示时刷新地址
  onShow() {
    // 重新加载地址列表，确保获取最新的地址信息
    this.loadAddressList();
  },

  // 复制订单号
  copyOrderNo() {
    if (!this.data.orderNo) {
      wx.showToast({
        title: '订单号不存在',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: this.data.orderNo,
      success: () => {
        wx.showToast({
          title: '订单号已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 直接选择特定优惠券
  selectSpecificCoupon(e) {
    const coupon = e.currentTarget.dataset.coupon;
    if (!coupon.isUsable) {
      return;
    }

    // 如果点击的是已选中的优惠券，则取消选择
    if (this.data.selectedCoupon && this.data.selectedCoupon.id === coupon.id) {
      this.setData({
        selectedCoupon: null
      });
    } else {
      this.setData({
        selectedCoupon: coupon
      });
    }
    this.calculateFinalPrice();
  },

  // 显示不可用优惠券的提示
  showCouponTip(e) {
    const coupon = e.currentTarget.dataset.coupon;
    wx.showToast({
      title: coupon.reasonNotUsable || '优惠券不可用',
      icon: 'none',
      duration: 2000
    });
  }
});
