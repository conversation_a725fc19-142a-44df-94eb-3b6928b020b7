<view class="container">
  <!-- 统一的导航栏 -->
  <navigation-bar title="订单确认" back="{{true}}" color="black" background="#FFF"></navigation-bar>

  <!-- 订单号 -->
  <view class="order-header">
    <text class="order-no">{{orderNo}}</text>
    <view class="order-actions">
      <text class="action-btn" bindtap="copyOrderNo">复制</text>
    </view>
    <text class="order-status">待支付</text>
  </view>

  <!-- 商品信息 -->
  <view class="product-section">
    <!-- 购物车商品列表 -->
    <block wx:if="{{from === 'cart'}}">
      <view wx:for="{{cartItems}}" wx:key="id" class="product-item">
        <image class="product-image" src="{{item.processedImg || '/images/default-product.png'}}" mode="aspectFill"/>
        <view class="product-info">
          <text class="product-title">{{item.title}}</text>
          <text class="product-subtitle">{{item.process_name}}</text>
          <view class="product-price">
            <text class="price">￥{{item.unit_price}}</text>
            <text class="quantity">数量：{{item.quantity}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 定制商品（保持原有逻辑） -->
    <block wx:else>
      <view class="product-item">
        <image class="product-image" src="{{product.processedImg || '/images/default-product.png'}}" mode="aspectFill"/>
        <view class="product-info">
          <text class="product-title">{{processName}}</text>
          <text class="product-subtitle">{{product.title || '定制商品'}}</text>
          <view class="product-price">
            <text class="price">￥{{displayPrice}}</text>
            <text class="quantity">数量：{{totalCount}}</text>
          </view>
        </view>
      </view>
      <view class="view-content-btn">
        <text>查看内容</text>
      </view>
    </block>
  </view>

  <!-- 收货地址选择 -->
  <view class="address-section">
    <view class="address-selector" bindtap="selectAddress">
      <view wx:if="{{selectedAddress}}" class="selected-address">
        <view class="address-info">
          <text class="recipient">{{selectedAddress.name}} {{selectedAddress.phone}}</text>
          <text class="address-detail">{{selectedAddress.province}}{{selectedAddress.city}}{{selectedAddress.district}}{{selectedAddress.detail}}</text>
        </view>
        <text class="change-btn">更换/新增</text>
      </view>
      <view wx:else class="no-address">
        <text class="select-address-text">【+点击设置收货信息】</text>
      </view>
    </view>
  </view>

  <!-- 优惠券选择 -->
  <view class="coupon-section">
    <view class="coupon-selector" bindtap="selectCoupon">
      <view class="coupon-icon">🎫</view>
      <view class="coupon-info">
        <view wx:if="{{selectedCoupon}}" class="selected-coupon">
          <text class="coupon-name">{{selectedCoupon.template_name}}</text>
          <text class="coupon-desc">-¥{{selectedCoupon.value}}</text>
        </view>
        <view wx:else class="no-coupon">
          <text class="select-coupon-text">选择优惠券</text>
          <view wx:if="{{availableCoupons.length > 0}}" class="coupon-status">
            <text class="available-count" wx:if="{{usableCouponsCount > 0}}">{{usableCouponsCount}}张可用</text>
            <text class="unavailable-count" wx:if="{{availableCoupons.length - usableCouponsCount > 0}}">{{availableCoupons.length - usableCouponsCount}}张不满足条件</text>
          </view>
        </view>
      </view>
      <text class="arrow">></text>
    </view>

    <!-- 优惠券列表展示 -->
    <view wx:if="{{availableCoupons.length > 0}}" class="coupon-list">
      <view wx:for="{{availableCoupons}}" wx:key="id" class="coupon-item {{item.isUsable ? '' : 'disabled'}}" bindtap="{{item.isUsable ? 'selectSpecificCoupon' : 'showCouponTip'}}" data-coupon="{{item}}">
        <view class="coupon-left">
          <view class="coupon-value">¥{{item.value}}</view>
          <view class="coupon-condition">
            <text wx:if="{{item.thresholdAmount > 0}}">满{{item.thresholdAmount}}减{{item.value}}</text>
            <text wx:else>无门槛券</text>
          </view>
        </view>
        <view class="coupon-right">
          <view class="coupon-title">{{item.template_name}}</view>
          <view class="coupon-validity">{{item.valid_from}} 至 {{item.valid_to}}</view>
          <view wx:if="{{!item.isUsable}}" class="coupon-tip">{{item.reasonNotUsable}}</view>
        </view>
        <view class="coupon-status-icon">
          <text wx:if="{{selectedCoupon && selectedCoupon.id === item.id}}" class="selected-icon">✓</text>
          <text wx:elif="{{!item.isUsable}}" class="disabled-icon">✗</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 订单总价 -->
  <view class="price-section">
    <view class="price-detail">
      <view class="price-row">
        <text class="price-label">商品总价：</text>
        <text class="price-value">￥{{originalPrice}}</text>
      </view>
      <view wx:if="{{selectedCoupon}}" class="price-row discount-row">
        <text class="price-label">优惠券：</text>
        <text class="discount-value">-￥{{selectedCoupon.value}}</text>
      </view>
    </view>
    <view class="total-price">
      <text class="price-label">实付金额：</text>
      <text class="price-value final-price">￥{{finalPrice}}</text>
    </view>
  </view>

  <!-- 确认付款按钮 -->
  <view class="payment-section">
    <button class="payment-btn" bindtap="confirmPayment" disabled="{{!selectedAddress}}">
      确认付款
    </button>
  </view>

  <!-- 添加地址按钮（当没有地址时显示） -->
  <view wx:if="{{addressList.length === 0}}" class="add-address-section">
    <button class="add-address-btn" bindtap="addAddress">
      添加收货地址
    </button>
  </view>
</view>
