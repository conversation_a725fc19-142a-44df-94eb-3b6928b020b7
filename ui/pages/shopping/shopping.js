const config = require('../../config/setting');
const { getImageUrl } = config;

Page({
  data: {
    cartList: [],
    checkedIds: [],
    allChecked: false,
    totalPrice: 0,
    // 滑动相关数据
    touchStartX: 0,
    touchStartY: 0,
    currentSwipeIndex: -1,
    swipeThreshold: 80, // 滑动阈值
    // 防抖相关
    updateTimer: null
  },
  onShow() {
    this.loadCart();
  },
  loadCart() {
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';
    if (!mobile) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }
    wx.request({
      url: config.rootUrl + '/shoppingcar/list',
      method: 'GET',
      data: { mobile },
      success: (res) => {
        if (res.data && res.data.success) {
          const cartList = res.data.data.map(item => {
            // 使用后端返回的单价（已经是产品售价+工艺价格的总和，单位为元）
            const unit_price = Number(item.unit_price);
            const quantity = Number(item.quantity);
            return {
              ...item,
              id: item.cart_id,
              img: getImageUrl(item.img), // 处理图片URL
              price: unit_price, // 单价（产品售价+工艺价格）
              quantity: quantity,
              amount: item.amount, // 使用后端计算好的总价
              swipeOffset: 0 // 初始化滑动偏移量
            };
          });
          this.setData({ cartList, checkedIds: [], allChecked: false }, this.calcTotal);
        }
      }
    });
  },
  // 商品复选框组变化
  onCheckboxGroupChange(e) {
    const checkedIds = e.detail;
    const cartList = this.data.cartList.map(item => ({
      ...item,
      checked: checkedIds.includes(String(item.id)) || checkedIds.includes(item.id)
    }));
    const allChecked = cartList.length > 0 && cartList.every(item => item.checked);
    this.setData({ checkedIds, cartList, allChecked }, this.calcTotal);
  },
  // 全选/取消全选
  onCheckAll(e) {
    // 先隐藏所有展开的删除按钮
    this.hideAllDeleteButtons();

    const allChecked = !this.data.allChecked;
    const checkedIds = allChecked ? this.data.cartList.map(item => String(item.id)) : [];
    const cartList = this.data.cartList.map(item => ({
      ...item,
      checked: allChecked
    }));
    this.setData({ allChecked, checkedIds, cartList }, this.calcTotal);
  },
  // 计算总价
  calcTotal() {
    let total = 0;
    this.data.cartList.forEach(item => {
      if (item.checked) {
        total += item.price * item.quantity;
      }
    });
    this.setData({ totalPrice: total.toFixed(2) });
  },
  // 提交订单
  onSubmit() {
    const checkedItems = this.data.cartList.filter(item => item.checked);
    if (checkedItems.length === 0) {
      wx.showToast({
        title: '请选择要提交的商品',
        icon: 'none'
      });
      return;
    }

    // 计算总价
    const totalPrice = checkedItems.reduce((sum, item) => {
      return sum + (Number(item.price) * Number(item.quantity));
    }, 0).toFixed(2);

    // 跳转到订单确认页面，传递选中的商品信息
    wx.navigateTo({
      url: `/pages/orderConfirm/orderConfirm?from=cart&totalPrice=${totalPrice}&items=${encodeURIComponent(JSON.stringify(checkedItems))}`
    });
  },

  // 修改商品
  onModify(e) {
    const id = e.currentTarget.dataset.id;
    // 跳转到商品详情页进行修改
    wx.navigateTo({
      url: `/pages/productDetail/productDetail?id=${id}&from=cart`
    });
  },

  // 查看商品
  onView(e) {
    const id = e.currentTarget.dataset.id;
    const item = this.data.cartList.find(item => item.id == id);
    if (item && item.product_id) {
      wx.navigateTo({
        url: `/pages/productDetail/productDetail?id=${item.product_id}`
      });
    }
  },

  // 删除单个商品
  onDelete(e) {
    const id = e.currentTarget.dataset.id;
    const item = this.data.cartList.find(item => item.id == id);

    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${item.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteCartItem(id);
        }
      }
    });
  },

  // 批量删除选中商品
  onBatchDelete() {
    const checkedItems = this.data.cartList.filter(item => item.checked);
    if (checkedItems.length === 0) {
      wx.showToast({
        title: '请选择要删除的商品',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: `确定要删除选中的${checkedItems.length}个商品吗？`,
      success: (res) => {
        if (res.confirm) {
          this.batchDeleteCartItems(checkedItems.map(item => item.id));
        }
      }
    });
  },

  // 删除单个购物车商品
  deleteCartItem(id) {
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';

    wx.showLoading({ title: '删除中...' });

    // 找到对应的购物车项，获取真实的cart_id
    const cartItem = this.data.cartList.find(item => item.id == id);
    const cartId = cartItem ? cartItem.cart_id : id;

    wx.request({
      url: config.rootUrl + '/shoppingcar/delete',
      method: 'POST',
      data: { id: cartId, mobile },
      success: (res) => {
        wx.hideLoading();
        console.log('删除响应:', res.data);
        if (res.data && res.data.success) {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          this.loadCart(); // 重新加载购物车
        } else {
          wx.showToast({
            title: res.data.message || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('删除请求失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 批量删除购物车商品
  batchDeleteCartItems(ids) {
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';

    wx.showLoading({ title: '删除中...' });

    // 将前端的id转换为真实的cart_id
    const cartIds = ids.map(id => {
      const cartItem = this.data.cartList.find(item => item.id == id);
      return cartItem ? cartItem.cart_id : id;
    });

    wx.request({
      url: config.rootUrl + '/shoppingcar/batch-delete',
      method: 'POST',
      data: { ids: cartIds, mobile },
      success: (res) => {
        wx.hideLoading();
        console.log('批量删除响应:', res.data);
        if (res.data && res.data.success) {
          wx.showToast({
            title: res.data.message || '删除成功',
            icon: 'success'
          });
          this.loadCart(); // 重新加载购物车
        } else {
          wx.showToast({
            title: res.data.message || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('批量删除请求失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 触摸开始
  onTouchStart(e) {
    const touch = e.touches[0];
    this.setData({
      touchStartX: touch.clientX,
      touchStartY: touch.clientY
    });
  },

  // 触摸移动
  onTouchMove(e) {
    const touch = e.touches[0];
    const { touchStartX, touchStartY, swipeThreshold } = this.data;
    const deltaX = touch.clientX - touchStartX;
    const deltaY = touch.clientY - touchStartY;
    const index = e.currentTarget.dataset.index;

    // 判断是否为水平滑动
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
      // 阻止页面滚动
      e.preventDefault && e.preventDefault();

      // 限制滑动距离
      let offset = deltaX;
      if (offset > 0) {
        offset = 0; // 不允许向右滑动
      } else if (offset < -160) {
        offset = -160; // 最大左滑距离
      }

      // 更新当前项的滑动偏移
      const cartList = this.data.cartList.map((item, i) => {
        if (i === index) {
          return { ...item, swipeOffset: offset };
        } else {
          return { ...item, swipeOffset: 0 }; // 重置其他项
        }
      });

      this.setData({
        cartList,
        currentSwipeIndex: index
      });
    }
  },

  // 触摸结束
  onTouchEnd(e) {
    const { touchStartX, swipeThreshold } = this.data;
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStartX;
    const index = e.currentTarget.dataset.index;

    // 判断是否达到删除阈值
    if (deltaX < -swipeThreshold) {
      // 显示删除按钮
      this.showDeleteButton(index);
    } else {
      // 回弹到原位
      this.hideDeleteButton(index);
    }
  },

  // 显示删除按钮
  showDeleteButton(index) {
    const cartList = this.data.cartList.map((item, i) => {
      if (i === index) {
        return { ...item, swipeOffset: -160 };
      } else {
        return { ...item, swipeOffset: 0 };
      }
    });

    this.setData({
      cartList,
      currentSwipeIndex: index
    });
  },

  // 隐藏删除按钮
  hideDeleteButton(index) {
    const cartList = this.data.cartList.map((item, i) => {
      return { ...item, swipeOffset: 0 };
    });

    this.setData({
      cartList,
      currentSwipeIndex: -1
    });
  },

  // 隐藏所有删除按钮
  hideAllDeleteButtons() {
    const cartList = this.data.cartList.map(item => ({
      ...item,
      swipeOffset: 0
    }));

    this.setData({
      cartList,
      currentSwipeIndex: -1
    });
  },

  // 滑动删除
  onSwipeDelete(e) {
    const id = e.currentTarget.dataset.id;
    const item = this.data.cartList.find(item => item.id == id);

    wx.showModal({
      title: '确认删除',
      content: `确定要删除"${item.title}"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.deleteCartItem(id);
        } else {
          // 取消删除，隐藏删除按钮
          this.hideDeleteButton(this.data.currentSwipeIndex);
        }
      }
    });
  },

  // 购物车项点击事件（阻止冒泡）
  onCartItemTap(e) {
    // 如果有展开的删除按钮，先隐藏它们
    if (this.data.currentSwipeIndex !== -1) {
      this.hideAllDeleteButtons();
    }
  },

  // 增加数量
  onQuantityIncrease(e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    // 先隐藏删除按钮
    this.hideAllDeleteButtons();

    const cartList = [...this.data.cartList];
    const item = cartList[index];

    // 增加数量
    item.quantity += 1;
    item.amount = (item.price * item.quantity).toFixed(2);

    this.setData({ cartList }, () => {
      this.calcTotal();
      this.updateCartQuantity(id, item.quantity);
    });
  },

  // 减少数量
  onQuantityDecrease(e) {
    const id = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    // 先隐藏删除按钮
    this.hideAllDeleteButtons();

    const cartList = [...this.data.cartList];
    const item = cartList[index];

    // 如果数量已经是1，询问是否删除
    if (item.quantity <= 1) {
      wx.showModal({
        title: '确认删除',
        content: `确定要删除"${item.title}"吗？`,
        confirmText: '删除',
        confirmColor: '#ff4d6b',
        success: (res) => {
          if (res.confirm) {
            this.deleteCartItem(id);
          }
        }
      });
      return;
    }

    // 减少数量
    item.quantity -= 1;
    item.amount = (item.price * item.quantity).toFixed(2);

    this.setData({ cartList }, () => {
      this.calcTotal();
      this.updateCartQuantity(id, item.quantity);
    });
  },

  // 更新服务器端购物车数量（带防抖）
  updateCartQuantity(cartId, quantity) {
    // 获取用户信息
    const userInfo = wx.getStorageSync('userInfo');
    const mobile = userInfo ? userInfo.mobile : '';
    if (!mobile) {
      wx.showToast({ title: '请先登录', icon: 'none' });
      return;
    }

    // 清除之前的定时器
    if (this.data.updateTimer) {
      clearTimeout(this.data.updateTimer);
    }

    // 设置新的定时器，500ms后执行更新
    const timer = setTimeout(() => {
      wx.request({
        url: config.rootUrl + '/shoppingcar/update',
        method: 'POST',
        data: {
          cart_id: cartId,
          quantity: quantity,
          mobile: mobile
        },
        success: (res) => {
          if (res.data && res.data.success) {
            console.log('数量更新成功');
          } else {
            wx.showToast({
              title: res.data?.message || '更新失败',
              icon: 'none'
            });
            // 如果更新失败，重新加载购物车数据
            this.loadCart();
          }
        },
        fail: (err) => {
          console.error('更新数量失败:', err);
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          });
          // 网络错误时重新加载购物车数据
          this.loadCart();
        }
      });
    }, 500);

    this.setData({ updateTimer: timer });
  }
});
