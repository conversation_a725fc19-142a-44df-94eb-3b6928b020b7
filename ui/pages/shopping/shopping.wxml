<view class="shopping-top-bar">购物车</view>
<view class="shopping-container" bindtap="hideAllDeleteButtons">
<van-checkbox-group value="{{checkedIds}}" bind:change="onCheckboxGroupChange">
  <block wx:for="{{cartList}}" wx:key="id">
    <!-- 可滑动的购物车项容器 -->
    <view class="swipe-item-container">
      <!-- 删除按钮背景 -->
      <view class="swipe-delete-bg">
        <view class="swipe-delete-btn" bindtap="onSwipeDelete" data-id="{{item.id}}">
          <view class="delete-icon">
            <view class="trash-can">
              <view class="trash-lid"></view>
              <view class="trash-body">
                <view class="trash-line"></view>
                <view class="trash-line"></view>
                <view class="trash-line"></view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 主要内容区域 -->
      <view class="cart-item swipe-content"
            style="transform: translateX({{item.swipeOffset || 0}}rpx);"
            bindtouchstart="onTouchStart"
            bindtouchmove="onTouchMove"
            bindtouchend="onTouchEnd"
            catchtap="onCartItemTap"
            data-id="{{item.id}}"
            data-index="{{index}}">
        <van-checkbox name="{{item.id}}" shape="square" icon-size="20px" checked-color="#ff4d6b"></van-checkbox>
        <image class="cart-img" src="{{item.img}}" mode="aspectFill" />
        <view class="cart-info">
          <view class="cart-title">
            {{item.title}}
            <text class="cart-process">{{item.process_name}}</text>
          </view>
          <view class="cart-row">
            <view class="price-info">
              <text class="unit-price">￥{{item.price}}</text>
              <text class="total-amount">￥{{item.amount}}</text>
            </view>
            <view class="quantity-control">
              <view class="quantity-btn {{item.quantity <= 1 ? 'disabled' : ''}}"
                    bindtap="onQuantityDecrease"
                    data-id="{{item.id}}"
                    data-index="{{index}}">-</view>
              <text class="quantity-num">{{item.quantity}}</text>
              <view class="quantity-btn"
                    bindtap="onQuantityIncrease"
                    data-id="{{item.id}}"
                    data-index="{{index}}">+</view>
            </view>
          </view>
          <view class="cart-actions">
            <button class="cart-btn" bindtap="onModify" data-id="{{item.id}}">修改</button>
            <button class="cart-btn" bindtap="onView" data-id="{{item.id}}">查看</button>
          </view>
        </view>
      </view>
    </view>
  </block>
</van-checkbox-group>
</view>

<view class="footer-bar-fixed">
  <view class="custom-checkbox footer-checkbox" bindtap="onCheckAll">
    <view class="checkbox-icon {{allChecked ? 'checked' : ''}}">
      <text class="checkmark" wx:if="{{allChecked}}">✓</text>
    </view>
    <text class="checkbox-label">全选</text>
  </view>
  <text class="footer-total">合计：<text class="footer-price">￥{{totalPrice}}</text></text>
  <button class="footer-btn delete-btn" bindtap="onBatchDelete">删除选中</button>
  <button class="footer-btn" bindtap="onSubmit">提交订单</button>
</view>