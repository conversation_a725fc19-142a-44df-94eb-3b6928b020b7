.cart-list {
  padding: 16rpx;
}

/* 滑动容器样式 */
.swipe-item-container {
  position: relative;
  margin: 24rpx 32rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
}

/* 删除按钮背景 */
.swipe-delete-bg {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 160rpx;
  background: linear-gradient(135deg, #ff4757, #ff6b6b);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  border-radius: 0 16rpx 16rpx 0;
}

/* 滑动删除按钮 */
.swipe-delete-btn {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.swipe-delete-btn:active {
  transform: scale(0.9);
  background: rgba(255, 255, 255, 0.3);
}

/* 删除图标 */
.delete-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 垃圾桶图标 */
.trash-can {
  position: relative;
  width: 48rpx;
  height: 48rpx;
}

.trash-lid {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  width: 32rpx;
  height: 6rpx;
  background: #fff;
  border-radius: 3rpx;
}

.trash-lid::before {
  content: '';
  position: absolute;
  top: -6rpx;
  left: 8rpx;
  width: 16rpx;
  height: 4rpx;
  background: #fff;
  border-radius: 2rpx;
}

.trash-body {
  position: absolute;
  top: 16rpx;
  left: 12rpx;
  width: 24rpx;
  height: 28rpx;
  background: #fff;
  border-radius: 0 0 4rpx 4rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  padding: 4rpx 0;
}

.trash-line {
  width: 2rpx;
  height: 16rpx;
  background: #ff4757;
  border-radius: 1rpx;
}

/* 主要内容区域 */
.swipe-content {
  position: relative;
  z-index: 2;
  background: #fff;
  transition: transform 0.3s ease;
  border-radius: 16rpx;
  overflow: hidden;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-radius: 16rpx;
  margin-bottom: 0;
  gap: 8rpx;
}
.cart-checkbox {
  margin-right: 16rpx;
  margin-top: 16rpx;
}
.cart-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  background: #f5f5f5;
}
.cart-info {
  flex: 1;
}
.cart-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.cart-process {
  font-size: 24rpx;
  color: #888;
  margin-left: 12rpx;
}
.cart-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
  gap: 16rpx;
}

/* 价格信息区域 */
.price-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4rpx;
}

.unit-price {
  color: #888;
  font-size: 24rpx;
}

.total-amount {
  color: #ff4d6b;
  font-size: 32rpx;
  font-weight: bold;
}

/* 数量控制器 */
.quantity-control {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 24rpx;
  padding: 4rpx;
  border: 1rpx solid #e9ecef;
  box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.04);
}

.quantity-btn {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #ff4d6b;
  border: 1rpx solid #ff4d6b;
  transition: all 0.2s ease;
  user-select: none;
  box-shadow: 0 1rpx 3rpx rgba(255, 77, 107, 0.1);
}

.quantity-btn:active {
  transform: scale(0.95);
  background: #ff4d6b;
  color: #fff;
  box-shadow: 0 2rpx 6rpx rgba(255, 77, 107, 0.3);
}

.quantity-btn.disabled {
  color: #ccc;
  border-color: #e9ecef;
  background: #f8f9fa;
  box-shadow: none;
}

.quantity-btn.disabled:active {
  transform: none;
  background: #f8f9fa;
  color: #ccc;
}

.quantity-num {
  min-width: 50rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin: 0 12rpx;
  background: #fff;
  border-radius: 12rpx;
  padding: 4rpx 0;
}


.cart-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}
.cart-btn {
  font-size: 22rpx;
  color: #ff4d6b;
  border: 1rpx solid #ff4d6b;
  border-radius: 12rpx;
  background: #fff;
  padding: 6rpx 16rpx;
  height: 56rpx;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.cart-btn.delete {
  color: #fff;
  background: #ff4d6b;
  border: none;
}
.shopping-top-bar {
  width: 100vw;
  height: 100rpx;
  background: #ffb6c1;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
  position: sticky;
  top: 0;
  z-index: 10;
}

.my-submit-bar {
  margin-bottom: -50rpx !important;
}

.footer-bar-fixed {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100rpx;
  background: #fff;
  display: flex;
  align-items: center;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.04);
  z-index: 100;
  padding: 0 24rpx;
  gap: 16rpx;
}

.footer-checkbox {
  margin-right: 16rpx;
}

/* 自定义复选框样式 */
.custom-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.checkbox-icon {
  width: 48rpx;
  height: 48rpx;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.3s ease;
  background-color: #fff;
}

.checkbox-icon.checked {
  background-color: #ff4d6b;
  border-color: #ff4d6b;
}

.checkmark {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  line-height: 1;
}

.checkbox-label {
  font-size: 32rpx;
  color: #333;
  user-select: none;
}

.footer-total {
  font-size: 32rpx;
  color: #333;
  flex: 1;
}

.footer-price {
  color: #ff4d6b;
  font-weight: bold;
  font-size: 36rpx;
  margin-left: 4rpx;
}

.footer-btn {
  background: linear-gradient(90deg, #ff4d6b, #ff7e5f);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 26rpx;
  padding: 0 32rpx;
  height: 72rpx;
  line-height: 72rpx;
  margin-left: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255,77,107,0.1);
  min-width: 140rpx;
  white-space: nowrap;
}

.footer-btn.delete-btn {
  background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
  margin-left: auto;
  margin-right: 8rpx;
  min-width: 120rpx;
}
