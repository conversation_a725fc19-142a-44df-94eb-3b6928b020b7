page {
  background-color: #f5f5f5;
}

.top-bar {
  width: 100%;
  height: 100rpx;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  box-shadow: 0 2rpx 10rpx rgba(255, 182, 193, 0.3);
}

.top-bar-title {
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  letter-spacing: 2rpx;
}

.page-content {
  margin-top: 100rpx;
  min-height: calc(100vh - 100rpx);
}

/* 标签切换 */
.tab-container {
  display: flex;
  background: #fff;
  margin: 20rpx;
  border-radius: 50rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(255, 182, 193, 0.2);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #999;
  background: #fff;
  transition: all 0.3s ease;
}

.tab-item.active {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #fff;
  font-weight: bold;
}

/* 优惠券列表 */
.coupon-list, .my-coupon-list {
  padding: 0 20rpx 40rpx;
}

.loading, .empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 优惠券卡片 */
.coupon-card, .my-coupon-card {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  padding: 4rpx;
  position: relative;
  overflow: hidden;
}

.my-coupon-card.disabled {
  background: linear-gradient(135deg, #ccc 0%, #e0e0e0 100%);
}

.coupon-content {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  display: flex;
  align-items: center;
  position: relative;
}

/* 优惠券左侧 - 金额 */
.coupon-left {
  width: 160rpx;
  text-align: center;
  position: relative;
}

.coupon-left::after {
  content: '';
  position: absolute;
  right: -15rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 2rpx;
  height: 80rpx;
  background: linear-gradient(to bottom, transparent 0%, #ff9a9e 20%, #fecfef 80%, transparent 100%);
}

.coupon-value {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 10rpx;
}

.currency {
  font-size: 24rpx;
  color: #ff6b9d;
  font-weight: bold;
}

.amount {
  font-size: 48rpx;
  color: #ff6b9d;
  font-weight: bold;
  line-height: 1;
}

.coupon-condition {
  font-size: 22rpx;
  color: #ff6b9d;
  background: rgba(255, 107, 157, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 优惠券右侧 - 信息 */
.coupon-right {
  flex: 1;
  padding-left: 30rpx;
}

.coupon-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  line-height: 1.3;
}

.coupon-validity {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.coupon-remaining {
  font-size: 22rpx;
  color: #ff6b9d;
}

.coupon-code {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 优惠券操作按钮 */
.coupon-action {
  width: 160rpx;
  text-align: center;
}

.claim-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #fff;
  border: none;
  border-radius: 30rpx;
  padding: 16rpx 20rpx;
  font-size: 22rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.4);
  white-space: nowrap;
  width: 140rpx;
  height: 60rpx;
  line-height: 60rpx;
  min-width: 120rpx;
  text-align: center;
}

.claim-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 154, 158, 0.4);
}

.btn-text {
  line-height: 1.2;
  text-align: center;
}

/* 我的优惠券状态 */
.coupon-status {
  width: 120rpx;
  text-align: center;
}

.status-available {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #fff;
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: bold;
  display: inline-block;
}

.use-btn {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #fff;
  border: none;
  padding: 16rpx 24rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: bold;
  display: inline-block;
  box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.4);
}

.use-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 154, 158, 0.4);
}

.status-expired, .status-used, .status-disabled {
  color: #999;
  font-size: 24rpx;
  padding: 16rpx 24rpx;
  border: 2rpx solid #ddd;
  border-radius: 30rpx;
  display: inline-block;
}

/* 优惠券卡片装饰 */
.coupon-card::before,
.my-coupon-card::before {
  content: '';
  position: absolute;
  left: 160rpx;
  top: -10rpx;
  width: 20rpx;
  height: 20rpx;
  background: #f5f5f5;
  border-radius: 50%;
  z-index: 1;
}

.coupon-card::after,
.my-coupon-card::after {
  content: '';
  position: absolute;
  left: 160rpx;
  bottom: -10rpx;
  width: 20rpx;
  height: 20rpx;
  background: #f5f5f5;
  border-radius: 50%;
  z-index: 1;
}
