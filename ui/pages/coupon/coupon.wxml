<!-- 顶部条形栏 -->
<view class="top-bar">
  <text class="top-bar-title">领券中心</text>
</view>

<view class="page-content">
  <!-- 标签切换 -->
  <view class="tab-container">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      领取优惠券
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      我的优惠券
    </view>
  </view>

  <!-- 领取优惠券页面 -->
  <view wx:if="{{currentTab === 0}}" class="coupon-list">
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{availableCoupons.length === 0}}" class="empty-state">
      <text>暂无可领取的优惠券</text>
    </view>

    <view wx:else>
      <view wx:for="{{availableCoupons}}" wx:key="campaign_id" class="coupon-card">
        <view class="coupon-content">
          <view class="coupon-left">
            <view class="coupon-value">
              <text class="currency">¥</text>
              <text class="amount">{{item.value}}</text>
            </view>
            <view class="coupon-condition" wx:if="{{item.threshold_amount > 0}}">
              满{{item.threshold_amount}}减{{item.value}}
            </view>
            <view class="coupon-condition" wx:else>
              无门槛券
            </view>
          </view>

          <view class="coupon-right">
            <view class="coupon-title">{{item.template_name || item.campaign_name}}</view>
            <view class="coupon-validity">
              <text wx:if="{{item.validity_type === 1}}">
                {{item.valid_from}} 至 {{item.valid_to}}
              </text>
              <text wx:else>
                领取后{{item.valid_days_after_claim}}天内有效
              </text>
            </view>
            <view class="coupon-remaining" wx:if="{{item.max_quantity !== -1}}">
              剩余：{{item.remaining_quantity}}
            </view>
          </view>

          <view class="coupon-action">
            <button class="claim-btn" bindtap="claimCoupon" data-campaign-id="{{item.campaign_id}}">
              <view class="btn-text">立即<br/>领取</view>
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 我的优惠券页面 -->
  <view wx:if="{{currentTab === 1}}" class="my-coupon-list">
    <view wx:if="{{loading}}" class="loading">
      <text>加载中...</text>
    </view>

    <view wx:elif="{{myCoupons.length === 0}}" class="empty-state">
      <text>您还没有优惠券</text>
    </view>

    <view wx:else>
      <view wx:for="{{myCoupons}}" wx:key="id" class="my-coupon-card {{item.status === 1 && !item.is_expired ? '' : 'disabled'}}">
        <view class="coupon-content">
          <view class="coupon-left">
            <view class="coupon-value">
              <text class="currency">¥</text>
              <text class="amount">{{item.value}}</text>
            </view>
            <view class="coupon-condition" wx:if="{{item.threshold_amount > 0}}">
              满{{item.threshold_amount}}减{{item.value}}
            </view>
            <view class="coupon-condition" wx:else>
              无门槛券
            </view>
          </view>

          <view class="coupon-right">
            <view class="coupon-title">{{item.template_name || item.campaign_name}}</view>
            <view class="coupon-validity">
              {{item.valid_from}} 至 {{item.valid_to}}
            </view>
          </view>

          <view class="coupon-status">
            <button wx:if="{{item.status === 1 && !item.is_expired}}" class="use-btn" bindtap="useCoupon" data-coupon-id="{{item.id}}">去使用</button>
            <text wx:elif="{{item.is_expired}}" class="status-expired">已过期</text>
            <text wx:elif="{{item.status === 2}}" class="status-used">已使用</text>
            <text wx:else class="status-disabled">不可用</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
