const config = require('../../config/setting');
var app = getApp();

Page({
  data: {
    currentTab: 0, // 0: 领取优惠券, 1: 我的优惠券
    availableCoupons: [], // 可领取的优惠券
    myCoupons: [], // 我的优惠券
    loading: false,
    userInfo: null
  },

  onLoad() {
    this.checkLoginStatus();
  },

  onShow() {
    this.checkLoginStatus();
    if (this.data.currentTab === 0) {
      this.loadAvailableCoupons();
    } else {
      this.loadMyCoupons();
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLogin = app.checkLogin();
    let userInfo = app.getUserInfo();

    console.log('用户登录状态:', isLogin);
    console.log('用户信息:', userInfo);

    // 如果没有用户信息，使用测试用户信息
    if (!userInfo || !userInfo.id) {
      userInfo = {
        id: 3259,
        mobile: '13800138001',
        nickname: '测试用户',
        name: '测试用户'
      };
      console.log('使用测试用户信息:', userInfo);
    }

    this.setData({
      userInfo: userInfo
    });

    // 暂时注释掉登录检查，方便测试
    // if (!isLogin) {
    //   wx.showToast({
    //     title: '请先登录',
    //     icon: 'none'
    //   });
    //   setTimeout(() => {
    //     wx.navigateTo({
    //       url: '/pages/number_login/number_login'
    //     });
    //   }, 1500);
    //   return false;
    // }
    return true;
  },

  // 切换标签
  switchTab(e) {
    const tab = parseInt(e.currentTarget.dataset.tab);
    this.setData({
      currentTab: tab
    });

    if (tab === 0) {
      this.loadAvailableCoupons();
    } else {
      this.loadMyCoupons();
    }
  },

  // 加载可领取的优惠券
  loadAvailableCoupons() {
    if (!this.checkLoginStatus()) return;

    this.setData({ loading: true });

    const userInfo = this.data.userInfo;
    const params = {
      user_id: userInfo.id,
      phone_number: userInfo.mobile || userInfo.phone_number
    };

    wx.request({
      url: config.rootUrl + '/coupons/available',
      method: 'GET',
      data: params,
      success: (res) => {
        console.log('可领取优惠券:', res.data);
        if (res.data.success) {
          // 格式化日期
          const coupons = res.data.data.map(item => ({
            ...item,
            valid_from: this.formatDate(item.valid_from),
            valid_to: this.formatDate(item.valid_to)
          }));

          this.setData({
            availableCoupons: coupons,
            loading: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 加载我的优惠券
  loadMyCoupons() {
    if (!this.checkLoginStatus()) return;

    this.setData({ loading: true });

    const userInfo = this.data.userInfo;
    const params = {
      user_id: userInfo.id,
      phone_number: userInfo.mobile || userInfo.phone_number
    };

    console.log('查询我的优惠券，参数:', params);

    wx.request({
      url: config.rootUrl + '/coupons/my',
      method: 'GET',
      data: params,
      success: (res) => {
        console.log('我的优惠券API响应:', res);
        console.log('响应数据:', res.data);

        if (res.data && res.data.success) {
          console.log('原始优惠券数据:', res.data.data);

          // 格式化日期
          const coupons = res.data.data.map(item => ({
            ...item,
            valid_from: this.formatDate(item.valid_from),
            valid_to: this.formatDate(item.valid_to)
          }));

          console.log('处理后的优惠券数据:', coupons);
          console.log('设置到页面的数据长度:', coupons.length);

          this.setData({
            myCoupons: coupons,
            loading: false
          }, () => {
            console.log('页面数据设置完成，当前myCoupons:', this.data.myCoupons);
          });
        } else {
          console.log('查询失败:', res.data ? res.data.message : '响应格式错误');
          wx.showToast({
            title: (res.data && res.data.message) || '加载失败',
            icon: 'none'
          });
          this.setData({ loading: false });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({ loading: false });
      }
    });
  },

  // 领取优惠券
  claimCoupon(e) {
    if (!this.checkLoginStatus()) return;

    const campaignId = e.currentTarget.dataset.campaignId;
    const userInfo = this.data.userInfo;

    console.log('领取优惠券，用户信息:', userInfo);
    console.log('活动ID:', campaignId);

    wx.showLoading({
      title: '领取中...'
    });

    const requestData = {
      user_id: userInfo.id,
      phone_number: userInfo.mobile || userInfo.phone_number,
      campaign_id: campaignId
    };

    console.log('领取优惠券请求数据:', requestData);

    wx.request({
      url: config.rootUrl + '/coupons/claim',
      method: 'POST',
      data: requestData,
      success: (res) => {
        wx.hideLoading();
        console.log('领取优惠券结果:', res.data);

        if (res.data.success) {
          wx.showToast({
            title: '领取成功！',
            icon: 'success'
          });

          // 重新加载可领取的优惠券列表和我的优惠券列表
          this.loadAvailableCoupons();
          this.loadMyCoupons();
        } else {
          wx.showToast({
            title: res.data.message || '领取失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('领取失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 使用优惠券 - 跳转到产品页
  useCoupon(e) {
    console.log('点击了去使用按钮');
    wx.switchTab({
      url: '/pages/product/product',
      success: () => {
        console.log('跳转成功');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        wx.showToast({
          title: '跳转失败',
          icon: 'none'
        });
      }
    });
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}/${month}/${day}`;
  }
});