// addressEdit页面
const config = require('../../config/setting');

Page({
  data: {
    isEdit: false,
    addressId: null,
    returnUrl: '', // 添加返回URL参数
    formData: {
      name: '',
      phone: '',
      province: '',
      city: '',
      district: '',
      detail: '',
      is_default: false
    },
    regionValue: []
  },

  onLoad(options) {
    // 获取返回URL参数
    const returnUrl = options.returnUrl ? decodeURIComponent(options.returnUrl) : '';

    if (options.id) {
      this.setData({
        isEdit: true,
        addressId: options.id,
        returnUrl: returnUrl
      });
      this.loadAddressDetail(options.id);
    } else {
      this.setData({
        returnUrl: returnUrl
      });
    }
  },

  // 加载地址详情
  loadAddressDetail(id) {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: config.rootUrl + '/addresses',
      method: 'GET',
      data: {
        user_id: userInfo.id
      },
      success: (res) => {
        if (res.data.success) {
          const address = res.data.data.find(item => item.id == id);
          if (address) {
            this.setData({
              formData: {
                name: address.name,
                phone: address.phone,
                province: address.province,
                city: address.city,
                district: address.district,
                detail: address.detail,
                is_default: address.is_default === 1
              },
              regionValue: [address.province, address.city, address.district]
            });
          }
        }
      }
    });
  },

  // 地区选择变化
  onRegionChange(e) {
    const [province, city, district] = e.detail.value;
    this.setData({
      'formData.province': province,
      'formData.city': city,
      'formData.district': district,
      regionValue: e.detail.value
    });
  },

  // 表单提交
  onSubmit(e) {
    const formData = e.detail.value;
    const userInfo = wx.getStorageSync('userInfo');

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 表单验证
    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入收货人姓名',
        icon: 'none'
      });
      return;
    }

    if (!formData.phone.trim() || !/^1[3-9]\d{9}$/.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return;
    }

    if (!this.data.formData.province || !this.data.formData.city || !this.data.formData.district) {
      wx.showToast({
        title: '请选择所在地区',
        icon: 'none'
      });
      return;
    }

    if (!formData.detail.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return;
    }

    const submitData = {
      user_id: userInfo.id,
      name: formData.name.trim(),
      phone: formData.phone.trim(),
      province: this.data.formData.province,
      city: this.data.formData.city,
      district: this.data.formData.district,
      detail: formData.detail.trim(),
      is_default: formData.is_default
    };

    const url = this.data.isEdit
      ? `${config.rootUrl}/addresses/${this.data.addressId}`
        : `${config.rootUrl}/addresses`;
    
    const method = this.data.isEdit ? 'PUT' : 'POST';

    wx.showLoading({ title: '保存中...' });

    wx.request({
      url: url,
      method: method,
      data: submitData,
      success: (res) => {
        wx.hideLoading();
        if (res.data.success) {
          wx.showToast({
            title: this.data.isEdit ? '修改成功' : '添加成功',
            icon: 'success'
          });
          setTimeout(() => {
            // 如果有返回URL，则跳转到指定页面，否则返回上一页
            if (this.data.returnUrl) {
              wx.redirectTo({
                url: this.data.returnUrl
              });
            } else {
              wx.navigateBack();
            }
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  }
});

// 页面注册完成
