/* pages/addressEdit/addressEdit.wxss */
.container {
  padding: 0;
  background: #f7f7f7;
  min-height: 100vh;
  padding-top: env(safe-area-inset-top);
}

.form-section {
  background: #fff;
  margin: 24rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.form-item {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 160rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  padding-top: 8rpx; /* 使用padding而不是margin，更稳定 */
}

.form-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 8rpx 0; /* 添加上下padding，与标签对齐 */
  margin: 0;
  line-height: 1.5;
}

.form-input::placeholder {
  color: #999;
}

.form-textarea {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  min-height: 120rpx;
  padding: 8rpx 0 0 0; /* 顶部添加一点padding，与标签对齐 */
  margin: 0;
  width: 100%;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea::placeholder {
  color: #999;
}

/* 专门为textarea的容器添加样式 */
.form-item:has(.form-textarea) {
  align-items: flex-start;
}

/* 如果不支持:has选择器，使用类名 */
.form-item.textarea-item {
  align-items: flex-start;
  min-height: 160rpx; /* 确保有足够的高度 */
}

.textarea-item .form-label {
  padding-top: 8rpx; /* textarea标签与输入框顶部对齐 */
  align-self: flex-start; /* 确保标签从顶部开始 */
}

.picker-item {
  cursor: pointer;
}

.form-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  padding: 8rpx 0; /* 与其他输入框保持一致的padding */
  line-height: 1.5;
}

.form-value.placeholder {
  color: #999;
}

.arrow-right {
  font-size: 24rpx;
  color: #999;
  margin-left: 16rpx;
}

.switch-item {
  justify-content: space-between;
}

.submit-section {
  padding: 60rpx 24rpx;
}

.submit-btn {
  width: 100%;
  background: #ff4d6b;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(255, 77, 107, 0.3);
}

.submit-btn:active {
  background: #e6455f;
}

.submit-btn::after {
  border: none;
}
