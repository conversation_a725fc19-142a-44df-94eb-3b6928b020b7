<view class="container">
  <navigation-bar title="{{isEdit ? '编辑地址' : '添加地址'}}" back="{{true}}" color="black" background="#FFF"></navigation-bar>
  
  <form bindsubmit="onSubmit">
    <view class="form-section">
      <view class="form-item">
        <view class="form-label">收货人</view>
        <input class="form-input" name="name" value="{{formData.name}}" placeholder="请输入收货人姓名" maxlength="20" />
      </view>
      
      <view class="form-item">
        <view class="form-label">手机号</view>
        <input class="form-input" name="phone" value="{{formData.phone}}" placeholder="请输入手机号" type="number" maxlength="11" />
      </view>
      
      <picker mode="region" value="{{regionValue}}" bindchange="onRegionChange">
        <view class="form-item picker-item">
          <view class="form-label">所在地区</view>
          <view class="form-value {{!formData.province ? 'placeholder' : ''}}">
            {{formData.province && formData.city && formData.district ?
              formData.province + ' ' + formData.city + ' ' + formData.district :
              '请选择省市区'}}
          </view>
          <view class="arrow-right">></view>
        </view>
      </picker>
      
      <view class="form-item textarea-item">
        <view class="form-label">详细地址</view>
        <textarea class="form-textarea" name="detail" value="{{formData.detail}}" placeholder="请输入详细地址（街道、门牌号等）" maxlength="100" />
      </view>
      
      <view class="form-item switch-item">
        <view class="form-label">设为默认地址</view>
        <switch name="is_default" checked="{{formData.is_default}}" color="#ff4d6b" />
      </view>
    </view>
    
    <view class="submit-section">
      <button class="submit-btn" form-type="submit">{{isEdit ? '保存' : '添加'}}</button>
    </view>
  </form>


</view>
