<view class="container">
  <!-- 统一的导航栏 -->
  <navigation-bar title="登录" back="{{true}}" color="#d4756b" background="rgba(255, 255, 255, 0.95)"></navigation-bar>

  <view class="main">
    <view class="icon-view">
      <!-- 应用图标 -->
      <image src="/images/主页1.jpg" class="app-icon"></image>
      <text class="title">伊布布</text>
      <text class="subtitle">欢迎来到伊布布！</text>
    </view>
  </view>

  <!-- 微信登录按钮 -->
  <view class="login-section">
    <view class="wx-login-btn" bind:tap="wxLogin">
       <image src="/images/wx-icon.svg" class="wx-icon"></image>
       <text class="wx-login-text">微信一键登录</text>
     </view>
    
    <view class="privacy-tip">
      <text>登录即表示同意</text>
      <text class="privacy-link" bind:tap="showPrivacyPolicy">《隐私政策》</text>
    </view>
  </view>

  <!-- 隐私政策弹窗 -->
  <van-popup show="{{ showPrivacy }}" position="bottom" round bind:close="hidePrivacyPolicy">
    <view class="privacy-popup">
      <view class="popup-header">
        <text class="popup-title">隐私政策</text>
        <van-icon name="cross" bind:tap="hidePrivacyPolicy" class="close-icon" />
      </view>
      <view class="popup-content">
        <text class="privacy-text">为了更好地为您提供服务，我们需要获取您的微信昵称、头像等基本信息。我们承诺严格保护您的隐私，不会向第三方泄露您的个人信息。</text>
      </view>
      <view class="popup-actions">
        <view bind:tap="hidePrivacyPolicy" class="text-icon-btn cancel-text-btn">
          <text class="btn-icon">❌</text>
          <text class="btn-text">取消</text>
        </view>
        <view bind:tap="agreeAndLogin" class="text-icon-btn agree-text-btn">
          <text class="btn-icon">✅</text>
          <text class="btn-text">同意</text>
        </view>
      </view>
    </view>
  </van-popup>

  <!-- 手机号授权弹窗 -->
  <van-popup show="{{ showPhoneAuth }}" position="center" round>
    <view class="phone-auth-popup">
       <view class="popup-header">
         <text class="popup-title">获取手机号</text>
       </view>
       <view class="popup-content">
         <text class="phone-auth-text">为了更好地为您提供服务，需要获取您的手机号用于匹配优惠券。</text>
         <text class="phone-auth-desc">您可以选择跳过此步骤，不影响正常使用。</text>
       </view>
      <view class="popup-actions">
        <view bind:tap="navigateToMyPage" class="text-icon-btn skip-text-btn">
          <text class="btn-icon">⏭️</text>
          <text class="btn-text">跳过</text>
        </view>
        <button open-type="getPhoneNumber" bind:getphonenumber="getPhoneNumber" class="text-icon-btn phone-text-btn">
          <text class="btn-icon">📱</text>
          <text class="btn-text">获取手机号</text>
        </button>
      </view>
    </view>
  </van-popup>
</view>