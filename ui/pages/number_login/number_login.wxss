.container {
  padding: 20rpx;
  min-height: 100vh;
  position: relative;
}

/* 背景图片样式 */
.bg-image {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 236, 210, 0.8) 0%, rgba(252, 182, 159, 0.6) 100%);
  z-index: -1;
}

/* 修复导航栏穿透问题 */
.container navigation-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20rpx);
  border-bottom: 1rpx solid rgba(252, 182, 159, 0.2);
}

.main {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0 80rpx 0;
  position: relative;
  z-index: 1;
}

.icon-view {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 100rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 40rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(252, 182, 159, 0.3);
  backdrop-filter: blur(10rpx);
}

.app-icon {
  width: 140rpx;
  height: 140rpx;
  border-radius: 35rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 12rpx 40rpx rgba(252, 182, 159, 0.4);
  border: 4rpx solid rgba(255, 255, 255, 0.8);
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: #d4756b;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 8rpx rgba(212, 117, 107, 0.3);
}

.subtitle {
  font-size: 28rpx;
  color: #e8a798;
  font-weight: 400;
}

.login-section {
  padding: 0 60rpx;
  position: relative;
  z-index: 1;
}

.wx-login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #07c160;
  border-radius: 60rpx;
  padding: 36rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 16rpx 40rpx rgba(7, 193, 96, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
}

.wx-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.wx-login-btn:active::before {
  left: 100%;
}

.wx-login-btn:active {
  transform: scale(0.98);
}

.wx-icon {
  width: 52rpx;
  height: 52rpx;
  margin-right: 24rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(255, 255, 255, 0.8));
}

.wx-login-text {
  font-size: 36rpx;
  color: #fff;
  font-weight: 600;
  text-shadow: 0 2rpx 8rpx rgba(7, 193, 96, 0.6);
}

.privacy-tip {
  text-align: center;
  font-size: 24rpx;
  color: rgba(212, 117, 107, 0.8);
  background: rgba(255, 255, 255, 0.7);
  border-radius: 30rpx;
  padding: 20rpx;
  backdrop-filter: blur(10rpx);
}

.privacy-link {
  color: #d4756b;
  text-decoration: underline;
  margin-left: 10rpx;
  font-weight: 500;
}

/* 隐私政策弹窗样式 */
.privacy-popup {
  background: linear-gradient(135deg, #ffeef0 0%, #fff5f5 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(212, 117, 107, 0.2);
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #d4756b;
}

.close-icon {
  color: #d4756b;
  font-size: 40rpx;
}

.popup-content {
  margin-bottom: 40rpx;
}

.privacy-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.popup-actions {
  display: flex;
  gap: 30rpx;
  justify-content: space-around;
  align-items: center;
  padding: 20rpx 0;
}

/* 带文字的图标按钮样式 */
.text-icon-btn {
  width: 180rpx;
  height: 70rpx;
  border-radius: 35rpx;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin: 0 !important;
  padding: 0 !important;
  gap: 4rpx;
  cursor: pointer;
}

.cancel-text-btn {
  border: 2rpx solid #ff4757;
}

.agree-text-btn {
  border: 2rpx solid #2ed573;
}

.text-icon-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.7);
}

.cancel-text-btn:active {
  border-color: #ff3838;
  background: rgba(255, 71, 87, 0.1);
}

.agree-text-btn:active {
  border-color: #26d467;
  background: rgba(46, 213, 115, 0.1);
}

.btn-icon {
  font-size: 24rpx;
  line-height: 1;
}

.btn-text {
  font-size: 20rpx;
  line-height: 1;
  color: #333;
  font-weight: 500;
}

/* 手机号授权弹窗样式 */
.phone-auth-popup {
  background: linear-gradient(135deg, #ffeef0 0%, #fff5f5 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(212, 117, 107, 0.2);
  width: 600rpx;
}

.phone-auth-popup .popup-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.phone-auth-popup .popup-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #d4756b;
}

.phone-auth-popup .popup-content {
  text-align: center;
  margin-bottom: 40rpx;
}

.phone-auth-text {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.phone-auth-desc {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

.phone-auth-popup .popup-actions {
  display: flex;
  gap: 30rpx;
  align-items: center;
  justify-content: space-around;
  padding: 20rpx 0;
}

.skip-text-btn {
  border: 2rpx solid #ffa502;
}

.phone-text-btn {
  border: 2rpx solid #3742fa;
}

.skip-text-btn:active {
  border-color: #ff9500;
  background: rgba(255, 165, 2, 0.1);
}

.phone-text-btn:active {
  border-color: #2f3542;
  background: rgba(55, 66, 250, 0.1);
}
