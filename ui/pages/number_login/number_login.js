var app = getApp();//取到app.js对象
const config = require('../../config/setting.js');

Page({
  data: {
    showPrivacy: false,
    loading: false,
    showPhoneAuth: false
  },
  // 显示隐私政策弹窗
  showPrivacyPolicy() {
    this.setData({
      showPrivacy: true
    });
  },

  // 隐藏隐私政策弹窗
  hidePrivacyPolicy() {
    this.setData({
      showPrivacy: false
    });
  },

  // 微信登录
  wxLogin() {
    this.showPrivacyPolicy();
  },

  // 同意隐私政策并登录
  agreeAndLogin() {
    this.hidePrivacyPolicy();
    
    wx.showLoading({
      title: '登录中...'
    });

    // 先获取用户信息（必须在用户点击事件中直接调用）
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (profileRes) => {
        console.log('获取用户信息成功:', profileRes);
        console.log('用户详细信息:', {
          nickName: profileRes.userInfo.nickName,
          avatarUrl: profileRes.userInfo.avatarUrl,
          gender: profileRes.userInfo.gender,
          country: profileRes.userInfo.country,
          province: profileRes.userInfo.province,
          city: profileRes.userInfo.city,
          language: profileRes.userInfo.language
        });
        // 获取用户信息成功后，再获取微信登录凭证
        this.performWxLogin(profileRes.userInfo);
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err);
        wx.hideLoading();
        let errorMsg = '获取用户信息失败';
        if (err.errMsg && err.errMsg.includes('deny')) {
          errorMsg = '需要授权用户信息才能登录';
        } else if (err.errMsg && err.errMsg.includes('user TAP gesture')) {
          errorMsg = '请直接点击登录按钮';
        }
        wx.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 执行微信登录
  performWxLogin(userInfo) {
    // 获取微信登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('微信登录成功，code:', loginRes.code);
          this.sendWxLoginToServer(loginRes.code, userInfo);
        } else {
          console.error('微信登录失败:', loginRes.errMsg);
          wx.hideLoading();
          wx.showToast({
            title: '获取微信登录凭证失败，请重试',
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: (err) => {
        console.error('微信登录失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '微信登录服务异常，请重试',
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 发送微信登录信息到服务器
  sendWxLoginToServer(code, userInfo) {
    console.log('发送到服务器的用户信息:', {
      code: code,
      nickName: userInfo.nickName,
      avatarUrl: userInfo.avatarUrl,
      gender: userInfo.gender,
      country: userInfo.country,
      province: userInfo.province,
      city: userInfo.city
    });
    
    wx.request({
      url: config.wx_login || (config.rootUrl + '/wx_login'),
      method: 'POST',
      data: {
        code: code,
        userInfo: userInfo
      },
      success: (res) => {
        wx.hideLoading();
        console.log('微信登录响应:', res.data);
        
        if (res.data.code === 200) {
          // 登录成功，保存用户信息
          const data = res.data;
          const token = data.token || '';
          const id = data.id || null;
          const name = data.name || userInfo.nickName || '用户';
          const score = data.score || 0;
          const avatar = data.avatar || userInfo.avatarUrl || null;
          const mobile = data.mobile || '';

          console.log('服务器返回的原始数据:', res.data);
          console.log('本地获取的微信用户信息:', {
            nickName: userInfo.nickName,
            avatarUrl: userInfo.avatarUrl,
            gender: userInfo.gender
          });
          console.log('最终保存的用户信息:', { id, name, score, avatar, token, mobile });
          console.log('数据来源对比:', {
            name: data.name ? '服务器' : '微信',
            avatar: data.avatar ? '服务器' : '微信',
            mobile: mobile ? '已有手机号' : '无手机号'
          });

          // 保存到app.js中
          app.initUserInfo(id, name, score, avatar, token, mobile);
          
          // 设置登录成功标记，用于首页弹窗显示
          wx.setStorageSync('justLoggedIn', true);
          
          wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500
          });
          
          // 检查是否需要获取手机号
          if (!mobile || mobile === '') {
            // 用户还没有手机号，请求授权
            this.requestPhoneNumber();
          } else {
            // 用户已有手机号，直接跳转
            this.navigateToMyPage();
          }
        } else {
          // 登录失败，显示错误信息
          const errorMsg = res.data.msg || '登录失败';
          console.error('微信登录失败:', res.data);
          wx.showToast({
            title: errorMsg,
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('微信登录请求失败:', err);
        wx.showToast({
          title: '网络连接失败，请检查网络后重试',
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 请求获取手机号
  requestPhoneNumber() {
    wx.showModal({
      title: '获取手机号',
      content: '为了更好地为您提供服务，需要获取您的手机号用于匹配优惠券。',
      confirmText: '授权',
      cancelText: '跳过',
      success: (res) => {
        if (res.confirm) {
          // 用户同意授权，显示获取手机号按钮
          this.setData({
            showPhoneAuth: true
          });
        } else {
          // 用户选择跳过，直接跳转
          this.navigateToMyPage();
        }
      }
    });
  },

  // 获取手机号按钮点击事件
  getPhoneNumber(e) {
    console.log('获取手机号结果:', e.detail);
    
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      // 获取成功，发送到服务器
      wx.request({
        url: config.update_phone || (config.rootUrl + '/update_phone'),
        method: 'POST',
        data: {
          code: e.detail.code,
          token: app.globalData.token
        },
        success: (res) => {
          console.log('更新手机号响应:', res.data);
          if (res.data.code === 200) {
            wx.showToast({
              title: '手机号获取成功',
              icon: 'success',
              duration: 1500
            });
            // 更新本地存储的手机号
            app.globalData.mobile = res.data.mobile || '';
          } else {
            wx.showToast({
              title: '手机号获取失败',
              icon: 'none',
              duration: 2000
            });
          }
          // 隐藏授权弹窗并跳转
          this.setData({ showPhoneAuth: false });
          this.navigateToMyPage();
        },
        fail: (err) => {
          console.error('更新手机号失败:', err);
          wx.showToast({
            title: '网络错误，手机号获取失败',
            icon: 'none',
            duration: 2000
          });
          // 隐藏授权弹窗并跳转
          this.setData({ showPhoneAuth: false });
          this.navigateToMyPage();
        }
      });
    } else {
      // 获取失败或用户拒绝
      console.log('用户拒绝授权手机号');
      wx.showToast({
        title: '已跳过手机号授权',
        icon: 'none',
        duration: 1500
      });
      // 隐藏授权弹窗并跳转
      this.setData({ showPhoneAuth: false });
      this.navigateToMyPage();
    }
  },

  // 跳转到个人中心
  navigateToMyPage() {
    setTimeout(() => {
      wx.switchTab({
        url: '/pages/my/my',
        success: () => {
          console.log('跳转到个人中心成功');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.navigateTo({
            url: '/pages/my/my'
          });
        }
      });
    }, 1500);
  },

  onBack() {
    wx.navigateBack();
  }
})