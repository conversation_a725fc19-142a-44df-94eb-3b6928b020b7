.container {
  min-height: 100vh;
  background-color: #fff;
}

.privacy-content {
  padding: 40rpx;
}

.section {
  margin-bottom: 40rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  display: block;
  text-align: center;
  margin-bottom: 20rpx;
}

.update-time {
  font-size: 28rpx;
  color: #999;
  display: block;
  text-align: center;
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.content {
  font-size: 32rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  white-space: pre-line;
}