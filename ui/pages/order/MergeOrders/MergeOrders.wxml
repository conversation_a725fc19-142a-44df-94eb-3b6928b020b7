<!--pages/order/MergeOrders/MergeOrders.wxml-->
<view class="container">
  <navigation-bar title="合并支付订单" back="{{true}}" color="black" background="#FFF"></navigation-bar>
  <view class="tabs">
    <view 
      wx:for="{{tabs}}" 
      wx:key="*this" 
      class="tab-item {{activeIndex === index ? 'active' : ''}}" 
      data-index="{{index}}" 
      bindtap="handleTabClick"
    >
      {{item}}
    </view>
  </view>

  <view class="content">
    <block wx:if="{{orders.length > 0}}">
      <!-- 订单列表 -->
      <view wx:for="{{orders}}" wx:key="id" class="order-item">
        <!-- 订单内容 -->
      </view>
    </block>
    <block wx:else>
      <view class="no-data">没有数据</view>
    </block>
  </view>
</view>