// pages/order/GroupOrder/GroupOrder.js
const { getImageUrl } = require('../../../config/setting');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabs: ['全部', '待付款', '待发货', '待收货', '已签收'],
    activeIndex: 0, // 当前选中的tab
    orders: [] // 订单数据
  },
  
  // 切换tab
  handleTabClick(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeIndex: index
    });
    // 这里可以根据选中的tab去加载对应的订单数据
    this.loadOrders(index);
  },

  // 根据tab加载订单数据（示例）
  loadOrders(tabIndex) {
    console.log(`加载 ${this.data.tabs[tabIndex]} 订单数据`);
    // 在这里实现网络请求等逻辑
    // 示例：清空数据，显示"没有数据"
    this.setData({
      orders: []
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let activeIndex = 0; // 默认为"全部"
    if (options.id) {
      // 从页面参数中获取activeIndex
      activeIndex = parseInt(options.id, 10);
    }

    this.setData({
      activeIndex: activeIndex
    });
    // 页面加载时，加载对应tab的数据
    this.loadOrders(activeIndex);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})