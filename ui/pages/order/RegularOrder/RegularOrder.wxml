<!--pages/order/RegularOrder/RegularOrder.wxml-->
<view class="container">
  <navigation-bar title="普通订单" back="{{true}}" color="black" background="#FFF"></navigation-bar>
  <view class="tabs">
    <view 
      wx:for="{{tabs}}" 
      wx:key="*this" 
      class="tab-item {{activeIndex === index ? 'active' : ''}}" 
      data-index="{{index}}" 
      bindtap="handleTabClick"
    >
      {{item}}
    </view>
  </view>

  <view class="content">
    <block wx:if="{{loading}}">
      <view class="loading">加载中...</view>
    </block>
    <block wx:elif="{{orders.length > 0}}">
      <!-- 订单列表 -->
      <view wx:for="{{orders}}" wx:key="id" class="order-item">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-no">订单号：{{item.order_no}}</text>
            <text class="order-time">{{item.created_at}}</text>
          </view>
          <view class="order-status {{item.status_text}}">{{item.status_name}}</view>
        </view>

        <!-- 订单商品列表 -->
        <view class="order-products">
          <view wx:for="{{item.items}}" wx:for-item="product" wx:key="id" class="product-item">
            <image class="product-image" src="{{product.product_image_snapshot}}" mode="aspectFill" />
            <view class="product-info">
              <view class="product-name">{{product.product_name_snapshot}}</view>
              <view class="product-sku">SKU: {{product.sku_id}}</view>
              <view class="product-price-qty">
                <text class="product-price">￥{{product.price_at_purchase}}</text>
                <text class="product-qty">x{{product.quantity}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 订单金额 -->
        <view class="order-amount">
          <view class="amount-row">
            <text class="amount-label">商品总额：</text>
            <text class="amount-value">￥{{item.total_product_amount}}</text>
          </view>
          <view wx:if="{{item.discount_amount && item.discount_amount > 0}}" class="amount-row discount">
            <text class="amount-label">优惠券：</text>
            <text class="amount-value discount">-￥{{item.discount_amount}}</text>
          </view>
          <view class="amount-row total">
            <text class="amount-label">实付金额：</text>
            <text class="amount-value total">￥{{item.total_order_amount}}</text>
          </view>
        </view>

        <!-- 收货信息 -->
        <view class="shipping-info" wx:if="{{item.recipient_name}}">
          <text class="shipping-label">收货人：</text>
          <text class="shipping-value">{{item.recipient_name}} {{item.recipient_phone}}</text>
        </view>
        <view class="shipping-address" wx:if="{{item.shipping_address}}">
          <text class="address-label">收货地址：</text>
          <text class="address-value">{{item.shipping_address}}</text>
        </view>

        <!-- 订单操作按钮 -->
        <view class="order-actions">
          <button class="action-btn secondary" bindtap="viewOrderDetail" data-id="{{item.id}}">查看详情</button>
          <button wx:if="{{item.status_text === 'pending'}}" class="action-btn primary" bindtap="payOrder" data-id="{{item.id}}">去支付</button>
          <button wx:if="{{item.status_text === 'pending'}}" class="action-btn danger" bindtap="cancelOrder" data-id="{{item.id}}">取消订单</button>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="no-data">
        <image class="no-data-icon" src="/images/empty.png" mode="aspectFit" />
        <text class="no-data-text">暂无订单数据</text>
      </view>
    </block>
  </view>
</view>