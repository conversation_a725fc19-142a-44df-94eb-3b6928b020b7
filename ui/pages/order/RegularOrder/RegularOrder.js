// pages/order/RegularOrder/RegularOrder.js
const { getImageUrl } = require('../../../config/setting');
var app = getApp();
const config = require('../../../config/setting');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    tabs: ['全部', '待付款', '待发货', '待收货', '已签收', '已取消'],
    activeIndex: 0, // 当前选中的tab
    orders: [], // 订单数据
    loading: false
  },
  
  // 切换tab
  handleTabClick(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      activeIndex: index
    });
    // 这里可以根据选中的tab去加载对应的订单数据
    this.loadOrders(index);
  },

  // 根据tab加载订单数据
  loadOrders(tabIndex) {
    const userInfo = app.getUserInfo();
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    // 状态映射
    const statusMap = {
      0: 'all',       // 全部
      1: 'pending',   // 待付款
      2: 'paid',      // 待发货
      3: 'shipped',   // 待收货
      4: 'completed', // 已签收
      5: 'cancelled'  // 已取消
    };

    const status = statusMap[tabIndex] || 'all';

    this.setData({ loading: true });

    wx.request({
      url: `${config.rootUrl}/orders/list/${userInfo.id}`,
      method: 'GET',
      data: {
        status: status
      },
      success: (res) => {
        if (res.data && res.data.success) {
          // 处理订单中的商品图片URL
          const orders = res.data.data.map(order => ({
            ...order,
            items: order.items ? order.items.map(item => ({
              ...item,
              product_image_snapshot: getImageUrl(item.product_image_snapshot)
            })) : []
          }));

          this.setData({
            orders: orders,
            loading: false
          });
        } else {
          wx.showToast({
            title: '加载订单失败',
            icon: 'none'
          });
          this.setData({
            orders: [],
            loading: false
          });
        }
      },
      fail: (err) => {
        console.error('加载订单失败:', err);
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
        this.setData({
          orders: [],
          loading: false
        });
      }
    });
  },

  // 查看订单详情
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/orderDetail/orderDetail?id=${orderId}`
    });
  },

  // 取消订单
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    const userInfo = app.getUserInfo();

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？取消后无法恢复。',
      confirmText: '确认取消',
      cancelText: '我再想想',
      success: (res) => {
        if (res.confirm) {
          // 显示加载提示
          wx.showLoading({
            title: '取消中...',
            mask: true
          });

          // 调用取消订单的API
          wx.request({
            url: `${config.rootUrl}/orders/${orderId}/cancel`,
            method: 'PUT',
            data: {
              user_id: userInfo.id
            },
            success: (apiRes) => {
              wx.hideLoading();

              if (apiRes.data && apiRes.data.success) {
                wx.showToast({
                  title: '订单已取消',
                  icon: 'success'
                });
                // 重新加载当前tab的数据
                this.loadOrders(this.data.activeIndex);
              } else {
                wx.showToast({
                  title: apiRes.data.message || '取消订单失败',
                  icon: 'none'
                });
              }
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('取消订单失败:', err);
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 去支付
  payOrder(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.showToast({
      title: '跳转支付页面',
      icon: 'none'
    });
    // 这里可以跳转到支付页面
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    let activeIndex = 0; // 默认为"全部"
    if (options.id) {
      // 从页面参数中获取activeIndex
      activeIndex = parseInt(options.id, 10);
    }

    this.setData({
      activeIndex: activeIndex
    });
    // 页面加载时，加载对应tab的数据
    this.loadOrders(activeIndex);
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})