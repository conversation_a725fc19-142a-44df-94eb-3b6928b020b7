/* pages/order/RegularOrder/RegularOrder.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f7;
}

.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  color: #333;
  font-size: 28rpx;
  position: relative;
}

.tab-item.active {
  color: #e54d42;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #e54d42;
  border-radius: 3rpx;
}

.content {
  flex: 1;
  overflow-y: auto;
}

.loading {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 100rpx 0;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.no-data-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.no-data-text {
  color: #999;
  font-size: 28rpx;
}

/* 订单项样式 */
.order-item {
  background: white;
  margin: 16rpx;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-info {
  flex: 1;
}

.order-no {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: bold;
}

.order-status.pending {
  background: #fff3e0;
  color: #ff9800;
}

.order-status.paid {
  background: #e3f2fd;
  color: #2196f3;
}

.order-status.shipped {
  background: #f3e5f5;
  color: #9c27b0;
}

.order-status.completed {
  background: #e8f5e8;
  color: #4caf50;
}

/* 商品列表样式 */
.order-products {
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
  margin-right: 16rpx;
  background: #f5f5f5;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
  font-weight: 500;
}

.product-sku {
  font-size: 22rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 26rpx;
  color: #ff4d6b;
  font-weight: bold;
}

.product-qty {
  font-size: 24rpx;
  color: #666;
}

/* 订单金额样式 */
.order-amount {
  margin-bottom: 16rpx;
  padding: 12rpx 0;
  border-top: 1rpx solid #f0f0f0;
}

.amount-row {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 4rpx;
}

.amount-row:last-child {
  margin-bottom: 0;
}

.amount-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.amount-value {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.amount-value.discount {
  color: #00c851;
}

.amount-value.total {
  font-size: 32rpx;
  color: #ff4d6b;
}

/* 收货信息样式 */
.shipping-info, .shipping-address {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
  font-size: 24rpx;
}

.shipping-label, .address-label {
  color: #666;
  margin-right: 8rpx;
  flex-shrink: 0;
}

.shipping-value, .address-value {
  color: #333;
  flex: 1;
}

/* 操作按钮样式 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12rpx;
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #666;
}

.action-btn.secondary:active {
  background: #e0e0e0;
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b9d, #ff4d6b);
  color: white;
}

.action-btn.primary:active {
  transform: scale(0.95);
}

.action-btn.danger {
  background: #fff;
  color: #ff4757;
  border: 1rpx solid #ff4757;
}

.action-btn.danger:active {
  background: #fff5f5;
}