/* pages/order/BulkOrder/BulkOrder.wxss */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f7f7f7;
}

.tabs {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  color: #333;
  font-size: 28rpx;
  position: relative;
}

.tab-item.active {
  color: #e54d42;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #e54d42;
  border-radius: 3rpx;
}

.content {
  flex: 1;
  overflow-y: auto;
}

.no-data {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding-top: 200rpx;
}