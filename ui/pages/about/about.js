const config = require('../../config/setting');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    addressList: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadAddressList();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadAddressList();
  },

  // 加载地址列表
  loadAddressList() {
    const userInfo = wx.getStorageSync('userInfo');
    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: config.rootUrl + '/addresses',
      method: 'GET',
      data: {
        user_id: userInfo.id
      },
      success: (res) => {
        if (res.data.success) {
          this.setData({
            addressList: res.data.data
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取地址列表失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 添加地址
  addAddress() {
    wx.navigateTo({
      url: '/pages/addressEdit/addressEdit'
    });
  },

  // 编辑地址
  editAddress(e) {
    const id = e.currentTarget.dataset.id;
    // 检查是否有返回URL参数（从其他页面跳转过来的）
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const returnUrl = currentPage.options.returnUrl;

    let url = `/pages/addressEdit/addressEdit?id=${id}`;
    if (returnUrl) {
      url += `&returnUrl=${returnUrl}`;
    }

    wx.navigateTo({
      url: url
    });
  },

  // 设置默认地址
  setDefault(e) {
    const id = e.currentTarget.dataset.id;
    const userInfo = wx.getStorageSync('userInfo');

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: `${config.rootUrl}/addresses/${id}/default`,
      method: 'POST',
      data: {
        user_id: userInfo.id
      },
      success: (res) => {
        if (res.data.success) {
          wx.showToast({
            title: '设置成功',
            icon: 'success'
          });
          this.loadAddressList(); // 重新加载列表
        } else {
          wx.showToast({
            title: res.data.message || '设置失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 删除地址
  deleteAddress(e) {
    const id = e.currentTarget.dataset.id;
    const userInfo = wx.getStorageSync('userInfo');

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个地址吗？',
      success: (res) => {
        if (res.confirm) {
          wx.request({
            url: `${config.rootUrl}/addresses/${id}?user_id=${userInfo.id}`,
            method: 'DELETE',
            success: (res) => {
              if (res.data.success) {
                wx.showToast({
                  title: '删除成功',
                  icon: 'success'
                });
                this.loadAddressList(); // 重新加载列表
              } else {
                wx.showToast({
                  title: res.data.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: () => {
              wx.showToast({
                title: '网络错误',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  }
})