<view class="container">
  <navigation-bar title="地址管理" back="{{true}}" color="black" background="#FFF"></navigation-bar>

  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{addressList.length > 0}}">
    <block wx:for="{{addressList}}" wx:key="id">
      <view class="address-item" bindtap="editAddress" data-id="{{item.id}}">
        <view class="address-info">
          <view class="address-header">
            <text class="receiver-name">{{item.name}}</text>
            <text class="receiver-phone">{{item.phone}}</text>
            <view class="default-tag" wx:if="{{item.is_default}}">默认</view>
          </view>
          <view class="address-detail">
            {{item.province}} {{item.city}} {{item.district}} {{item.detail}}
          </view>
        </view>
        <view class="address-actions">
          <view class="action-btn" catchtap="setDefault" data-id="{{item.id}}" wx:if="{{!item.is_default}}">
            设为默认
          </view>
          <view class="action-btn delete-btn" catchtap="deleteAddress" data-id="{{item.id}}">
            删除
          </view>
        </view>
      </view>
    </block>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{addressList.length === 0}}">
    <view class="empty-icon">📍</view>
    <view class="empty-text">暂无收货地址</view>
    <view class="empty-desc">添加收货地址，让购物更便捷</view>
  </view>

  <!-- 添加地址按钮 -->
  <view class="add-address-btn" bindtap="addAddress">
    <text>+ 添加新地址</text>
  </view>

</view>