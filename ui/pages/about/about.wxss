/* pages/about/about.wxss */
.container {
  padding: 0;
  background: #f7f7f7;
  min-height: 100vh;
  padding-top: env(safe-area-inset-top);
}

.address-list {
  padding: 24rpx;
}

.address-item {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.address-info {
  flex: 1;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.receiver-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-right: 24rpx;
}

.receiver-phone {
  font-size: 28rpx;
  color: #666;
  margin-right: 24rpx;
}

.default-tag {
  background: #ff4d6b;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  margin-left: auto;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 24rpx;
}

.address-actions {
  display: flex;
  gap: 24rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  text-align: center;
  border: 1rpx solid #ddd;
  color: #666;
  background: #fff;
}

.action-btn:active {
  background: #f5f5f5;
}

.delete-btn {
  color: #ff4d6b;
  border-color: #ff4d6b;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 24rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
}

.add-address-btn {
  position: fixed;
  bottom: 60rpx;
  left: 24rpx;
  right: 24rpx;
  background: #ff4d6b;
  color: #fff;
  text-align: center;
  padding: 32rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(255, 77, 107, 0.3);
}

.add-address-btn:active {
  background: #e6455f;
}