// app.js
App({
  globalData: {
    navigateToProductZone: null,
    userInfo: null,
    isLogin: false,
    token: ''
  },

  // 初始化用户信息
  initUserInfo(id, name, score, avatar, token, mobile) {
    this.globalData.userInfo = {
      id: id,
      name: name,
      score: score,
      avatar: avatar,
      mobile: mobile
    };
    this.globalData.token = token;
    this.globalData.isLogin = true;

    // 将用户信息保存到本地存储
    wx.setStorageSync('userInfo', this.globalData.userInfo);
    wx.setStorageSync('token', token);
    wx.setStorageSync('isLogin', true);
  },

  // 获取用户信息
  getUserInfo() {
    if (!this.globalData.userInfo) {
      // 从本地存储获取
      const userInfo = wx.getStorageSync('userInfo');
      const token = wx.getStorageSync('token');
      const isLogin = wx.getStorageSync('isLogin');
      
      if (userInfo && token && isLogin) {
        this.globalData.userInfo = userInfo;
        this.globalData.token = token;
        this.globalData.isLogin = isLogin;
      }
    }
    return this.globalData.userInfo;
  },

  // 清除用户信息（登出）
  clearUserInfo() {
    this.globalData.userInfo = null;
    this.globalData.token = '';
    this.globalData.isLogin = false;
    
    // 清除本地存储
    wx.removeStorageSync('userInfo');
    wx.removeStorageSync('token');
    wx.removeStorageSync('isLogin');
  },

  // 检查是否已登录
  checkLogin() {
    return this.globalData.isLogin || wx.getStorageSync('isLogin');
  }
})
