/**
 * we-cropper v1.4.0
 * (c) 2021 dlhandsome
 * @license MIT
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.We<PERSON>ropper=e()}(this,function(){"use strict";var t=void 0,e=["touchstarted","touchmoved","touchended"],o={strokeStyle:"setStrokeStyle",fillStyle:"setFillStyle",lineWidth:"setLineWidth"};function r(n){for(var o=[],t=arguments.length-1;0<t--;)o[t]=arguments[t+1];e.forEach(function(t,e){void 0!==o[e]&&(n[t]=o[e])})}function n(){return t=t||wx.getSystemInfoSync()}function h(t,e,n){"2d"===t.type?t.ctx[e]=n:t.ctx[o[e]](n)}var a={},i={id:{default:"cropper",get:function(){return a.id},set:function(t){"string"!=typeof t&&console.error("id\uff1a"+t+" is invalid"),a.id=t}},width:{default:750,get:function(){return a.width},set:function(t){"number"!=typeof t&&console.error("width\uff1a"+t+" is invalid"),a.width=t}},height:{default:750,get:function(){return a.height},set:function(t){"number"!=typeof t&&console.error("height\uff1a"+t+" is invalid"),a.height=t}},pixelRatio:{default:n().pixelRatio,get:function(){return a.pixelRatio},set:function(t){"number"!=typeof t&&console.error("pixelRatio\uff1a"+t+" is invalid"),a.pixelRatio=t}},scale:{default:2.5,get:function(){return a.scale},set:function(t){"number"!=typeof t&&console.error("scale\uff1a"+t+" is invalid"),a.scale=t}},zoom:{default:5,get:function(){return a.zoom},set:function(t){"number"!=typeof t?console.error("zoom\uff1a"+t+" is invalid"):(t<0||10<t)&&console.error("zoom should be ranged in 0 ~ 10"),a.zoom=t}},src:{default:"",get:function(){return a.src},set:function(t){"string"!=typeof t&&console.error("src\uff1a"+t+" is invalid"),a.src=t}},cut:{default:{},get:function(){return a.cut},set:function(t){"object"!=typeof t&&console.error("cut\uff1a"+t+" is invalid"),a.cut=t}},boundStyle:{default:{},get:function(){return a.boundStyle},set:function(t){"object"!=typeof t&&console.error("boundStyle\uff1a"+t+" is invalid"),a.boundStyle=t}},onReady:{default:null,get:function(){return a.ready},set:function(t){a.ready=t}},onBeforeImageLoad:{default:null,get:function(){return a.beforeImageLoad},set:function(t){a.beforeImageLoad=t}},onImageLoad:{default:null,get:function(){return a.imageLoad},set:function(t){a.imageLoad=t}},onBeforeDraw:{default:null,get:function(){return a.beforeDraw},set:function(t){a.beforeDraw=t}}},c=n().windowWidth;function l(t){return"function"==typeof t}var u=["ready","beforeImageLoad","beforeDraw","imageLoad"];function s(r){return function(t){for(var o=[],e=arguments.length-1;0<e--;)o[e]=arguments[e+1];return void 0===t&&(t={}),new Promise(function(e,n){t.success=function(t){e(t)},t.fail=function(t){n(t)},r.apply(void 0,[t].concat(o))})}}function g(e,n){return void 0===n&&(n=!1),new Promise(function(t){e.draw&&e.draw(n,t)})}var p=s(wx.getImageInfo),v=s(wx.canvasToTempFilePath),f="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var d,y=(function(u,h){!function(t){var e=h,n=u&&u.exports==e&&u,o="object"==typeof f&&f;o.global!==o&&o.window!==o||(t=o);function r(t){this.message=t}(r.prototype=new Error).name="InvalidCharacterError";function s(t){throw new r(t)}var d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=/[\t\n\f\r ]/g,a={encode:function(t){t=String(t),/[^\0-\xFF]/.test(t)&&s("The string to be encoded contains characters outside of the Latin1 range.");for(var e,n,o,r,a=t.length%3,i="",c=-1,u=t.length-a;++c<u;)e=t.charCodeAt(c)<<16,n=t.charCodeAt(++c)<<8,o=t.charCodeAt(++c),i+=d.charAt((r=e+n+o)>>18&63)+d.charAt(r>>12&63)+d.charAt(r>>6&63)+d.charAt(63&r);return 2==a?(e=t.charCodeAt(c)<<8,n=t.charCodeAt(++c),i+=d.charAt((r=e+n)>>10)+d.charAt(r>>4&63)+d.charAt(r<<2&63)+"="):1==a&&(r=t.charCodeAt(c),i+=d.charAt(r>>2)+d.charAt(r<<4&63)+"=="),i},decode:function(t){var e=(t=String(t).replace(c,"")).length;(e=e%4==0?(t=t.replace(/==?$/,"")).length:e)%4!=1&&!/[^+a-zA-Z0-9/]/.test(t)||s("Invalid character: the string to be decoded is not correctly encoded.");for(var n,o,r=0,a="",i=-1;++i<e;)o=d.indexOf(t.charAt(i)),n=r%4?64*n+o:o,r++%4&&(a+=String.fromCharCode(255&n>>(-2*r&6)));return a},version:"0.1.0"};if(0,e&&!e.nodeType)if(n)n.exports=a;else for(var i in a)a.hasOwnProperty(i)&&(e[i]=a[i]);else t.base64=a}(f)}(d={exports:{}},d.exports),d.exports);function x(t){var e="";if("string"==typeof t)e=t;else for(var n=0;n<t.length;n++)e+=String.fromCharCode(t[n]);return y.encode(e)}function m(t,e,n,o,r,a,i){var c;void 0===i&&(i=function(){}),a="image/"+(a=void 0===a?"png":a).toLowerCase().replace(/jpg/i,"jpeg").match(/png|jpeg|bmp|gif/)[0],/bmp/.test(a)?(t=t,e=e,n=n,o=o,r=r,c=function(t,e){t=function(t){var e=t.width,n=t.height,o=[66,77,255&(o=54+(r=e*n*3)),o>>8&255,o>>16&255,o>>24&255,0,0,0,0,54,0,0,0],r=[40,0,0,0,255&e,e>>8&255,e>>16&255,e>>24&255,255&n,n>>8&255,n>>16&255,n>>24&255,1,0,24,0,0,0,0,0,255&r,r>>8&255,r>>16&255,r>>24&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],a=(4-3*e%4)%4,i=t.data,c="",u=e<<2,s=n,d=String.fromCharCode;do{for(var h=u*(s-1),f="",l=0;l<e;l++){var g=l<<2;f+=d(i[h+g+2])+d(i[h+g+1])+d(i[h+g])}for(var p=0;p<a;p++)f+=String.fromCharCode(0)}while(c+=f,--s);return x(o.concat(r))+x(c)}(t);l(i)&&i("data:image/"+a+";base64,"+t,e)},wx.canvasGetImageData({canvasId:t,x:e,y:n,width:o,height:r,success:function(t){c(t,null)},fail:function(t){c(null,t)}})):console.error("\u6682\u4e0d\u652f\u6301\u751f\u6210'"+a+"'\u7c7b\u578b\u7684base64\u56fe\u7247")}var w=function(t,e){return m((t=void 0===t?{}:t).canvasId,t.x,t.y,t.width,t.height,"bmp",e=void 0===e?function(){}:e)};var b={touchStart:function(t){var e=t.touches,n=e[0],e=e[1];this.src&&(r(this,!0,null,null),this.__oneTouchStart(n),2<=t.touches.length&&this.__twoTouchStart(n,e))},touchMove:function(t){var e=t.touches,n=e[0],e=e[1];this.src&&(r(this,null,!0),1===t.touches.length&&this.__oneTouchMove(n),2<=t.touches.length&&this.__twoTouchMove(n,e))},touchEnd:function(t){this.src&&(r(this,!1,!1,!0),this.__xtouchEnd())}};function C(t){var e,n,o=this,r={};return e=o,n=i,Object.defineProperties(e,n),Object.keys(i).forEach(function(t){r[t]=i[t].default}),Object.assign(o,r,t),o.prepare(),o.attachPage(),o.createCtx(),o.observer(),o.cutt(),o.methods(),o.init(),o.update(),o}return C.prototype.init=function(){var t=this,e=t.src;return t.version="1.4.0","function"==typeof t.onReady&&t.onReady(t.ctx,t),e?t.pushOrign(e):t.updateCanvas(),r(t,!1,!1,!1),t.oldScale=1,t.newScale=1,t},Object.assign(C.prototype,b),C.prototype.prepare=function(){var n=this;n.attachPage=function(){var t=getCurrentPages(),t=t[t.length-1];Object.defineProperty(t,"wecropper",{get:function(){return console.warn("Instance will not be automatically bound to the page after v1.4.0\n\nPlease use a custom instance name instead\n\nExample: \nthis.mycropper = new WeCropper(options)\n\n// ...\nthis.mycropper.getCropperImage()"),n},configurable:!0})},n.createCtx=function(){var t=n.id,e=n.targetId;t?(n.ctx=n.ctx||wx.createCanvasContext(t),n.targetCtx=n.targetCtx||wx.createCanvasContext(e),"function"!=typeof n.ctx.setStrokeStyle&&(n.type="2d")):console.error("constructor: create canvas context failed, 'id' must be valuable")},n.deviceRadio=c/750},C.prototype.observer=function(){var o=this;o.on=function(t,e){var n;return-1<u.indexOf(t)?l(e)&&("ready"===t?e(o):o["on"+((n=t).charAt(0).toUpperCase()+n.slice(1))]=e):console.error("event: "+t+" is invalid"),o}},C.prototype.methods=function(){var a=this,t=a.width,e=a.height,i=a.id,c=a.targetId,u=a.pixelRatio,n=a.cut,s=n.x;void 0===s&&(s=0);var d=n.y;void 0===d&&(d=0);var h=n.width;void 0===h&&(h=t);var f=n.height;void 0===f&&(f=e),a.updateCanvas=function(t){return a.croperTarget&&a.ctx.drawImage(a.croperTarget,a.imgLeft,a.imgTop,a.scaleWidth,a.scaleHeight),l(a.onBeforeDraw)&&a.onBeforeDraw(a.ctx,a),a.setBoundStyle(a.boundStyle),"2d"!==a.type&&a.ctx.draw(!1,t),t&&t(),a},a.pushOrigin=a.pushOrign=function(e){return a.src=e,l(a.onBeforeImageLoad)&&a.onBeforeImageLoad(a.ctx,a),o=a,r=e,new Promise(function(t,e){var n;"2d"===o.type?((n=o.canvas.createImage()).onload=function(){t(n)},n.onerror=function(t){e(t)},n.src=r):t(r)}).then(function(t){return a.croperTarget=t,p({src:e}).then(function(t){t=t.width/t.height;return t<h/f?(a.rectX=s,a.baseWidth=h,a.baseHeight=h/t,a.rectY=d-Math.abs((f-a.baseHeight)/2)):(a.rectY=d,a.baseWidth=f*t,a.baseHeight=f,a.rectX=s-Math.abs((h-a.baseWidth)/2)),a.imgLeft=a.rectX,a.imgTop=a.rectY,a.scaleWidth=a.baseWidth,a.scaleHeight=a.baseHeight,a.update(),new Promise(function(t){a.updateCanvas(t)})}).then(function(){l(a.onImageLoad)&&a.onImageLoad(a.ctx,a)})});var o,r},a.removeImage=function(){return a.src="",a.croperTarget="","2d"===a.type?a.ctx.clearRect(0,0,a.canvas.width,a.canvas.height):g(a.ctx)},a.getCropperBase64=function(t){w({canvasId:i,x:s,y:d,width:h,height:f},t=void 0===t?function(){}:t)},a.getCropperImage=function(t,e){var n=Object.assign({fileType:"jpg"},t),o=l(t)?t:l(e)?e:null,r={canvasId:i,x:s,y:d,width:h,height:f};"2d"===a.type&&(r.canvas=a.canvas);e=function(){return Promise.resolve()};return(e=n.original?function(){return a.targetCtx.drawImage(a.croperTarget,a.imgLeft*u,a.imgTop*u,a.scaleWidth*u,a.scaleHeight*u),r={canvasId:c,x:s*u,y:d*u,width:h*u,height:f*u},g(a.targetCtx)}:e)().then(function(){Object.assign(r,n);var t=r.componentContext?[r,r.componentContext]:[r];return v.apply(null,t)}).then(function(t){t=t.tempFilePath;return o?o.call(a,t,null):t}).catch(function(t){if(!o)throw t;o.call(a,null,t)})}},C.prototype.cutt=function(){var r=this,a=r.width,i=r.height,t=r.cut,c=t.x;void 0===c&&(c=0);var u=t.y;void 0===u&&(u=0);var s=t.width;void 0===s&&(s=a);var d=t.height;void 0===d&&(d=i),r.outsideBound=function(t,e){r.imgLeft=c<=t?c:r.scaleWidth+t-c<=s?c+s-r.scaleWidth:t,r.imgTop=u<=e?u:r.scaleHeight+e-u<=d?u+d-r.scaleHeight:e},r.setBoundStyle=function(t){var e=(t=void 0===t?{}:t).color;void 0===e&&(e="#04b00f");var n=t.mask;void 0===n&&(n="rgba(0, 0, 0, 0.3)");var o=t.lineWidth,t=(o=void 0===o?1:o)/2,t=[{start:{x:c-t,y:u+10-t},step1:{x:c-t,y:u-t},step2:{x:c+10-t,y:u-t}},{start:{x:c-t,y:u+d-10+t},step1:{x:c-t,y:u+d+t},step2:{x:c+10-t,y:u+d+t}},{start:{x:c+s-10+t,y:u-t},step1:{x:c+s+t,y:u-t},step2:{x:c+s+t,y:u+10-t}},{start:{x:c+s+t,y:u+d-10+t},step1:{x:c+s+t,y:u+d+t},step2:{x:c+s-10+t,y:u+d+t}}];r.ctx.beginPath(),h(r,"fillStyle",n),r.ctx.fillRect(0,0,c,i),r.ctx.fillRect(c,0,s,u),r.ctx.fillRect(c,u+d,s,i-u-d),r.ctx.fillRect(c+s,0,a-c-s,i),r.ctx.fill(),t.forEach(function(t){r.ctx.beginPath(),h(r,"strokeStyle",e),h(r,"lineWidth",o),r.ctx.moveTo(t.start.x,t.start.y),r.ctx.lineTo(t.step1.x,t.step1.y),r.ctx.lineTo(t.step2.x,t.step2.y),r.ctx.stroke()})}},C.prototype.update=function(){var c=this;c.src&&(c.__oneTouchStart=function(t){c.touchX0=Math.round(t.x),c.touchY0=Math.round(t.y)},c.__oneTouchMove=function(t){if(c.touchended)return c.updateCanvas();var e=Math.round(t.x-c.touchX0),t=Math.round(t.y-c.touchY0),e=Math.round(c.rectX+e),t=Math.round(c.rectY+t);c.outsideBound(e,t),c.updateCanvas()},c.__twoTouchStart=function(t,e){var n;c.touchX1=Math.round(c.rectX+c.scaleWidth/2),c.touchY1=Math.round(c.rectY+c.scaleHeight/2),n=Math.round(e.x-t.x),t=Math.round(e.y-t.y),t=Math.round(Math.sqrt(n*n+t*t)),c.oldDistance=t},c.__twoTouchMove=function(t,e){var n,o=c.oldScale,r=c.oldDistance,a=c.scale,i=c.zoom;c.newScale=(n=o,o=r,r=i,i=t,t=e,e=Math.round(t.x-i.x),i=Math.round(t.y-i.y),n+.001*r*(Math.round(Math.sqrt(e*e+i*i))-o)),c.newScale<=1&&(c.newScale=1),c.newScale>=a&&(c.newScale=a),c.scaleWidth=Math.round(c.newScale*c.baseWidth),c.scaleHeight=Math.round(c.newScale*c.baseHeight);o=Math.round(c.touchX1-c.scaleWidth/2),a=Math.round(c.touchY1-c.scaleHeight/2);c.outsideBound(o,a),c.updateCanvas()},c.__xtouchEnd=function(){c.oldScale=c.newScale,c.rectX=c.imgLeft,c.rectY=c.imgTop})},C});