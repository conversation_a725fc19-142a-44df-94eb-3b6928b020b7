/* components/startup-popup/startup-popup.wxss */

.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
}

.popup-content {
  position: relative;
  width: 600rpx;
  max-width: 90vw;
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: popupShow 0.3s ease-out;
}

@keyframes popupShow {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(100rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.close-icon {
  font-size: 36rpx;
  color: #666;
  font-weight: bold;
}

.ad-content {
  position: relative;
  width: 100%;
}

.ad-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20rpx;
}
