// components/startup-popup/startup-popup.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹窗
    visible: {
      type: Boolean,
      value: false
    },
    // 广告图片URL（从数据库获取）
    adImageUrl: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭弹窗
    closePopup() {
      this.triggerEvent('close');
    },

    // 防止弹窗内容滚动穿透
    preventTouchMove() {
      return false;
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      console.log('启动弹窗组件已加载');
    }
  },

  /**
   * 组件属性变化监听
   */
  observers: {
    'visible': function(visible) {
      console.log('弹窗显示状态变化:', visible);
    },
    'adImageUrl': function(imageUrl) {
      console.log('广告图片URL变化:', imageUrl);
    }
  }
});
