const mysql = require('mysql2');

// 数据库连接配置
const db = mysql.createConnection({
  host: '**************',
  user: 'miniapp',
  password: 'Miniapp@123456',
  database: 'miniapp'
});

// 连接数据库
db.connect((err) => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  console.log('数据库连接成功');
  checkUsers();
});

function checkUsers() {
  console.log('查询用户表结构...');

  // 先查看users表结构
  db.query('DESCRIBE users', (err, results) => {
    if (err) {
      console.error('查询users表结构失败:', err);
    } else {
      console.log('users表结构:');
      console.table(results);
    }

    // 查询用户表数据
    const sql = `SELECT * FROM users LIMIT 10`;

    db.query(sql, (err, results) => {
      if (err) {
        console.error('查询用户失败:', err);
      } else {
        console.log('\n用户列表:');
        console.table(results);
      }

      // 查询已领取的优惠券
      const couponSql = `
        SELECT uc.*, u.nickname
        FROM user_coupon uc
        LEFT JOIN users u ON uc.user_id = u.id
        ORDER BY uc.claimed_at DESC
        LIMIT 10
      `;

      db.query(couponSql, (err, results) => {
        if (err) {
          console.error('查询优惠券失败:', err);
        } else {
          console.log('\n已领取的优惠券:');
          console.table(results);
        }
        db.end();
      });
    });
  });
}
