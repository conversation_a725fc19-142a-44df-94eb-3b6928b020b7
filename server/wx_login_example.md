# 微信小程序登录接口文档

## 代码架构说明

为了更好的代码组织和维护，微信登录接口已从 `app.js` 拆分到独立的路由文件中：

- **主文件**: `app.js` - 应用主入口，负责基础配置和路由注册
- **认证路由**: `routes/auth.js` - 包含微信登录等认证相关接口
- **环境配置**: `.env` - 存储敏感配置信息（如微信 appid 和 secret）

## 接口地址
`POST /api/wx_login`

## 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | string | 是 | 微信小程序登录凭证 |

## 小程序端调用示例
```javascript
// 在小程序中调用
wx.login({
  success: function(res) {
    if (res.code) {
      // 发送请求到后端
      wx.request({
        url: 'http://localhost:8000/api/wx_login',
        method: 'POST',
        data: {
          code: res.code
        },
        success: function(response) {
          console.log('登录成功:', response.data);
          if (response.data.code === 200) {
            // 保存token和用户信息
            wx.setStorageSync('token', response.data.token);
            wx.setStorageSync('userInfo', {
              id: response.data.id,
              name: response.data.name,
              score: response.data.score,
              avatar: response.data.avatar
            });
          }
        },
        fail: function(error) {
          console.error('登录失败:', error);
        }
      });
    } else {
      console.log('获取用户登录态失败！' + res.errMsg);
    }
  }
});
```

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "msg": "登录成功",
  "token": "base64编码的登录态",
  "id": 1,
  "name": "微信用户1234",
  "score": 0,
  "avatar": null,
  "openid": "用户的openid",
  "session_key": "微信返回的session_key"
}
```

### 错误响应
```json
{
  "code": 400,
  "msg": "错误信息"
}
```

## 环境变量配置

### 1. 创建 .env 文件

在项目根目录创建 `.env` 文件，配置微信小程序信息：

```env
# 微信小程序配置
WX_APPID=your_actual_appid
WX_SECRET=your_actual_secret
```

### 2. 安全最佳实践

- ✅ **使用环境变量**：敏感信息存储在 `.env` 文件中
- ✅ **版本控制排除**：`.env` 文件已添加到 `.gitignore` 中
- ✅ **模板文件**：提供 `.env.example` 作为配置模板
- ❌ **避免硬编码**：不要在代码中直接写入 secret

### 3. 配置步骤

1. 复制 `.env.example` 为 `.env`
2. 在 `.env` 中填入真实的微信小程序配置
3. 重启服务器使配置生效

## 项目结构

```
shopping/
├── app.js                 # 主应用文件
├── routes/
│   └── auth.js           # 认证相关路由
├── .env                  # 环境变量配置（不提交到版本控制）
├── .env.example          # 环境变量模板
├── .gitignore           # Git忽略文件配置
├── package.json         # 项目依赖
└── wx_login_example.md  # 本文档
```

## 数据库变更

为支持微信登录，已在 `users` 表中添加了 `session_key` 字段：

```sql
ALTER TABLE users ADD COLUMN session_key VARCHAR(255) NULL AFTER unionid;
```

## 安全注意事项

1. **环境变量保护**：
   - 微信小程序的 `secret` 存储在环境变量中
   - `.env` 文件不应提交到版本控制系统
   - 生产环境使用专门的密钥管理服务

2. **传输安全**：
   - 生产环境中应使用 HTTPS 协议
   - 验证请求来源的合法性

3. **数据安全**：
   - Token 应设置合理的过期时间
   - Session Key 用于解密微信数据，应妥善保存
   - 定期轮换密钥

## 部署注意事项

1. **环境变量设置**：
   - 开发环境：使用 `.env` 文件
   - 生产环境：通过服务器环境变量或密钥管理服务设置

2. **依赖安装**：
   ```bash
   npm install dotenv
   ```

3. **服务重启**：
   - 修改环境变量后需要重启服务
   - 确保所有环境变量正确加载

## 测试方法

1. **配置检查**：
   - 确认 `.env` 文件配置正确
   - 检查控制台是否有配置警告

2. **功能测试**：
   - 在微信开发者工具中测试小程序登录
   - 使用 Postman 等工具模拟请求（需要有效的 code）
   - 检查数据库中用户记录的创建和更新

## 错误码说明

- `400`：参数错误或微信API调用失败
- `500`：服务器内部错误或数据库错误

## 常见问题

**Q: 为什么要拆分路由？**
A: 拆分路由可以提高代码的可维护性，便于团队协作，也更符合模块化开发的最佳实践。

**Q: 环境变量没有生效怎么办？**
A: 确保已安装 `dotenv` 包，`.env` 文件格式正确，并重启了服务器。

**Q: 如何在生产环境配置环境变量？**
A: 可以通过服务器的环境变量设置，或使用专门的密钥管理服务，避免在代码中硬编码敏感信息。