const mysql = require('mysql2');

const db = mysql.createConnection({
  host: '**************',
  user: 'miniapp',
  password: 'Miniapp@123456',
  database: 'miniapp',
  charset: 'utf8mb4'
});

console.log('检查数据库中的优惠券相关表...\n');

// 1. 查看所有表
db.query('SHOW TABLES', (err, tables) => {
  if (err) {
    console.error('查询表列表失败:', err);
    db.end();
    return;
  }

  console.log('数据库中的所有表:');
  const tableNames = tables.map(table => Object.values(table)[0]);
  console.table(tableNames);

  // 2. 查找包含 coupon 的表
  const couponTables = tableNames.filter(name => 
    name.toLowerCase().includes('coupon') || 
    name.toLowerCase().includes('discount') ||
    name.toLowerCase().includes('voucher')
  );

  console.log('\n优惠券相关的表:');
  if (couponTables.length > 0) {
    console.table(couponTables);
    
    // 3. 查看每个优惠券表的结构
    couponTables.forEach((tableName, index) => {
      db.query(`DESCRIBE ${tableName}`, (err, columns) => {
        if (err) {
          console.error(`查询表 ${tableName} 结构失败:`, err);
        } else {
          console.log(`\n表 "${tableName}" 的结构:`);
          console.table(columns);
          
          // 查看表中的数据
          db.query(`SELECT * FROM ${tableName} LIMIT 5`, (err, data) => {
            if (err) {
              console.error(`查询表 ${tableName} 数据失败:`, err);
            } else {
              console.log(`\n表 "${tableName}" 的示例数据:`);
              console.table(data);
            }
            
            // 如果是最后一个表，关闭连接
            if (index === couponTables.length - 1) {
              db.end();
            }
          });
        }
      });
    });
  } else {
    console.log('没有找到优惠券相关的表');
    
    // 4. 如果没有优惠券表，查看是否有其他可能相关的表
    const possibleTables = tableNames.filter(name => 
      name.toLowerCase().includes('promo') ||
      name.toLowerCase().includes('offer') ||
      name.toLowerCase().includes('deal') ||
      name.toLowerCase().includes('sale')
    );
    
    if (possibleTables.length > 0) {
      console.log('\n可能相关的促销表:');
      console.table(possibleTables);
    }
    
    db.end();
  }
});
