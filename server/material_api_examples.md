# 素材管理API使用示例

## 1. 获取边框分类列表
```
GET http://localhost:8000/api/border-categories

响应示例:
{
  "success": true,
  "data": [
    {
      "category_id": 7,
      "name": "经典边框",
      "sort_order": 1,
      "type": "border"
    },
    {
      "category_id": 8,
      "name": "花纹边框",
      "sort_order": 2,
      "type": "border"
    }
  ]
}
```

## 2. 获取指定分类的边框素材
```
GET http://localhost:8000/api/borders/7

响应示例:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "金色边框",
      "image_url": "/images/borders/gold_border.png",
      "thumbnail_url": "/images/borders/thumbs/gold_border.png",
      "sort_order": 1,
      "status": 1
    }
  ]
}
```

## 3. 获取卡背分类列表
```
GET http://localhost:8000/api/card-back-categories

响应示例:
{
  "success": true,
  "data": [
    {
      "category_id": 6,
      "name": "动漫卡背",
      "sort_order": 1,
      "type": "card_back"
    }
  ]
}
```

## 4. 获取指定分类的卡背素材
```
GET http://localhost:8000/api/card-backs/6

响应示例:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "柯南卡背",
      "image_url": "/images/card_backs/conan_back.jpg",
      "thumbnail_url": "/images/card_backs/thumbs/conan_back.jpg",
      "sort_order": 1,
      "status": 1
    }
  ]
}
```

## 5. 获取所有边框素材（包含分类信息）
```
GET http://localhost:8000/api/borders

响应示例:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "金色边框",
      "image_url": "/images/borders/gold_border.png",
      "thumbnail_url": "/images/borders/thumbs/gold_border.png",
      "sort_order": 1,
      "status": 1,
      "category_name": "经典边框",
      "category_id": 7
    }
  ]
}
```

## 6. 获取所有卡背素材（包含分类信息）
```
GET http://localhost:8000/api/card-backs

响应示例:
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "柯南卡背",
      "image_url": "/images/card_backs/conan_back.jpg",
      "thumbnail_url": "/images/card_backs/thumbs/conan_back.jpg",
      "sort_order": 1,
      "status": 1,
      "category_name": "动漫卡背",
      "category_id": 6
    }
  ]
}
```

## 数据库表结构说明

### material_categories (素材分类表)
- category_id: 分类ID
- name: 分类名称
- sort_order: 排序
- type: 类型 ('border' | 'card_back')

### materials (素材表)
- id: 素材ID
- name: 素材名称
- image_url: 图片URL
- thumbnail_url: 缩略图URL
- sort_order: 排序
- status: 状态 (1=启用, 0=禁用)

### material_group (素材分组关联表)
- category_id: 分类ID
- material_id: 素材ID
- sort_order: 在分组中的排序
