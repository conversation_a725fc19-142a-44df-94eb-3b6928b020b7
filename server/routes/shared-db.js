const mysql = require('mysql2');

// 微信相关功能的共享数据库连接配置
const wxDbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  // 微信功能专用连接池配置
  connectionLimit: 15,
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true,
  charset: 'utf8mb4'
};

// 创建微信功能专用的数据库连接池
const wxDb = mysql.createPool(wxDbConfig);

// 连接池事件监听
wxDb.on('connection', function (connection) {
  console.log('微信功能数据库连接已建立，连接ID:', connection.threadId);
});

wxDb.on('error', function(err) {
  console.error('微信功能数据库连接池错误:', err);
  if(err.code === 'PROTOCOL_CONNECTION_LOST') {
    console.log('数据库连接丢失，将自动重连');
  }
});

// 导出连接池
module.exports = wxDb;