const express = require('express');
const axios = require('axios');
const router = express.Router();
const wxDb = require('./shared-db'); // 引入微信功能共享数据库连接
const secretEncoded = Buffer.from(
  process.env.WX_SECRET_ENCODED, 
  'base64'
).toString('utf8');

// 微信小程序登录接口
router.post('/wx_login', async (req, res) => {
  try {
    const { code, userInfo } = req.body;
    
    // 从userInfo中提取用户信息
    const nickName = userInfo?.nickName;
    const avatarUrl = userInfo?.avatarUrl;
    const gender = userInfo?.gender;
    const country = userInfo?.country;
    const province = userInfo?.province;
    const city = userInfo?.city;
    
    // 验证参数
    if (!code) {
      return res.json({
        code: 400,
        msg: 'code参数不能为空'
      });
    }
    
    // 从环境变量获取微信小程序配置
    const appid = process.env.WX_APPID;
    const secret = secretEncoded;
    
    // 检查配置是否设置
    if (appid === 'your_appid_here' || secret === 'your_secret_here') {
      console.warn('警告：微信小程序配置未设置，请设置环境变量 WX_APPID 和 WX_SECRET');
    }
    
    // 调用微信API获取session_key和openid
    const wxApiUrl = `https://api.weixin.qq.com/sns/jscode2session?appid=${appid}&secret=${secret}&js_code=${code}&grant_type=authorization_code`;
    
    const wxResponse = await axios.get(wxApiUrl);
    const wxData = wxResponse.data;
    
    // 检查微信API返回结果
    if (wxData.errcode) {
      console.error('微信API调用失败:', wxData);
      return res.json({
        code: 400,
        msg: '微信登录失败: ' + (wxData.errmsg || '未知错误')
      });
    }
    
    const { openid, session_key, unionid } = wxData;
    
    if (!openid) {
      return res.json({
        code: 400,
        msg: '获取用户openid失败'
      });
    }
    
    // 使用微信功能共享数据库连接
    
    // 查询或创建用户
    wxDb.query('SELECT * FROM users WHERE openid = ?', [openid], (err, users) => {
      if (err) {
        console.error('查询用户错误:', err);
        return res.json({
          code: 500,
          msg: '数据库错误'
        });
      }
      
      let user;
      if (users.length === 0) {
        // 创建新用户
        const newUser = {
          openid: openid,
          unionid: unionid || null,
          session_key: session_key,
          name: `微信用户${openid.slice(-4)}`,
          score: 0,
          avatar: null,
          created_at: new Date(),
          updated_at: new Date(),
          mobile: 1
        };
        
        wxDb.query('INSERT INTO users SET ?', newUser, (err, result) => {
          if (err) {
            console.error('创建用户错误:', err);
            return res.json({
              code: 500,
              msg: '创建用户失败'
            });
          }
          
          user = {
            id: result.insertId,
            ...newUser
          };
          
          // 生成自定义登录态token
          const token = Buffer.from(`${user.id}-${openid}-${Date.now()}`).toString('base64');
          
          const response = {
            code: 200,
            msg: '登录成功',
            token: token,
            id: user.id,
            name: user.name,
            score: user.score,
            avatar: user.avatar,
            openid: openid,
            session_key: session_key
          };
          console.log('微信登录响应数据:', response);
          res.json(response);
        });
      } else {
        // 用户已存在，更新session_key
        user = users[0];
        
        wxDb.query('UPDATE users SET session_key = ?, updated_at = ? WHERE id = ?', 
          [session_key, new Date(), user.id], (err) => {
          if (err) {
            console.error('更新用户session_key错误:', err);
          }
        });
        
        // 生成自定义登录态token
        const token = Buffer.from(`${user.id}-${openid}-${Date.now()}`).toString('base64');
        
        const response = {
          code: 200,
          msg: '登录成功',
          token: token,
          id: user.id,
          name: user.name,
          score: user.score,
          avatar: user.avatar,
          openid: openid,
          session_key: session_key,
          mobile: user.mobile
        };
        console.log('微信登录响应数据:', response);
        res.json(response);
      }
    });
    
  } catch (error) {
    console.error('微信登录错误:', error);
    res.json({
      code: 500,
      msg: '服务器错误'
    });
  }
});

// 修改手机号接口
router.post('/update_phone', async (req, res) => {
  try {
    const { code, token } = req.body;
    
    // 验证参数
    if (!code) {
      return res.json({
        code: 400,
        msg: 'code参数不能为空'
      });
    }
    
    if (!token) {
      return res.json({
        code: 400,
        msg: 'token参数不能为空'
      });
    }
    
    // 解析token获取用户ID
    let userId;
    try {
      const tokenData = Buffer.from(token, 'base64').toString();
      userId = tokenData.split('-')[0];
    } catch (error) {
      return res.json({
        code: 401,
        msg: 'token格式错误'
      });
    }
    
    // 使用微信功能共享数据库连接
    
    // 验证用户是否存在并获取session_key
    wxDb.query('SELECT * FROM users WHERE id = ?', [userId], async (err, users) => {
      if (err) {
        console.error('查询用户错误:', err);
        return res.json({
          code: 500,
          msg: '数据库错误'
        });
      }
      
      if (users.length === 0) {
        return res.json({
          code: 404,
          msg: '用户不存在'
        });
      }
      
      const user = users[0];
      const sessionKey = user.session_key;
      
      if (!sessionKey) {
        return res.json({
          code: 400,
          msg: '用户session_key无效，请重新登录'
        });
      }
      
      try {
        // 从环境变量获取微信小程序配置
        const appid = process.env.WX_APPID;
        const secret = secretEncoded;
        
        // 调用微信API获取手机号
        const wxApiUrl = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${await getAccessToken(appid, secret)}`;
        
        const wxResponse = await axios.post(wxApiUrl, {
          code: code
        });
        
        const wxData = wxResponse.data;
        
        // 检查微信API返回结果
        if (wxData.errcode !== 0) {
          console.error('微信获取手机号失败:', wxData);
          return res.json({
            code: 400,
            msg: '获取手机号失败: ' + (wxData.errmsg || '未知错误')
          });
        }
        
        const phoneNumber = wxData.phone_info.phoneNumber;
        
        if (!phoneNumber) {
          return res.json({
            code: 400,
            msg: '获取手机号失败'
          });
        }
        
        // 更新用户手机号
        wxDb.query('UPDATE users SET mobile = ?, updated_at = ? WHERE id = ?', 
          [phoneNumber, new Date(), userId], (err) => {
          if (err) {
            console.error('更新用户手机号错误:', err);
            return res.json({
              code: 500,
              msg: '更新手机号失败'
            });
          }
          
          res.json({
            code: 200,
            msg: '手机号更新成功',
            mobile: phoneNumber
          });
        });
        
      } catch (error) {
        console.error('获取手机号错误:', error);
        res.json({
          code: 500,
          msg: '获取手机号失败'
        });
      }
    });
    
  } catch (error) {
    console.error('修改手机号错误:', error);
    res.json({
      code: 500,
      msg: '服务器错误'
    });
  }
});

// 获取微信access_token的辅助函数
async function getAccessToken(appid, secret) {
  try {
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appid}&secret=${secret}`;
    const response = await axios.get(url);
    
    if (response.data.access_token) {
      return response.data.access_token;
    } else {
      throw new Error('获取access_token失败: ' + response.data.errmsg);
    }
  } catch (error) {
    console.error('获取access_token错误:', error);
    throw error;
  }
}

module.exports = router;