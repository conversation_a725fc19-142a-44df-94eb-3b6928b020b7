const express = require('express');
const router = express.Router();

// 获取产品模板属性
router.get('/template-attribute/:productId', (req, res) => {
  const productId = req.params.productId;
  const db = req.app.locals.db;

  if (!productId) {
    return res.json({ success: false, message: '缺少产品ID参数' });
  }

  // 使用一个SQL查询获取所有相关数据
  const query = `
    SELECT 
      tal.attribute_id,
      tal.sort_order as attribute_sort_order,
      a.name as attribute_name,
      a.description as attribute_description,
      ao.id as option_id,
      ao.value as option_value,
      ao.operation,
      ao.price_adjustment,
      ao.image_url,
      ao.sort_order as option_sort_order
    FROM product p
    INNER JOIN template_attribute_link tal ON p.group_id = tal.template_id
    INNER JOIN attributes a ON tal.attribute_id = a.id
    LEFT JOIN attribute_options ao ON tal.attribute_id = ao.attribute_id
    WHERE p.id = ?
    ORDER BY tal.sort_order ASC, ao.sort_order ASC
  `;

  db.query(query, [productId], (err, results) => {
    if (err) {
      console.error('查询模板属性失败:', err);
      return res.json({ success: false, message: '查询模板属性失败' });
    }

    if (results.length === 0) {
      return res.json({ success: true, data: [], message: '该产品没有配置模板属性' });
    }

    // 将查询结果按attribute_id分组
    const attributesMap = new Map();
    
    results.forEach(row => {
      const attributeId = row.attribute_id;
      
      if (!attributesMap.has(attributeId)) {
        attributesMap.set(attributeId, {
          attribute_id: attributeId,
          options: []
        });
      }
      
      // 如果还没有添加属性基本信息，先添加
      if (attributesMap.get(attributeId).name === undefined) {
        const attribute = attributesMap.get(attributeId);
        attribute.name = row.attribute_name;
        attribute.description = row.attribute_description;
      }
      
      // 如果有选项数据，添加到options数组中
      if (row.option_id) {
        attributesMap.get(attributeId).options.push({
          id: row.option_id,
          value: row.option_value,
          operation: row.operation,
          price_adjustment: row.price_adjustment,
          image_url: row.image_url,
          sort_order: row.option_sort_order,
          attribute_id: attributeId
        });
      }
    });

    // 转换为数组格式
    const attributesWithOptions = Array.from(attributesMap.values());
    
    console.log(`产品ID: ${productId}, 返回属性数量: ${attributesWithOptions.length}`);

    res.json({ 
      success: true, 
      data: attributesWithOptions
    });
  });
});

module.exports = router;