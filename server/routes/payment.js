const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const axios = require('axios');
const xml2js = require('xml2js');
const wxDb = require('./shared-db'); // 引入微信功能共享数据库连接

// 获取微信支付密钥
function getWxPaySecret() {
  const encoded = process.env.WX_PAY_SECRET_ENCODED || 'your_encoded_pay_secret_here';
  return Buffer.from(encoded, 'base64').toString('utf-8');
}

// 微信支付配置
const wxPayConfig = {
  appid: process.env.WX_APPID || 'your_appid_here',
  mch_id: process.env.WX_MCH_ID || 'your_mch_id_here',
  key: getWxPaySecret(),
  notify_url: process.env.WX_PAY_NOTIFY_URL || 'https://your-domain.com/api/payment/notify'
};

// 生成随机字符串
function generateNonceStr(length = 32) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成微信支付签名
function generateSign(params, key) {
  // 1. 参数排序
  const sortedKeys = Object.keys(params).sort();
  
  // 2. 拼接字符串
  let stringA = '';
  sortedKeys.forEach(key => {
    if (params[key] && key !== 'sign') {
      stringA += `${key}=${params[key]}&`;
    }
  });
  
  // 3. 添加key
  stringA += `key=${key}`;
  
  // 4. MD5加密并转大写
  return crypto.createHash('md5').update(stringA, 'utf8').digest('hex').toUpperCase();
}

// 对象转XML
function objToXml(obj) {
  let xml = '<xml>';
  Object.keys(obj).forEach(key => {
    xml += `<${key}><![CDATA[${obj[key]}]]></${key}>`;
  });
  xml += '</xml>';
  return xml;
}

// XML转对象
function xmlToObj(xml) {
  return new Promise((resolve, reject) => {
    xml2js.parseString(xml, { explicitArray: false }, (err, result) => {
      if (err) {
        reject(err);
      } else {
        resolve(result.xml);
      }
    });
  });
}

// 统一下单接口
router.post('/unifiedorder', async (req, res) => {
  const db = wxDb;
  
  try {
    const {
      user_id,                    // 用户ID（可选，游客为null）
      openid,                     // 用户openid
      order_items,                // 订单商品列表
      total_product_amount,       // 商品总金额（分）
      shipping_fee = 0,           // 运费（分）
      discount_amount = 0,        // 优惠金额（分）
      total_order_amount,         // 订单实际支付金额（分）
      recipient_name,             // 收货人姓名
      recipient_phone,            // 收货人电话
      shipping_address,           // 收货地址
      shipping_method = 1,        // 配送方式
      customer_note,              // 用户备注
      attach                      // 附加数据（可选）
    } = req.body;

    // 参数验证
    if (!openid || !total_order_amount || !order_items || !recipient_name || !recipient_phone || !shipping_address) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：openid, total_order_amount, order_items, recipient_name, recipient_phone, shipping_address'
      });
    }

    // 生成订单号
    const order_no = 'ORD' + Date.now() + Math.random().toString(36).substr(2, 4).toUpperCase();
    
    // 1. 先创建数据库订单（状态为待支付）
    const orderResult = await new Promise((resolve, reject) => {
      db.query(
        `INSERT INTO orders (
          order_no, user_id, total_product_amount, shipping_fee, discount_amount, 
          total_order_amount, order_status, payment_method, recipient_name, 
          recipient_phone, shipping_address, shipping_method, customer_note, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, 10, 1, ?, ?, ?, ?, ?, NOW())`,
        [
          order_no, user_id, total_product_amount, shipping_fee, discount_amount,
          total_order_amount, recipient_name, recipient_phone, shipping_address,
          shipping_method, customer_note
        ],
        (err, result) => {
          if (err) reject(err);
          else resolve(result);
        }
      );
    });

    // 2. 记录支付请求日志
    db.query(
      'INSERT INTO payment_logs (order_no, action, request_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
      [order_no, 'create_order', JSON.stringify(req.body), 'success']
    );

    // 3. 构建统一下单参数
    const unifiedOrderParams = {
      appid: wxPayConfig.appid,
      mch_id: wxPayConfig.mch_id,
      nonce_str: generateNonceStr(),
      body: `订单${order_no}`,
      out_trade_no: order_no,
      total_fee: total_order_amount,
      spbill_create_ip: req.ip || '127.0.0.1',
      notify_url: wxPayConfig.notify_url,
      trade_type: 'JSAPI',
      openid: openid
    };

    // 添加附加数据
    if (attach) {
      unifiedOrderParams.attach = attach;
    }

    // 生成签名
    unifiedOrderParams.sign = generateSign(unifiedOrderParams, wxPayConfig.key);

    // 转换为XML
    const xmlData = objToXml(unifiedOrderParams);

    // 记录统一下单请求日志
    db.query(
      'INSERT INTO payment_logs (order_no, action, request_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
      [order_no, 'unifiedorder_request', JSON.stringify(unifiedOrderParams), 'pending']
    );

    // 4. 调用微信统一下单API
    const response = await axios.post('https://api.mch.weixin.qq.com/pay/unifiedorder', xmlData, {
      headers: {
        'Content-Type': 'application/xml'
      }
    });

    // 解析返回结果
    const result = await xmlToObj(response.data);

    // 记录统一下单响应日志
    db.query(
      'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
      [order_no, 'unifiedorder_response', JSON.stringify(result), result.return_code === 'SUCCESS' ? 'success' : 'failed']
    );

    if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
      // 5. 更新订单的prepay_id
      db.query(
        'UPDATE orders SET payment_transaction_id = ? WHERE order_no = ?',
        [result.prepay_id, order_no]
      );

      // 6. 生成小程序支付参数
      const payParams = {
        appId: wxPayConfig.appid,
        timeStamp: Math.floor(Date.now() / 1000).toString(),
        nonceStr: generateNonceStr(),
        package: `prepay_id=${result.prepay_id}`,
        signType: 'MD5'
      };

      // 生成小程序支付签名
      payParams.paySign = generateSign(payParams, wxPayConfig.key);

      res.json({
        success: true,
        message: '统一下单成功',
        data: {
          ...payParams,
          order_no: order_no,
          order_id: orderResult.insertId
        }
      });
    } else {
      // 统一下单失败，更新订单状态为已关闭
      db.query(
        'UPDATE orders SET order_status = 60, cancelled_at = NOW() WHERE order_no = ?',
        [order_no]
      );
      
      res.status(400).json({
        success: false,
        message: result.err_code_des || result.return_msg || '统一下单失败',
        error_code: result.err_code,
        order_no: order_no
      });
    }
  } catch (error) {
    console.error('统一下单错误:', error);
    
    // 记录错误日志
    if (req.body.order_no) {
      db.query(
        'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
        [req.body.order_no, 'unifiedorder_error', JSON.stringify({ error: error.message }), 'error']
      );
    }
    
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 支付结果通知接口
router.post('/notify', async (req, res) => {
  const db = wxDb;
  
  try {
    // 获取原始XML数据
    let xmlData = '';
    req.on('data', chunk => {
      xmlData += chunk;
    });
    
    req.on('end', async () => {
      try {
        // 解析XML
        const result = await xmlToObj(xmlData);
        
        // 记录支付通知日志
        db.query(
          'INSERT INTO payment_logs (order_no, action, request_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
          [result.out_trade_no, 'payment_notify', JSON.stringify(result), 'received']
        );
        
        // 验证签名
        const sign = result.sign;
        delete result.sign;
        const calculatedSign = generateSign(result, wxPayConfig.key);
        
        if (sign !== calculatedSign) {
          console.error('支付通知签名验证失败');
          
          // 记录签名验证失败日志
          db.query(
            'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
            [result.out_trade_no, 'signature_verify', JSON.stringify({ error: '签名验证失败' }), 'failed']
          );
          
          return res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名验证失败]]></return_msg></xml>');
        }
        
        // 验证支付结果
        if (result.return_code === 'SUCCESS' && result.result_code === 'SUCCESS') {
          // 更新订单状态为已支付
          db.query(
            'UPDATE orders SET order_status = 20, payment_transaction_id = ?, paid_at = NOW() WHERE order_no = ? AND order_status = 10',
            [result.transaction_id, result.out_trade_no],
            (err, updateResult) => {
              if (err) {
                console.error('更新订单状态失败:', err);
                
                // 记录更新失败日志
                db.query(
                  'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
                  [result.out_trade_no, 'update_order_status', JSON.stringify({ error: err.message }), 'failed']
                );
                
                return res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[更新订单失败]]></return_msg></xml>');
              }
              
              if (updateResult.affectedRows > 0) {
                console.log(`订单 ${result.out_trade_no} 支付成功`);
                
                // 记录支付成功日志
                db.query(
                  'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
                  [result.out_trade_no, 'payment_success', JSON.stringify(result), 'success']
                );
                
                // 返回成功响应
                res.send('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>');
              } else {
                console.log(`订单 ${result.out_trade_no} 状态未更新，可能已处理过`);
                
                // 记录重复通知日志
                db.query(
                  'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
                  [result.out_trade_no, 'duplicate_notify', JSON.stringify(result), 'duplicate']
                );
                
                // 返回成功响应（避免微信重复通知）
                res.send('<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>');
              }
            }
          );
        } else {
          console.error('支付失败:', result.err_code_des);
          
          // 记录支付失败日志
          db.query(
            'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
            [result.out_trade_no, 'payment_failed', JSON.stringify(result), 'failed']
          );
          
          res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[支付失败]]></return_msg></xml>');
        }
      } catch (error) {
        console.error('处理支付通知错误:', error);
        
        // 记录处理错误日志
        db.query(
          'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
          ['unknown', 'notify_error', JSON.stringify({ error: error.message }), 'error']
        );
        
        res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[处理失败]]></return_msg></xml>');
      }
    });
  } catch (error) {
    console.error('支付通知接口错误:', error);
    res.send('<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[系统错误]]></return_msg></xml>');
  }
});

// 查询订单状态
router.get('/order/:order_no', async (req, res) => {
  const db = wxDb;
  
  try {
    const { order_no } = req.params;
    
    db.query(
      'SELECT * FROM orders WHERE order_no = ?',
      [order_no],
      (err, rows) => {
        if (err) {
          console.error('查询订单错误:', err);
          return res.status(500).json({
            success: false,
            message: '服务器内部错误'
          });
        }
        
        if (rows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '订单不存在'
          });
        }
        
        const order = rows[0];
        
        // 转换订单状态为可读文本
        const statusMap = {
          10: 'pending',     // 待支付
          20: 'processing', // 处理中
          30: 'shipped',    // 已发货
          40: 'completed',  // 已完成
          50: 'cancelled',  // 已取消
          60: 'closed'      // 已关闭
        };
        
        res.json({
          success: true,
          data: {
            ...order,
            order_status_text: statusMap[order.order_status] || 'unknown'
          }
        });
      }
    );
  } catch (error) {
    console.error('查询订单错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

// 申请退款
router.post('/refund', async (req, res) => {
  const db = wxDb;
  
  try {
    const {
      order_no,         // 订单号
      refund_no,        // 退款单号
      refund_amount,    // 退款金额（分）
      refund_reason     // 退款原因（可选）
    } = req.body;

    // 参数验证
    if (!order_no || !refund_no || !refund_amount) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数：order_no, refund_no, refund_amount'
      });
    }

    // 查询原订单信息
    db.query(
      'SELECT * FROM orders WHERE order_no = ? AND order_status IN (20, 30, 40)',
      [order_no],
      async (err, orderRows) => {
        if (err) {
          console.error('查询订单错误:', err);
          return res.status(500).json({
            success: false,
            message: '服务器内部错误'
          });
        }
        
        if (orderRows.length === 0) {
          return res.status(404).json({
            success: false,
            message: '订单不存在或状态不允许退款'
          });
        }
        
        const order = orderRows[0];
        
        // 检查退款金额是否超过订单金额
        if (refund_amount > order.total_order_amount) {
          return res.status(400).json({
            success: false,
            message: '退款金额不能超过订单金额'
          });
        }
        
        try {
          // 构建退款参数
          const refundParams = {
            appid: wxPayConfig.appid,
            mch_id: wxPayConfig.mch_id,
            nonce_str: generateNonceStr(),
            out_trade_no: order_no,
            out_refund_no: refund_no,
            total_fee: order.total_order_amount,
            refund_fee: refund_amount
          };
          
          if (refund_reason) {
            refundParams.refund_desc = refund_reason;
          }
          
          // 生成签名
          refundParams.sign = generateSign(refundParams, wxPayConfig.key);
          
          // 转换为XML
          const xmlData = objToXml(refundParams);
          
          // 记录退款请求日志
          db.query(
            'INSERT INTO payment_logs (order_no, action, request_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
            [order_no, 'refund_request', JSON.stringify(refundParams), 'pending']
          );
          
          // 注意：退款接口需要证书，这里仅为示例
          // 实际使用时需要配置证书文件
          console.log('退款请求参数:', refundParams);
          
          // 保存退款记录
          db.query(
            'INSERT INTO refunds (order_no, refund_no, refund_amount, refund_reason, refund_status, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
            [order_no, refund_no, refund_amount, refund_reason || '', 'pending'],
            (refundErr, refundResult) => {
              if (refundErr) {
                console.error('保存退款记录错误:', refundErr);
                return res.status(500).json({
                  success: false,
                  message: '保存退款记录失败'
                });
              }
              
              res.json({
                success: true,
                message: '退款申请已提交',
                data: {
                  refund_no: refund_no,
                  refund_id: refundResult.insertId,
                  status: 'pending',
                  note: '退款接口需要配置商户证书才能实际调用微信退款API'
                }
              });
            }
          );
        } catch (error) {
          console.error('处理退款请求错误:', error);
          
          // 记录错误日志
          db.query(
            'INSERT INTO payment_logs (order_no, action, response_data, status, created_at) VALUES (?, ?, ?, ?, NOW())',
            [order_no, 'refund_error', JSON.stringify({ error: error.message }), 'error']
          );
          
          res.status(500).json({
            success: false,
            message: '处理退款请求失败'
          });
        }
      }
    );
  } catch (error) {
    console.error('申请退款错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;