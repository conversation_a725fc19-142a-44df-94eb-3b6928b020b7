{"name": "shopping", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js", "dev": "cross-env NODE_ENV=dev node app.js", "prod": "cross-env NODE_ENV=prod node app.js", "test": "cross-env NODE_ENV=test node app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "mysql2": "^2.3.3", "xml2js": "^0.6.2"}, "devDependencies": {"cross-env": "^10.0.0"}}