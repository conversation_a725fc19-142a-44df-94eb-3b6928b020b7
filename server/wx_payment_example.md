# 微信支付接口使用说明

## 接口概览

微信支付功能已集成到 `/api/payment` 路由下，包含以下主要接口：

- `POST /api/payment/unifiedorder` - 统一下单
- `POST /api/payment/notify` - 支付结果通知（微信回调）
- `GET /api/payment/order/:out_trade_no` - 查询订单状态
- `POST /api/payment/refund` - 申请退款

## 1. 统一下单接口

### 请求地址
```
POST /api/payment/unifiedorder
```

### 请求参数
```javascript
{
  "openid": "用户的openid",           // 必填
  "total_fee": 100,                  // 必填，支付金额（分）
  "body": "商品描述",                 // 必填，商品描述
  "out_trade_no": "ORDER20231201001", // 必填，商户订单号（唯一）
  "attach": "附加数据"                // 可选，附加数据
}
```

### 响应示例
```javascript
// 成功响应
{
  "success": true,
  "message": "统一下单成功",
  "data": {
    "appId": "wxfb5acefd7300185b",
    "timeStamp": "1701234567",
    "nonceStr": "abc123def456",
    "package": "prepay_id=wx123456789",
    "signType": "MD5",
    "paySign": "A1B2C3D4E5F6",
    "out_trade_no": "ORDER20231201001"
  }
}

// 失败响应
{
  "success": false,
  "message": "缺少必要参数：openid, total_fee, body, out_trade_no",
  "error_code": "PARAM_ERROR"
}
```

### 小程序端调用示例
```javascript
// 1. 调用后端统一下单接口
wx.request({
  url: 'https://your-domain.com/api/payment/unifiedorder',
  method: 'POST',
  data: {
    openid: 'user_openid_here',
    total_fee: 100,  // 1元 = 100分
    body: '测试商品',
    out_trade_no: 'ORDER' + Date.now(),
    attach: JSON.stringify({ userId: 123, productId: 456 })
  },
  success: (res) => {
    if (res.data.success) {
      // 2. 调用微信支付
      const payData = res.data.data;
      wx.requestPayment({
        timeStamp: payData.timeStamp,
        nonceStr: payData.nonceStr,
        package: payData.package,
        signType: payData.signType,
        paySign: payData.paySign,
        success: (payRes) => {
          console.log('支付成功', payRes);
          // 支付成功后的处理逻辑
        },
        fail: (payErr) => {
          console.log('支付失败', payErr);
          // 支付失败后的处理逻辑
        }
      });
    } else {
      wx.showToast({
        title: res.data.message,
        icon: 'none'
      });
    }
  }
});
```

## 2. 查询订单状态接口

### 请求地址
```
GET /api/payment/order/:out_trade_no
```

### 请求示例
```javascript
wx.request({
  url: 'https://your-domain.com/api/payment/order/ORDER20231201001',
  method: 'GET',
  success: (res) => {
    console.log('订单状态:', res.data.data.status);
    // pending: 待支付
    // paid: 已支付
    // cancelled: 已取消
    // refunded: 已退款
  }
});
```

### 响应示例
```javascript
{
  "success": true,
  "data": {
    "id": 1,
    "out_trade_no": "ORDER20231201001",
    "openid": "user_openid_here",
    "total_fee": 100,
    "body": "测试商品",
    "status": "paid",
    "transaction_id": "4200001234567890",
    "created_at": "2023-12-01T10:00:00.000Z",
    "paid_at": "2023-12-01T10:05:00.000Z"
  }
}
```

## 3. 申请退款接口

### 请求地址
```
POST /api/payment/refund
```

### 请求参数
```javascript
{
  "out_trade_no": "ORDER20231201001",    // 必填，原商户订单号
  "out_refund_no": "REFUND20231201001", // 必填，商户退款单号（唯一）
  "refund_fee": 100,                    // 必填，退款金额（分）
  "refund_desc": "用户申请退款"           // 可选，退款原因
}
```

### 小程序端调用示例
```javascript
wx.request({
  url: 'https://your-domain.com/api/payment/refund',
  method: 'POST',
  data: {
    out_trade_no: 'ORDER20231201001',
    out_refund_no: 'REFUND' + Date.now(),
    refund_fee: 100,
    refund_desc: '用户申请退款'
  },
  success: (res) => {
    if (res.data.success) {
      wx.showToast({
        title: '退款申请已提交',
        icon: 'success'
      });
    }
  }
});
```

## 配置说明

### 环境变量配置

在对应的环境配置文件中添加以下配置：

```bash
# 微信支付配置
WX_MCH_ID=your_mch_id_here                    # 微信商户号
WX_PAY_SECRET_ENCODED=encoded_pay_secret      # Base64编码的支付密钥
WX_PAY_NOTIFY_URL=https://your-domain.com/api/payment/notify  # 支付结果通知地址
```

### 数据库表结构

执行 `payment_tables.sql` 文件创建必要的数据库表：

```sql
-- 主要表结构
orders     -- 订单表
refunds    -- 退款表
payment_logs -- 支付日志表（可选）
```

## 安全注意事项

1. **密钥安全**：支付密钥使用Base64编码存储，实际使用时解码
2. **签名验证**：所有微信回调都会进行签名验证
3. **订单号唯一性**：确保商户订单号的唯一性
4. **金额单位**：所有金额都以分为单位，避免浮点数精度问题
5. **HTTPS**：生产环境必须使用HTTPS协议

## 测试建议

1. **开发环境**：使用微信支付沙箱环境进行测试
2. **订单号生成**：建议使用时间戳+随机数确保唯一性
3. **异常处理**：完善支付失败、网络异常等情况的处理逻辑
4. **日志记录**：记录关键操作日志，便于问题排查

## 常见问题

### Q: 支付时提示"商户号不存在"
A: 检查 `WX_MCH_ID` 配置是否正确

### Q: 签名验证失败
A: 检查 `WX_PAY_SECRET_ENCODED` 是否正确编码和配置

### Q: 支付成功但订单状态未更新
A: 检查支付回调地址是否可访问，确保服务器能接收微信通知

### Q: 退款接口调用失败
A: 退款接口需要配置商户证书，请参考微信支付官方文档配置证书文件