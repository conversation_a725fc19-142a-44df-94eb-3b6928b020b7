# 项目启动说明

## 环境变量配置

项目支持多环境配置，根据 `NODE_ENV` 自动加载对应的环境文件：

- 开发环境：`.env.dev`
- 测试环境：`.env.test`
- 生产环境：`.env.prod`
- 默认环境：`.env`

### 1. 配置环境变量文件

### 2. 环境变量说明

| 变量名 | 说明 | 示例值 | 必填 |
|--------|------|--------|------|
| NODE_ENV | 运行环境 | development/production | 否 |
| WX_APPID | 微信小程序AppID | wxfb5acefd7300185b | 是 |
| WX_SECRET | 微信小程序Secret | your_secret_here | 是 |
| DB_HOST | 数据库地址 | localhost | 是 |
| DB_USER | 数据库用户名 | root | 是 |
| DB_PASSWORD | 数据库密码 | your_password | 是 |
| DB_NAME | 数据库名称 | shopping_db | 是 |

## 启动命令

### 开发环境启动

```bash
# 方式1：使用npm脚本（推荐）
npm run dev

# 方式2：使用npm start
npm start

# 方式3：直接使用node（不推荐，因为需要手动加载环境变量）
node app.js
```

### 生产环境启动

```bash
# 生产环境启动（设置NODE_ENV=production）
npm run prod
```

## 启动检查清单

启动前请确认：

- ✅ 已安装依赖：`npm install`
- ✅ 已配置对应环境的 `.env` 文件（如 `.env.development`）
- ✅ 数据库连接正常
- ✅ 微信小程序配置正确

## 启动成功标志

服务启动成功后，控制台会显示：

```
[dotenv@17.2.1] injecting env (3) from .env
后端服务已启动，端口 8000
数据库连接成功，连接ID: xxxxx
字符集设置成功
```

## 常见问题

### 1. 环境变量未生效

**问题**：修改了 `.env` 文件但配置没有生效

**解决**：
- 确保使用 `npm run dev` 或 `npm start` 启动
- 重启服务器
- 检查对应环境的 `.env` 文件格式是否正确
- 确认 `NODE_ENV` 设置正确

### 2. 端口被占用

**问题**：`Error: listen EADDRINUSE :::8000`

**解决**：
```bash
# Windows
netstat -ano | findstr :8000
taskkill /PID <进程ID> /F

# 或者修改端口
# 在 .env 中添加：PORT=8001
```

### 3. 数据库连接失败

**问题**：数据库连接错误

**解决**：
- 检查数据库服务是否启动
- 确认数据库连接配置正确
- 检查网络连接

## 部署建议

### 开发环境
- 使用 `npm run dev`
- 设置 `NODE_ENV=dev`
- 启用详细日志

### 测试环境
- 使用 `npm run test`
- 设置 `NODE_ENV=test`
- 启用详细日志

### 生产环境
- 使用 `npm run prod`
- 设置 `NODE_ENV=prod`
- 使用进程管理器（如 PM2）
- 配置反向代理（如 Nginx）
- 使用 HTTPS

### PM2 部署示例

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start app.js --name "shopping-backend" --env production

# 查看状态
pm2 status

# 查看日志
pm2 logs shopping-backend
```

## 环境变量最佳实践

1. **开发环境**：使用 `.env.development` 文件
2. **测试环境**：使用 `.env.test` 文件
3. **生产环境**：使用 `.env.production` 文件或服务器环境变量
4. **永远不要**：将包含敏感信息的 `.env` 文件提交到版本控制
5. **团队协作**：维护 `.env.example` 文件作为模板
6. **环境隔离**：不同环境使用不同的配置文件，避免配置混乱