const mysql = require('mysql2');

// 数据库连接配置
const db = mysql.createConnection({
  host: '**************',
  user: 'miniapp',
  password: 'Miniapp@123456',
  database: 'miniapp'
});

// 连接数据库
db.connect((err) => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  console.log('数据库连接成功');
  insertTestData();
});

function insertTestData() {
  console.log('开始插入测试数据...');

  // 1. 插入优惠券模板
  const templates = [
    {
      name: '新人券',
      type: 1, // 满减券
      value: 2.00,
      threshold_amount: 11.00,
      validity_type: 2, // 领取后N天有效
      valid_days_after_claim: 1000,
      scope_type: 1, // 全场通用
      status: 1
    },
    {
      name: '升级冲刺用券',
      type: 1,
      value: 5.00,
      threshold_amount: 0.00, // 无门槛
      validity_type: 2,
      valid_days_after_claim: 30,
      scope_type: 1,
      status: 1
    },
    {
      name: '满减优惠券',
      type: 1,
      value: 10.00,
      threshold_amount: 50.00,
      validity_type: 1, // 固定时间
      valid_from: '2024-01-01 00:00:00',
      valid_to: '2024-12-31 23:59:59',
      scope_type: 1,
      status: 1
    }
  ];

  let templateIds = [];
  let insertedCount = 0;

  templates.forEach((template, index) => {
    const sql = `
      INSERT INTO coupon_template (
        name, type, value, threshold_amount, validity_type,
        valid_from, valid_to, valid_days_after_claim, scope_type, scope_obj_id, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    const values = [
      template.name,
      template.type,
      template.value,
      template.threshold_amount,
      template.validity_type,
      template.valid_from || null,
      template.valid_to || null,
      template.valid_days_after_claim || null,
      template.scope_type,
      0, // scope_obj_id，全场通用设为0
      template.status
    ];

    db.query(sql, values, (err, result) => {
      if (err) {
        console.error(`插入模板 ${template.name} 失败:`, err);
        return;
      }

      console.log(`✓ 插入模板 ${template.name} 成功，ID: ${result.insertId}`);
      templateIds[index] = result.insertId;
      insertedCount++;

      // 当所有模板都插入完成后，插入活动
      if (insertedCount === templates.length) {
        insertCampaigns(templateIds);
      }
    });
  });
}

function insertCampaigns(templateIds) {
  console.log('开始插入优惠券活动...');

  const campaigns = [
    {
      campaign_name: '新人专享券',
      template_id: templateIds[0],
      channel_type: 1, // 小程序领取
      claim_start_time: '2024-01-01 00:00:00',
      claim_end_time: '2024-12-31 23:59:59',
      max_quantity: 1000,
      claimed_quantity: 15,
      status: 1
    },
    {
      campaign_name: '升级冲刺券',
      template_id: templateIds[1],
      channel_type: 1,
      claim_start_time: '2024-01-01 00:00:00',
      claim_end_time: '2024-12-31 23:59:59',
      max_quantity: 500,
      claimed_quantity: 48,
      status: 1
    },
    {
      campaign_name: '满减大礼包',
      template_id: templateIds[2],
      channel_type: 1,
      claim_start_time: '2024-01-01 00:00:00',
      claim_end_time: '2024-12-31 23:59:59',
      max_quantity: -1, // 无限制
      claimed_quantity: 0,
      status: 1
    }
  ];

  let insertedCampaigns = 0;

  campaigns.forEach((campaign) => {
    const sql = `
      INSERT INTO coupon_campaign (
        campaign_name, template_id, channel_type, claim_start_time, 
        claim_end_time, max_quantity, claimed_quantity, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    const values = [
      campaign.campaign_name,
      campaign.template_id,
      campaign.channel_type,
      campaign.claim_start_time,
      campaign.claim_end_time,
      campaign.max_quantity,
      campaign.claimed_quantity,
      campaign.status
    ];

    db.query(sql, values, (err, result) => {
      if (err) {
        console.error(`插入活动 ${campaign.campaign_name} 失败:`, err);
        return;
      }

      console.log(`✓ 插入活动 ${campaign.campaign_name} 成功，ID: ${result.insertId}`);
      insertedCampaigns++;

      // 当所有活动都插入完成后，关闭数据库连接
      if (insertedCampaigns === campaigns.length) {
        console.log('✅ 所有测试数据插入完成！');
        db.end();
      }
    });
  });
}
