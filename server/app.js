// 加载环境变量
const path = require('path');
const envFile = process.env.NODE_ENV ? `.env.${process.env.NODE_ENV}` : '.env';
const envPath = path.resolve(__dirname, envFile);
require('dotenv').config({ path: envPath });

const express = require('express');
const mysql = require('mysql2');
const cors = require('cors');
const bodyParser = require('body-parser');
const axios = require('axios');

// 引入路由模块
const authRoutes = require('./routes/auth');
const paymentRoutes = require('./routes/payment');
const attributesRoutes = require('./routes/attributes');

const app = express();
app.use(cors());
app.use(bodyParser.json());

// 提供静态文件服务
app.use('/images', express.static(path.join(__dirname, '../ui/images')));
app.use(express.static(__dirname)); // 提供当前目录的静态文件服务
app.use(bodyParser.urlencoded({ extended: true }));

// 数据库连接配置
const db = mysql.createConnection({
  host: process.env.DB_HOST,      // 数据库地址
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,    // 数据库密码
  database: process.env.DB_NAME,    // 数据库名
  charset: 'utf8mb4'     // 设置字符集支持中文
});

// 数据库连接和重连机制
function connectDatabase() {
  db.connect((err) => {
    if (err) {
      console.error('数据库连接失败:', err);
      console.log('5秒后尝试重连...');
      setTimeout(connectDatabase, 5000);
      return;
    }
    console.log('数据库连接成功，连接ID:', db.threadId);

    // 设置连接字符集
    db.query("SET NAMES utf8mb4", (err) => {
      if (err) {
        console.error('设置字符集失败:', err);
      } else {
        console.log('字符集设置成功');
      }
    });
  });

  // 处理连接错误
  db.on('error', (err) => {
    console.error('数据库连接错误:', err);
    if (err.code === 'PROTOCOL_CONNECTION_LOST') {
      console.log('数据库连接丢失，尝试重连...');
      connectDatabase();
    } else {
      throw err;
    }
  });
}

// 启动数据库连接
connectDatabase();

// 将数据库连接传递给路由
app.locals.db = db;

// 使用认证路由
app.use('/api', authRoutes);
app.use('/api', attributesRoutes);
// 使用支付路由
app.use('/api/payment', paymentRoutes);

// 存储验证码的临时对象（实际项目中建议使用 Redis）
const verificationCodes = new Map();

// 测试数据库连接接口
app.get('/api/test-db', (req, res) => {
  db.query('SHOW TABLES', (err, results) => {
    if (err) {
      console.error('查询数据库表失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }
    console.log('数据库中的表:', results);
    res.json({ success: true, tables: results });
  });
});

// 获取商品列表接口
app.get('/api/products', (req, res) => {
  db.query('SELECT * FROM product', (err, results) => {
    if (err) {
      console.error('查询product表失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }
    res.json(results);
  });
});

// 获取商品列表接口（带分类信息）
app.get('/api/products-with-categories', (req, res) => {
  const sql = `
    SELECT DISTINCT
      p.*,
      GROUP_CONCAT(pz.name) as category_names,
      GROUP_CONCAT(pz.zone_key) as category_keys,
      GROUP_CONCAT(pz.id) as category_ids
    FROM product p
    LEFT JOIN index_product ip ON p.id = ip.product_id
    LEFT JOIN product_zones pz ON ip.prod_cate_id = pz.id
    WHERE p.status = 1
    GROUP BY p.id
    ORDER BY p.id DESC
  `;

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询product和product_zones关联失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }
    res.json({ success: true, data: results });
  });
});



// 获取banner列表接口
app.get('/api/banners', (req, res) => {
  db.query('SELECT * FROM home_banner_notices WHERE status = 1 ORDER BY sort_order ASC', (err, results) => {
    if (err) {
      console.error('查询home_banner_notices表失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }
    res.json(results);
  });
});

// 获取index_products列表接口 - 返回完整商品信息
app.get('/api/index_products', (req, res) => {
  const sql = `
    SELECT DISTINCT
      p.id,
      p.title,
      p.img,
      p.price_sale,
      p.price_tag,
      p.size,
      p.limit,
      p.desc,
      GROUP_CONCAT(DISTINCT pz.zone_key) as zones
    FROM index_product ip
    LEFT JOIN product p ON ip.product_id = p.id
    LEFT JOIN product_zones pz ON ip.prod_cate_id = pz.id
    WHERE p.status = 1
    GROUP BY p.id
    ORDER BY p.id ASC
  `;

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询首页商品失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }

    // 处理价格转换和数据格式
    const processedResults = results.map(item => ({
      id: item.id,
      title: item.title,
      img: item.img,
      price: item.price_sale ? (item.price_sale / 100).toFixed(2) : '0.00',
      price_sale: item.price_sale,
      price_tag: item.price_tag,
      size: item.size,
      limit: item.limit,
      desc: item.desc,
      zone: item.zones ? item.zones.split(',')[0] : 'all' // 取第一个分类作为主分类
    }));
    res.json(processedResults);
  });
});

// 测试接口 - 确认服务器更新
app.get('/api/test-server-update', (req, res) => {
  res.json({ message: '服务器已更新', timestamp: new Date().toISOString() });
});

// 获取首页滚动通知接口
app.get('/api/home-notices', (req, res) => {
  db.query('SELECT * FROM home_banner_notices WHERE status = 1 and display_type = ? ORDER BY sort_order ASC, id ASC', [1], (err, results) => {
    if (err) {
      console.error('查询home_banner_notices表失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }
    res.json({ success: true, data: results });
  });
});

// 获取首页弹窗接口
app.get('/api/home-banner-notices/:display_type', (req, res) => {
  const display_type = req.params.display_type;
  console.log('display_type', display_type);

  db.query('SELECT * FROM home_banner_notices WHERE status = 1 AND display_type = ? ORDER BY sort_order ASC, id ASC', [display_type], (err, results) => {
    if (err) {
      console.error('查询home_banner_notices表失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }
    res.json({ success: true, data: results });
  });
});

// 获取系统推荐模块列表接口
app.get('/api/recommend-modules', (req, res) => {
  db.query('SELECT * FROM system_recommend_modules WHERE status = 1 ORDER BY sort_order ASC', (err, results) => {
    if (err) {
      console.error('查询system_recommend_modules表失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }
    res.json({ success: true, data: results });
  });
});

// 获取系统推荐模块及其商品接口
app.get('/api/recommend-modules-with-products', (req, res) => {
  const sql = `
    SELECT
      srm.*,
      GROUP_CONCAT(
        JSON_OBJECT(
          'product_id', p.id,
          'title', p.title,
          'img', p.img,
          'price_sale', p.price_sale,
          'price_tag', p.price_tag,
          'desc', p.desc,
          'sort_order', srp.sort_order
        ) ORDER BY srp.sort_order ASC
      ) as products_json
    FROM system_recommend_modules srm
    LEFT JOIN system_recommend_products srp ON srm.module_id = srp.module_id
    LEFT JOIN product p ON srp.product_id = p.id AND p.status = 1
    WHERE srm.status = 1
    GROUP BY srm.module_id
    ORDER BY srm.sort_order ASC
  `;

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询推荐模块和商品关联失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }

    // 处理结果，解析JSON字符串
    const processedResults = results.map(module => {
      let products = [];
      if (module.products_json) {
        try {
          // 解析GROUP_CONCAT的JSON字符串
          const productsStr = module.products_json;
          products = productsStr.split('},{').map((item, index, array) => {
            if (index === 0 && array.length > 1) item += '}';
            else if (index === array.length - 1 && array.length > 1) item = '{' + item;
            else if (array.length > 1) item = '{' + item + '}';
            return JSON.parse(item);
          });
        } catch (e) {
          console.error('解析商品JSON失败:', e);
          products = [];
        }
      }

      return {
        ...module,
        products: products,
        products_json: undefined // 移除原始JSON字符串
      };
    });

    res.json({ success: true, data: processedResults });
  });
});

// 获取产品分类列表接口
app.get('/api/product-zones', (req, res) => {
  // 直接在SQL中进行排序，确保按sort_order从小到大排序
  db.query('SELECT * FROM product_zones WHERE status = 1 ORDER BY sort_order ASC, id ASC', (err, results) => {
    if (err) {
      console.error('查询product_zones表失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }

    console.log('分类查询结果:', results.map(r => `${r.name}(${r.sort_order})`).join(', '));
    res.json(results);
  });
});

// 获取产品分类列表接口（带商品数量）
app.get('/api/product-zones-with-count', (req, res) => {
  const sql = `
    SELECT
      pz.*,
      COUNT(DISTINCT ip.product_id) as product_count
    FROM product_zones pz
    LEFT JOIN index_product ip ON pz.id = ip.prod_cate_id
    LEFT JOIN product p ON ip.product_id = p.id AND p.status = 1
    WHERE pz.status = 1
    GROUP BY pz.id
    ORDER BY pz.sort_order ASC, pz.id ASC
  `;

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询product_zones和product关联失败:', err);
      res.status(500).json({ error: '数据库查询失败', details: err.message });
      return;
    }

    console.log('带数量分类查询结果:', results.map(r => `${r.name}(${r.sort_order})`).join(', '));
    res.json({ success: true, data: results });
  });
});

// 添加新的产品分类接口（用于测试）
app.post('/api/product-zones', (req, res) => {
  const { name, zone_key, sort_order = 999 } = req.body;

  if (!name || !zone_key) {
    return res.status(400).json({ error: '分类名称和标识符不能为空' });
  }

  // 获取最大ID
  db.query('SELECT MAX(id) as maxId FROM product_zones', (err, results) => {
    if (err) {
      return res.status(500).json({ error: '数据库查询失败' });
    }

    const newId = (results[0].maxId || 0) + 1;

    db.query(
      'INSERT INTO product_zones (id, name, zone_key, sort_order, status, created_at, updated_at) VALUES (?, ?, ?, ?, 1, NOW(), NOW())',
      [newId, name, zone_key, sort_order],
      (err, result) => {
        if (err) {
          return res.status(500).json({ error: '添加分类失败' });
        }
        res.json({ success: true, id: newId, message: '分类添加成功' });
      }
    );
  });
});

// 删除产品分类接口（用于测试）
app.delete('/api/product-zones/:id', (req, res) => {
  const id = req.params.id;

  // 不允许删除"全部"分类
  if (id == 0) {
    return res.status(400).json({ error: '不能删除"全部"分类' });
  }

  db.query('DELETE FROM product_zones WHERE id = ?', [id], (err, result) => {
    if (err) {
      return res.status(500).json({ error: '删除分类失败' });
    }
    res.json({ success: true, message: '分类删除成功' });
  });
});

// 删除单个产品接口 (使用POST方法)
app.post('/api/products/delete/:id', (req, res) => {
  const id = req.params.id;

  if (!id) {
    return res.status(400).json({ error: '产品ID不能为空' });
  }

  // 先查询产品是否存在
  db.query('SELECT * FROM product WHERE id = ?', [id], (err, results) => {
    if (err) {
      console.error('查询产品失败:', err);
      return res.status(500).json({ error: '查询产品失败', details: err.message });
    }

    if (results.length === 0) {
      return res.status(404).json({ error: '产品不存在' });
    }

    const product = results[0];

    // 删除产品
    db.query('DELETE FROM product WHERE id = ?', [id], (deleteErr, deleteResult) => {
      if (deleteErr) {
        console.error('删除产品失败:', deleteErr);
        return res.status(500).json({ error: '删除产品失败', details: deleteErr.message });
      }

      console.log(`产品删除成功: ID=${id}, 标题=${product.title}`);
      res.json({
        success: true,
        message: '产品删除成功',
        deletedProduct: {
          id: product.id,
          title: product.title
        }
      });
    });
  });
});

// 批量删除产品接口 (使用POST方法)
app.post('/api/products/batch-delete', (req, res) => {
  const { ids } = req.body;

  if (!ids || !Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ error: '产品ID列表不能为空' });
  }

  // 先查询要删除的产品
  const placeholders = ids.map(() => '?').join(',');
  const selectQuery = `SELECT id, title FROM product WHERE id IN (${placeholders})`;

  db.query(selectQuery, ids, (err, results) => {
    if (err) {
      console.error('查询产品失败:', err);
      return res.status(500).json({ error: '查询产品失败', details: err.message });
    }

    if (results.length === 0) {
      return res.status(404).json({ error: '没有找到要删除的产品' });
    }

    // 批量删除产品
    const deleteQuery = `DELETE FROM product WHERE id IN (${placeholders})`;
    db.query(deleteQuery, ids, (deleteErr, deleteResult) => {
      if (deleteErr) {
        console.error('批量删除产品失败:', deleteErr);
        return res.status(500).json({ error: '批量删除产品失败', details: deleteErr.message });
      }

      console.log(`批量删除产品成功: 删除了${deleteResult.affectedRows}个产品`);
      res.json({
        success: true,
        message: `成功删除${deleteResult.affectedRows}个产品`,
        deletedCount: deleteResult.affectedRows,
        deletedProducts: results
      });
    });
  });
});

// 发送验证码接口
app.post('/api/send_sms', (req, res) => {
  try {
    const { mobile } = req.body;
    
    // 验证手机号格式
    if (!mobile || !/^1[3-9]\d{9}$/.test(mobile)) {
      return res.json({
        code: 400,
        msg: '手机号格式不正确'
      });
    }
    
    // 生成4位随机验证码
    const code = Math.floor(1000 + Math.random() * 9000).toString();
    
    // 模拟发送短信（实际项目中需要集成真实的短信服务商）
    console.log(`向 ${mobile} 发送验证码: ${code}`);
    
    // 存储验证码（5分钟有效期）
    verificationCodes.set(mobile, {
      code: code,
      expireTime: Date.now() + 5 * 60 * 1000
    });
    
    res.json({
      code: 200,
      msg: '验证码已发送'
    });
    
  } catch (error) {
    console.error('发送验证码错误:', error);
    res.json({
      code: 500,
      msg: '服务器错误'
    });
  }
});

// 验证登录接口
app.post('/api/verify_code', (req, res) => {
  try {
    const { mobile, code } = req.body;
    
    // 验证参数
    if (!mobile || !code) {
      return res.json({
        code: 400,
        msg: '手机号和验证码不能为空'
      });
    }
    
    // 获取存储的验证码
    const storedData = verificationCodes.get(mobile);
    
    if (!storedData) {
      return res.json({
        code: 400,
        msg: '验证码不存在或已过期'
      });
    }
    
    // 检查验证码是否过期
    if (Date.now() > storedData.expireTime) {
      verificationCodes.delete(mobile);
      return res.json({
        code: 400,
        msg: '验证码已过期'
      });
    }
    
    // 验证验证码：检查传入的验证码是否等于存储的验证码
    if (storedData.code !== code) {
      return res.json({
        code: 400,
        msg: '验证码错误'
      });
    }
    
    // 验证成功，删除验证码
    verificationCodes.delete(mobile);
    
    // 查询或创建用户
    db.query('SELECT * FROM users WHERE mobile = ?', [mobile], (err, users) => {
      if (err) {
        console.error('查询用户错误:', err);
        return res.json({
          code: 500,
          msg: '数据库错误'
        });
      }
      
      let user;
      if (users.length === 0) {
        // 创建新用户
        const newUser = {
          mobile: mobile,
          name: `用户${mobile.slice(-4)}`,
          score: 0,
          avatar: null // 不设置默认头像，由前端处理
        };
        
        db.query('INSERT INTO users SET ?', newUser, (err, result) => {
          if (err) {
            console.error('创建用户错误:', err);
            return res.json({
              code: 500,
              msg: '创建用户失败'
            });
          }
          
          user = {
            id: result.insertId,
            ...newUser
          };
          
          // 生成简单的 token
          const token = Buffer.from(`${user.id}-${Date.now()}`).toString('base64');
          
          const response = {
            code: 200,
            msg: '登录成功',
            token: token,
            id: user.id,
            name: user.name,
            score: user.score,
            avatar: user.avatar
          };
          console.log('登录响应数据:', response);
          res.json(response);
        });
      } else {
        // 用户已存在
        user = users[0];
        
        // 生成简单的 token
        const token = Buffer.from(`${user.id}-${Date.now()}`).toString('base64');
        
        const response = {
          code: 200,
          msg: '登录成功',
          token: token,
          id: user.id,
          name: user.name,
          score: user.score,
          avatar: user.avatar
        };
        console.log('登录响应数据:', response);
        res.json(response);
      }
    });
    
  } catch (error) {
    console.error('验证登录错误:', error);
    res.json({
      code: 500,
      msg: '服务器错误'
    });
  }
});

// 微信登录接口已移至 routes/auth.js

// 加入购物车接口
app.post('/api/shoppingcar/add', (req, res) => {
  const {
    mobile, product_id, title, size, limit, process_name, process_price, img, items
  } = req.body;

  if (!mobile || !product_id || !items || !Array.isArray(items) || items.length === 0) {
    res.json({ success: false, message: '参数不完整' });
    return;
  }

  // 首先获取产品的售价信息
  db.query('SELECT price_sale, price_tag FROM product WHERE id = ?', [product_id], (err, productResults) => {
    if (err) {
      console.error('查询产品价格失败:', err);
      res.json({ success: false, message: '查询产品价格失败' });
      return;
    }

    if (productResults.length === 0) {
      res.json({ success: false, message: '产品不存在' });
      return;
    }

    const product = productResults[0];
    // 获取产品售价，优先使用price_sale，其次price_tag
    const price_sale = product.price_sale || product.price_tag || 0;

    // 1. 插入cart主表，包含产品售价
    const insertCartSql = `
      INSERT INTO cart (mobile, product_id, title, size, \`limit\`, process_name, process_price, price_sale, img, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;
    db.query(insertCartSql, [mobile, product_id, title, size, limit, process_name, process_price, price_sale, img], (err, result) => {
      if (err) {
        console.error('主表插入失败:', err);
        res.json({ success: false, message: '主表插入失败' });
        return;
      }
      const cartId = result.insertId;
      // 2. 插入cart_item明细表
      const insertItemSql = `
        INSERT INTO cart_item (cart_id, quantity, front_image, back_image, border, card_back)
        VALUES ?
      `;
      const values = items.map(item => [
        cartId,
        item.quantity,
        item.front_image,
        item.back_image,
        item.border,
        item.card_back
      ]);
      db.query(insertItemSql, [values], (err2) => {
        if (err2) {
          console.error('明细表插入失败:', err2);
          res.json({ success: false, message: '明细表插入失败' });
        } else {
          res.json({ success: true });
        }
      });
    });
  });
});

// 获取当前用户购物车商品
app.get('/api/shoppingcar/list', (req, res) => {
  const mobile = req.query.mobile;
  if (!mobile) {
    res.json({ success: false, message: '缺少手机号' });
    return;
  }
  const sql = `
    SELECT
      c.id as cart_id, c.product_id, c.title, c.process_name, c.process_price, c.price_sale,
      SUM(ci.quantity) as quantity,
      c.img
    FROM cart c
    LEFT JOIN cart_item ci ON ci.cart_id = c.id
    WHERE c.mobile = ?
    GROUP BY c.id
  `;
  db.query(sql, [mobile], (err, results) => {
    if (err) {
      console.error('购物车查询失败:', err);
      res.json({ success: false, message: '数据库查询失败' });
    } else {
      // 处理价格数据：转换为元并计算总价
      const processedResults = results.map(item => {
        const price_sale = Number(item.price_sale) || 0; // 产品售价（分）
        const process_price = Number(item.process_price) || 0; // 工艺价格（分）
        const quantity = Number(item.quantity) || 0;

        // 单价 = (产品售价 + 工艺价格) / 100，转换为元
        const unit_price = (price_sale + process_price) / 100;
        // 总价 = 单价 * 数量
        const amount = unit_price * quantity;

        return {
          ...item,
          price_sale: (price_sale / 100).toFixed(2), // 产品售价（元）
          process_price: (process_price / 100).toFixed(2), // 工艺价格（元）
          unit_price: unit_price.toFixed(2), // 单价（元）
          amount: amount.toFixed(2) // 总价（元）
        };
      });

      res.json({ success: true, data: processedResults });
    }
  });
});


// 更新购物车商品数量
app.post('/api/shoppingcar/update', (req, res) => {
  const { cart_id, quantity, mobile } = req.body;
  if (!cart_id || !quantity || !mobile) {
    res.json({ success: false, message: '参数缺失' });
    return;
  }

  // 验证购物车是否属于当前用户
  db.query('SELECT id FROM cart WHERE id = ? AND mobile = ?', [cart_id, mobile], (err, results) => {
    if (err) {
      res.json({ success: false, message: '验证失败' });
      return;
    }

    if (results.length === 0) {
      res.json({ success: false, message: '购物车项不存在或无权限' });
      return;
    }

    // 更新cart_item表中的数量
    db.query(
      'UPDATE cart_item SET quantity = ? WHERE cart_id = ?',
      [quantity, cart_id],
      (err, result) => {
        if (err) {
          res.json({ success: false, message: '数据库更新失败' });
        } else {
          res.json({ success: true });
        }
      }
    );
  });
});

// 更新购物车商品数量（新接口，兼容前端调用）
app.post('/api/shoppingcar/update', (req, res) => {
  const { mobile, cart_id, quantity } = req.body;
  if (!mobile || !cart_id || quantity === undefined) {
    res.json({ success: false, message: '参数缺失' });
    return;
  }

  console.log(`更新购物车数量: cart_id=${cart_id}, quantity=${quantity}, mobile=${mobile}`);

  // 先验证cart_id是否属于该用户
  db.query(
    'SELECT id FROM cart WHERE id = ? AND mobile = ?',
    [cart_id, mobile],
    (err, cartResults) => {
      if (err) {
        console.error('验证购物车归属失败:', err);
        res.json({ success: false, message: '验证失败' });
        return;
      }

      if (cartResults.length === 0) {
        res.json({ success: false, message: '购物车项不存在或无权限' });
        return;
      }

      // 更新cart_item表中的数量
      db.query(
        'UPDATE cart_item SET quantity = ? WHERE cart_id = ?',
        [quantity, cart_id],
        (err, result) => {
          if (err) {
            console.error('更新购物车数量失败:', err);
            res.json({ success: false, message: '数据库更新失败' });
          } else {
            console.log(`更新成功，影响行数: ${result.affectedRows}`);
            res.json({ success: true, message: '更新成功' });
          }
        }
      );
    }
  );
});


// 获取指定id的商品（如主播推荐用）
app.get('/api/anchor-products', (req, res) => {
  db.query('SELECT * FROM product WHERE id IN (?, ?)', [1001, 1002], (err, results) => {
    if (err) {
      res.json({ success: false, message: '数据库查询失败' });
    } else {
      res.json({ success: true, data: results });
    }
  });
});

// 获取新品/人气推荐商品
app.get('/api/recommend-products', (req, res) => {
  db.query('SELECT * FROM product WHERE id IN (?, ?, ?, ?) ORDER BY FIELD(id, ?, ?, ?, ?)',
    [1003, 1004, 1005, 1006, 1003, 1004, 1005, 1006],
    (err, results) => {
      if (err) {
        res.json({ success: false, message: '数据库查询失败' });
      } else {
        res.json({ success: true, data: results });
      }
    }
  );
});


// 删除购物车商品接口（单个删除）
app.post('/api/shoppingcar/delete', (req, res) => {
  const { id, mobile } = req.body;
  if (!id || !mobile) {
    res.json({ success: false, message: '参数缺失' });
    return;
  }

  console.log(`删除购物车商品: cart_id=${id}, mobile=${mobile}`);

  // 先删除cart_item表中的相关记录
  const deleteCartItemSql = 'DELETE FROM cart_item WHERE cart_id = ?';
  db.query(deleteCartItemSql, [id], (err1, result1) => {
    if (err1) {
      console.error('删除cart_item失败:', err1);
    }

    // 再删除cart表中的记录
    const deleteCartSql = 'DELETE FROM cart WHERE id = ? AND mobile = ?';
    db.query(deleteCartSql, [id, mobile], (err2, result2) => {
      if (err2) {
        console.error('删除购物车商品失败:', err2);
        res.json({ success: false, message: '删除失败' });
      } else {
        console.log(`删除成功: 影响行数=${result2.affectedRows}`);
        res.json({ success: true, message: '删除成功' });
      }
    });
  });
});

// 批量删除购物车商品接口
app.post('/api/shoppingcar/batch-delete', (req, res) => {
  const { ids, mobile } = req.body;
  if (!ids || !Array.isArray(ids) || ids.length === 0 || !mobile) {
    res.json({ success: false, message: '参数缺失或格式错误' });
    return;
  }

  console.log(`批量删除购物车商品: cart_ids=${ids.join(',')}, mobile=${mobile}`);

  // 先删除cart_item表中的相关记录
  const placeholders = ids.map(() => '?').join(',');
  const deleteCartItemSql = `DELETE FROM cart_item WHERE cart_id IN (${placeholders})`;

  db.query(deleteCartItemSql, ids, (err1, result1) => {
    if (err1) {
      console.error('批量删除cart_item失败:', err1);
    }

    // 再删除cart表中的记录
    const deleteCartSql = `DELETE FROM cart WHERE id IN (${placeholders}) AND mobile = ?`;
    const params = [...ids, mobile];

    db.query(deleteCartSql, params, (err2, result2) => {
      if (err2) {
        console.error('批量删除购物车商品失败:', err2);
        res.json({ success: false, message: '批量删除失败' });
      } else {
        console.log(`批量删除成功: 影响行数=${result2.affectedRows}`);
        res.json({
          success: true,
          message: `成功删除${result2.affectedRows}个商品`,
          deletedCount: result2.affectedRows
        });
      }
    });
  });
});

// 获取单个产品详情
app.get('/api/product/:id', (req, res) => {
  const id = req.params.id;

  db.query('SELECT * FROM product WHERE id = ?', [id], (err, results) => {
    if (err) {
      console.error('查询产品失败:', err);
      res.json({ success: false, message: '数据库查询失败' });
      return;
    }

    if (results.length === 0) {
      res.json({ success: false, message: '未找到产品' });
      return;
    }

    const product = results[0];

    // 根据产品的img_category从image_list表查询对应的图片
    if (product.img_category) {
      db.query('SELECT image_url FROM image_list WHERE category = ? ORDER BY id ASC', [product.img_category], (imgErr, imgResults) => {
        if (imgErr) {
          console.error('查询图片失败:', imgErr);
          // 如果查询图片失败，使用产品自身的img字段
          product.mainImage = product.img || null;
          product.imageList = product.img ? [product.img] : [];
        } else if (imgResults.length > 0) {
          // 从数据库查询到图片，使用查询结果
          const imageUrls = imgResults.map(row => row.image_url);
          product.mainImage = imageUrls[0]; // 第一张作为主图
          product.imageList = imageUrls; // 所有图片作为图片列表
        } else {
          // 没有查询到图片，使用产品自身的img字段
          product.mainImage = product.img || null;
          product.imageList = product.img ? [product.img] : [];
        }

        res.json({ success: true, data: product });
      });
    } else {
      // 如果没有img_category，使用产品自身的img字段
      product.mainImage = product.img || null;
      product.imageList = product.img ? [product.img] : [];

      res.json({ success: true, data: product });
    }
  });
});

// 获取指定产品的工艺列表
app.get('/api/process/:productId', (req, res) => {
  const productId = req.params.productId;

  // 第一步：根据产品ID获取group_id
  db.query('SELECT group_id FROM product WHERE id = ?', [productId], (err, productResults) => {
    if (err) {
      console.error('查询产品group_id失败:', err);
      res.json({ success: false, message: '查询产品信息失败' });
      return;
    }

    if (productResults.length === 0) {
      res.json({ success: false, message: '未找到该产品' });
      return;
    }

    const groupId = productResults[0].group_id;
    if (!groupId) {
      res.json({ success: false, message: '该产品没有配置工艺组' });
      return;
    }

    console.log(`产品ID: ${productId}, group_id: ${groupId}`);

    // 第二步：根据process_group_id从process_spec表获取所有process_id
    db.query('SELECT process_id FROM process_spec WHERE process_group_id = ?', [groupId], (err, specResults) => {
      if (err) {
        console.error('查询process_spec失败:', err);
        res.json({ success: false, message: '查询工艺规格失败' });
        return;
      }

      if (specResults.length === 0) {
        res.json({ success: false, message: '该产品组没有配置工艺' });
        return;
      }

      const processIds = specResults.map(row => row.process_id);
      console.log(`找到的process_ids: ${processIds.join(', ')}`);

      // 第三步：根据process_id列表从process表获取工艺详情
      const placeholders = processIds.map(() => '?').join(',');
      const query = `SELECT * FROM process WHERE id IN (${placeholders}) ORDER BY id ASC`;

      db.query(query, processIds, (err, processResults) => {
        if (err) {
          console.error('查询process失败:', err);
          res.json({ success: false, message: '查询工艺详情失败' });
          return;
        }

        console.log(`返回工艺数量: ${processResults.length}`);
        res.json({ success: true, data: processResults });
      });
    });
  });
});



// 获取边框图片列表
app.get('/api/border-images', (req, res) => {
  const categoryId = req.query.category_id;
  let sql = 'SELECT * FROM border_images';
  let params = [];
  
  if (categoryId && categoryId !== 'all') {
    sql += ' WHERE category_id = ?';
    params.push(categoryId);
  }
  
  sql += ' ORDER BY sort_order ASC';
  
  db.query(sql, params, (err, results) => {
    if (err) {
      res.json({ 
        code: 500, 
        msg: '数据库查询失败',
        data: []
      });
    } else {
      res.json({ 
        code: 200, 
        msg: '获取成功',
        data: results 
      });
    }
  });
});



// 获取卡背图片列表
app.get('/api/back-images', (req, res) => {
  const categoryId = req.query.category_id;
  let sql = 'SELECT * FROM back_images';
  let params = [];
  if (categoryId && categoryId !== 'all') {
    sql += ' WHERE category_id = ?';
    params.push(categoryId);
  }
  sql += ' ORDER BY sort_order ASC';
  db.query(sql, params, (err, results) => {
    if (err) {
      res.json({ code: 500, msg: '数据库查询失败', data: [] });
    } else {
      res.json({ code: 200, msg: '获取成功', data: results });
    }
  });
});

// 简化的产品详情API用于测试
app.get('/api/product-test/:id', (req, res) => {
  const id = req.params.id;

  // 直接返回测试数据
  const testProduct = {
    id: parseInt(id),
    category: "1",
    img: "/images/banner/banner3.jpg",
    title: "标清·3寸花式拍立得",
    size: "86*54mm",
    price_tag: "2",
    limit: "20 张起订",
    desc: "111111111",
    detail: "2222222222",
    img_category: "1",
    price_sale: "1",
    mainImage: "/images/banner/banner1.jpg",
    imageList: [
      "/images/banner/banner1.jpg",
      "/images/banner/banner2.jpg",
      "/images/banner/banner3.jpg"
    ]
  };

  res.json({ success: true, data: testProduct });
});

// 添加测试图片数据的接口（用于演示）
app.post('/api/add-test-images', (req, res) => {
  // 为category 1添加测试图片
  const testImages = [
    { category: '1', image_url: '/images/product/product1-1.jpg' },
    { category: '1', image_url: '/images/product/product1-2.jpg' },
    { category: '1', image_url: '/images/product/product1-3.jpg' },
    { category: '2', image_url: '/images/product/product2-1.jpg' },
    { category: '2', image_url: '/images/product/product2-2.jpg' },
    { category: '3', image_url: '/images/product/product3-1.jpg' },
    { category: '3', image_url: '/images/product/product3-2.jpg' },
    { category: '3', image_url: '/images/product/product3-3.jpg' },
    { category: '3', image_url: '/images/product/product3-4.jpg' }
  ];

  // 先清除现有的测试数据
  db.query('DELETE FROM image_list WHERE category IN ("1", "2", "3")', (err) => {
    if (err) {
      return res.json({ success: false, message: '清除旧数据失败' });
    }

    // 插入新的测试数据
    const insertPromises = testImages.map(img => {
      return new Promise((resolve, reject) => {
        db.query('INSERT INTO image_list (category, image_url) VALUES (?, ?)',
          [img.category, img.image_url],
          (err, result) => {
            if (err) reject(err);
            else resolve(result);
          }
        );
      });
    });

    Promise.all(insertPromises)
      .then(() => {
        res.json({ success: true, message: '测试图片数据添加成功' });
      })
      .catch((error) => {
        res.json({ success: false, message: '添加测试数据失败', error: error.message });
      });
  });
});

// 启动服务
// ==================== 地址管理相关接口 ====================

// 获取用户地址列表
app.get('/api/addresses', (req, res) => {
  const user_id = req.query.user_id;
  if (!user_id) {
    return res.json({ success: false, message: '缺少用户ID参数' });
  }

  const sql = 'SELECT * FROM user_addresses WHERE user_id = ? ORDER BY is_default DESC, created_at DESC';
  db.query(sql, [user_id], (err, results) => {
    if (err) {
      console.error('查询地址列表失败:', err);
      return res.json({ success: false, message: '查询地址列表失败' });
    }
    res.json({ success: true, data: results });
  });
});

// 添加新地址
app.post('/api/addresses', (req, res) => {
  const { user_id, name, phone, province, city, district, detail, is_default } = req.body;

  if (!user_id || !name || !phone || !province || !city || !district || !detail) {
    return res.json({ success: false, message: '请填写完整的地址信息' });
  }

  // 如果设置为默认地址，先将其他地址设为非默认
  if (is_default) {
    const updateSql = 'UPDATE user_addresses SET is_default = 0 WHERE user_id = ?';
    db.query(updateSql, [user_id], (err) => {
      if (err) {
        console.error('更新默认地址失败:', err);
      }
    });
  }

  const sql = `INSERT INTO user_addresses (user_id, name, phone, province, city, district, detail, is_default)
               VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
  const values = [user_id, name, phone, province, city, district, detail, is_default ? 1 : 0];

  db.query(sql, values, (err, result) => {
    if (err) {
      console.error('添加地址失败:', err);
      return res.json({ success: false, message: '添加地址失败' });
    }
    res.json({ success: true, message: '地址添加成功', id: result.insertId });
  });
});

// 更新地址
app.put('/api/addresses/:id', (req, res) => {
  const addressId = req.params.id;
  const { user_id, name, phone, province, city, district, detail, is_default } = req.body;

  if (!user_id || !name || !phone || !province || !city || !district || !detail) {
    return res.json({ success: false, message: '请填写完整的地址信息' });
  }

  // 如果设置为默认地址，先将其他地址设为非默认
  if (is_default) {
    const updateSql = 'UPDATE user_addresses SET is_default = 0 WHERE user_id = ? AND id != ?';
    db.query(updateSql, [user_id, addressId], (err) => {
      if (err) {
        console.error('更新默认地址失败:', err);
      }
    });
  }

  const sql = `UPDATE user_addresses SET name = ?, phone = ?, province = ?, city = ?, district = ?,
               detail = ?, is_default = ? WHERE id = ? AND user_id = ?`;
  const values = [name, phone, province, city, district, detail, is_default ? 1 : 0, addressId, user_id];

  db.query(sql, values, (err, result) => {
    if (err) {
      console.error('更新地址失败:', err);
      return res.json({ success: false, message: '更新地址失败' });
    }
    if (result.affectedRows === 0) {
      return res.json({ success: false, message: '地址不存在或无权限修改' });
    }
    res.json({ success: true, message: '地址更新成功' });
  });
});

// 删除地址
app.delete('/api/addresses/:id', (req, res) => {
  const addressId = req.params.id;
  const user_id = req.query.user_id;

  if (!user_id) {
    return res.json({ success: false, message: '缺少用户ID参数' });
  }

  const sql = 'DELETE FROM user_addresses WHERE id = ? AND user_id = ?';
  db.query(sql, [addressId, user_id], (err, result) => {
    if (err) {
      console.error('删除地址失败:', err);
      return res.json({ success: false, message: '删除地址失败' });
    }
    if (result.affectedRows === 0) {
      return res.json({ success: false, message: '地址不存在或无权限删除' });
    }
    res.json({ success: true, message: '地址删除成功' });
  });
});

// 设置默认地址
app.post('/api/addresses/:id/default', (req, res) => {
  const addressId = req.params.id;
  const { user_id } = req.body;

  if (!user_id) {
    return res.json({ success: false, message: '缺少用户ID参数' });
  }

  // 先将所有地址设为非默认
  const updateAllSql = 'UPDATE user_addresses SET is_default = 0 WHERE user_id = ?';
  db.query(updateAllSql, [user_id], (err) => {
    if (err) {
      console.error('重置默认地址失败:', err);
      return res.json({ success: false, message: '设置默认地址失败' });
    }

    // 设置指定地址为默认
    const setDefaultSql = 'UPDATE user_addresses SET is_default = 1 WHERE id = ? AND user_id = ?';
    db.query(setDefaultSql, [addressId, user_id], (err, result) => {
      if (err) {
        console.error('设置默认地址失败:', err);
        return res.json({ success: false, message: '设置默认地址失败' });
      }
      if (result.affectedRows === 0) {
        return res.json({ success: false, message: '地址不存在或无权限修改' });
      }
      res.json({ success: true, message: '默认地址设置成功' });
    });
  });
});

// 创建订单
app.post('/api/orders', (req, res) => {
  const { user_id, order_no, total_price, cart_items, status, from, recipient_name, recipient_phone, shipping_address, discount_amount, final_price, coupon_id } = req.body;

  if (!user_id || !order_no || !total_price || !cart_items || !Array.isArray(cart_items)) {
    return res.json({ success: false, message: '缺少必要参数' });
  }

  // 开始事务
  db.beginTransaction((err) => {
    if (err) {
      console.error('开始事务失败:', err);
      return res.json({ success: false, message: '创建订单失败' });
    }

    // 计算订单状态：pending -> 10, paid -> 20, shipped -> 30, delivered -> 40, completed -> 50, cancelled -> 60
    let orderStatus = 10; // 默认待付款
    if (status === 'paid') orderStatus = 20;
    else if (status === 'shipped') orderStatus = 30;
    else if (status === 'delivered') orderStatus = 40;
    else if (status === 'completed') orderStatus = 50;
    else if (status === 'cancelled') orderStatus = 60;

    // 1. 创建主订单记录（使用正确的字段名）
    const orderSql = `INSERT INTO orders (user_id, order_no, total_product_amount, discount_amount, total_order_amount, order_status, recipient_name, recipient_phone, shipping_address, created_at)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`;

    // 计算实际金额
    const discountAmountValue = Number(discount_amount) || 0;
    const finalPriceValue = Number(final_price) || Number(total_price);

    const orderValues = [
      user_id,
      order_no,
      total_price, // 商品总金额
      discountAmountValue, // 优惠金额
      finalPriceValue, // 订单总金额（扣除优惠后的实付金额）
      orderStatus,
      recipient_name || '',
      recipient_phone || '',
      shipping_address || ''
    ];

    db.query(orderSql, orderValues, (err, orderResult) => {
      if (err) {
        console.error('创建主订单失败:', err);
        return db.rollback(() => {
          res.json({ success: false, message: '创建订单失败' });
        });
      }

      const orderId = orderResult.insertId;

      // 2. 创建订单商品记录
      const orderItemsData = [];
      let processedItems = 0;
      const totalItems = cart_items.length;

      // 处理购物车商品数据
      cart_items.forEach(item => {
        const productId = Number(item.product_id) || 0;
        const processName = item.process_name || '';

        // 通过process_name查找process_id来生成正确的sku_id
        if (processName) {
          const processQuery = 'SELECT id FROM process WHERE name = ?';
          db.query(processQuery, [processName], (err, processResults) => {
            let processId = 0;
            if (!err && processResults.length > 0) {
              processId = processResults[0].id;
            }

            const skuId = `${productId}-${processId}`;

            // 单价（产品售价 + 工艺价格）
            const price_sale = Number(item.price_sale) || 0; // 分
            const process_price = Number(item.process_price) || 0; // 分
            const unitPrice = price_sale + process_price; // 分

            const quantity = Number(item.quantity) || 1;
            const lineTotal = unitPrice * quantity; // 分

            orderItemsData.push([
              orderId,
              productId,
              skuId,
              item.title || '商品',
              item.img || '',
              unitPrice,
              quantity,
              lineTotal
            ]);

            processedItems++;

            // 当所有商品都处理完成后，插入订单商品数据
            if (processedItems === totalItems) {
              insertOrderItems();
            }
          });
        } else {
          // 没有工艺名称，使用默认值
          const skuId = `${productId}-0`;
          const price_sale = Number(item.price_sale) || 0;
          const process_price = Number(item.process_price) || 0;
          const unitPrice = price_sale + process_price;
          const quantity = Number(item.quantity) || 1;
          const lineTotal = unitPrice * quantity;

          orderItemsData.push([
            orderId,
            productId,
            skuId,
            item.title || '商品',
            item.img || '',
            unitPrice,
            quantity,
            lineTotal
          ]);

          processedItems++;

          if (processedItems === totalItems) {
            insertOrderItems();
          }
        }
      });

      // 插入订单商品的函数
      function insertOrderItems() {
        if (orderItemsData.length === 0) {
          return db.rollback(() => {
            res.json({ success: false, message: '订单商品数据为空' });
          });
        }

        // 计算实际的商品总金额（基于订单商品明细）
        const actualTotalAmount = orderItemsData.reduce((sum, item) => {
          return sum + item[7]; // line_total_amount 是第8个字段（索引7）
        }, 0);

        // 更新订单表中的商品总金额，并重新计算实付金额
        const finalOrderAmount = Math.max(0, actualTotalAmount - discountAmountValue);
        const updateOrderSql = `UPDATE orders SET total_product_amount = ?, total_order_amount = ? WHERE id = ?`;
        db.query(updateOrderSql, [actualTotalAmount, finalOrderAmount, orderId], (err) => {
          if (err) {
            console.error('更新订单金额失败:', err);
            return db.rollback(() => {
              res.json({ success: false, message: '更新订单金额失败' });
            });
          }

          // 批量插入订单商品
          const orderItemsSql = `INSERT INTO order_items
            (order_id, product_id, sku_id, product_name_snapshot, product_image_snapshot, price_at_purchase, quantity, line_total_amount)
            VALUES ?`;

          db.query(orderItemsSql, [orderItemsData], (err) => {
            if (err) {
              console.error('创建订单商品失败:', err);
              return db.rollback(() => {
                res.json({ success: false, message: '创建订单商品失败' });
              });
            }

          // 3. 如果使用了优惠券，更新优惠券状态
          const updateCouponAndFinish = () => {
            if (coupon_id && discountAmountValue > 0) {
              const updateCouponSql = `UPDATE user_coupon SET status = 2, used_at = NOW(), order_id = ? WHERE id = ? AND status = 1`;
              db.query(updateCouponSql, [orderId, coupon_id], (err) => {
                if (err) {
                  console.error('更新优惠券状态失败:', err);
                  return db.rollback(() => {
                    res.json({ success: false, message: '更新优惠券状态失败' });
                  });
                }
                finishOrder();
              });
            } else {
              finishOrder();
            }
          };

          // 4. 如果是购物车订单，删除购物车中的商品
          const finishOrder = () => {
            if (from === 'cart') {
              const cartIds = cart_items.map(item => item.cart_id || item.id).filter(id => id);

              if (cartIds.length > 0) {
                const placeholders = cartIds.map(() => '?').join(',');

                // 先删除cart_item
                const deleteCartItemSql = `DELETE FROM cart_item WHERE cart_id IN (${placeholders})`;
                db.query(deleteCartItemSql, cartIds, (err) => {
                  if (err) {
                    console.error('删除购物车明细失败:', err);
                  }

                  // 再删除cart主表
                  const deleteCartSql = `DELETE FROM cart WHERE id IN (${placeholders})`;
                  db.query(deleteCartSql, cartIds, (err) => {
                    if (err) {
                      console.error('删除购物车主表失败:', err);
                    }

                    // 提交事务
                    db.commit((err) => {
                      if (err) {
                        console.error('提交事务失败:', err);
                        return db.rollback(() => {
                          res.json({ success: false, message: '订单创建失败' });
                        });
                      }
                      res.json({ success: true, message: '订单创建成功', orderId: orderId });
                    });
                  });
                });
              } else {
                // 没有购物车ID，直接提交事务
                db.commit((err) => {
                  if (err) {
                    console.error('提交事务失败:', err);
                    return db.rollback(() => {
                      res.json({ success: false, message: '订单创建失败' });
                    });
                  }
                  res.json({ success: true, message: '订单创建成功', orderId: orderId });
                });
              }
            } else {
              // 非购物车订单，直接提交事务
              db.commit((err) => {
                if (err) {
                  console.error('提交事务失败:', err);
                  return db.rollback(() => {
                    res.json({ success: false, message: '订单创建失败' });
                  });
                }
                res.json({ success: true, message: '订单创建成功', orderId: orderId });
              });
            }
          };

          // 开始处理优惠券和完成订单
          updateCouponAndFinish();
          });
        });
      }
    });
  });
});

// 获取订单统计信息
app.get('/api/orders/stats/:userId', (req, res) => {
  const userId = req.params.userId;

  if (!userId) {
    return res.json({ success: false, message: '缺少用户ID参数' });
  }

  const sql = `
    SELECT
      order_status,
      COUNT(*) as count
    FROM orders
    WHERE user_id = ?
    GROUP BY order_status
  `;

  db.query(sql, [userId], (err, results) => {
    if (err) {
      console.error('查询订单统计失败:', err);
      return res.json({ success: false, message: '查询订单统计失败' });
    }

    // 初始化统计数据
    const stats = {
      pending: 0,    // 待付款 (10)
      paid: 0,       // 待发货 (20, 21)
      shipped: 0,    // 待收货 (30)
      completed: 0   // 已签收 (40, 50)
    };

    // 处理查询结果
    results.forEach(item => {
      const status = item.order_status;
      const count = item.count;

      if (status === 10) {
        stats.pending += count;
      } else if (status === 20 || status === 21) {
        stats.paid += count;
      } else if (status === 30) {
        stats.shipped += count;
      } else if (status === 40 || status === 50) {
        stats.completed += count;
      }
    });

    res.json({ success: true, data: stats });
  });
});

// 获取用户订单列表 (必须在 /api/orders/:id 之前)
app.get('/api/orders/list/:userId', (req, res) => {
  const userId = req.params.userId;
  const status = req.query.status; // 可选的状态过滤参数

  if (!userId) {
    return res.json({ success: false, message: '缺少用户ID参数' });
  }

  let sql = `
    SELECT
      o.*,
      CASE
        WHEN o.order_status = 10 THEN 'pending'
        WHEN o.order_status = 20 OR o.order_status = 21 THEN 'paid'
        WHEN o.order_status = 30 THEN 'shipped'
        WHEN o.order_status = 40 OR o.order_status = 50 THEN 'completed'
        WHEN o.order_status = 60 THEN 'cancelled'
        ELSE 'unknown'
      END as status_text,
      CASE
        WHEN o.order_status = 10 THEN '待付款'
        WHEN o.order_status = 20 OR o.order_status = 21 THEN '待发货'
        WHEN o.order_status = 30 THEN '待收货'
        WHEN o.order_status = 40 OR o.order_status = 50 THEN '已签收'
        WHEN o.order_status = 60 THEN '已取消'
        ELSE '未知状态'
      END as status_name
    FROM orders o
    WHERE o.user_id = ?
  `;

  const params = [userId];

  // 根据状态过滤
  if (status && status !== 'all') {
    if (status === 'pending') {
      sql += ' AND o.order_status = 10';
    } else if (status === 'paid') {
      sql += ' AND (o.order_status = 20 OR o.order_status = 21)';
    } else if (status === 'shipped') {
      sql += ' AND o.order_status = 30';
    } else if (status === 'completed') {
      sql += ' AND (o.order_status = 40 OR o.order_status = 50)';
    } else if (status === 'cancelled') {
      sql += ' AND o.order_status = 60';
    }
  }

  sql += ' ORDER BY o.created_at DESC';

  db.query(sql, params, (err, orders) => {
    if (err) {
      console.error('查询订单列表失败:', err);
      return res.json({ success: false, message: '查询订单列表失败' });
    }

    // 为每个订单获取商品详情
    if (orders.length === 0) {
      return res.json({ success: true, data: [] });
    }

    let completedOrders = 0;
    const ordersWithItems = [];

    orders.forEach((order, index) => {
      const itemsSql = 'SELECT * FROM order_items WHERE order_id = ?';
      db.query(itemsSql, [order.id], (err, items) => {
        if (err) {
          console.error('查询订单商品失败:', err);
          items = [];
        }

        // 转换价格格式（从分转为元）
        const processedOrder = {
          ...order,
          total_product_amount: (order.total_product_amount / 100).toFixed(2),
          shipping_fee: (order.shipping_fee / 100).toFixed(2),
          discount_amount: (order.discount_amount / 100).toFixed(2),
          total_order_amount: (order.total_order_amount / 100).toFixed(2),
          items: items.map(item => ({
            ...item,
            price_at_purchase: (item.price_at_purchase / 100).toFixed(2),
            line_total_amount: (item.line_total_amount / 100).toFixed(2)
          }))
        };

        ordersWithItems[index] = processedOrder;
        completedOrders++;

        // 当所有订单的商品都查询完成后，返回结果
        if (completedOrders === orders.length) {
          res.json({ success: true, data: ordersWithItems });
        }
      });
    });
  });
});

// 取消订单
app.put('/api/orders/:id/cancel', (req, res) => {
  const orderId = req.params.id;
  const { user_id } = req.body;

  if (!user_id) {
    return res.json({ success: false, message: '缺少用户ID参数' });
  }

  // 开始事务
  db.beginTransaction((err) => {
    if (err) {
      console.error('开始事务失败:', err);
      return res.json({ success: false, message: '取消订单失败' });
    }

    // 首先检查订单是否存在且属于该用户
    const checkOrderSql = `SELECT id, order_status, discount_amount FROM orders WHERE id = ? AND user_id = ?`;

    db.query(checkOrderSql, [orderId, user_id], (err, results) => {
      if (err) {
        console.error('检查订单失败:', err);
        return db.rollback(() => {
          res.json({ success: false, message: '检查订单失败' });
        });
      }

      if (results.length === 0) {
        return db.rollback(() => {
          res.json({ success: false, message: '订单不存在或无权限操作' });
        });
      }

      const order = results[0];

      // 检查订单状态，只有待付款状态的订单才能取消
      if (order.order_status !== 10) {
        return db.rollback(() => {
          res.json({ success: false, message: '只有待付款状态的订单才能取消' });
        });
      }

      // 更新订单状态为已取消 (60)
      const cancelOrderSql = `UPDATE orders SET order_status = 60, cancelled_at = NOW(), updated_at = NOW() WHERE id = ? AND user_id = ?`;

      db.query(cancelOrderSql, [orderId, user_id], (err, result) => {
        if (err) {
          console.error('取消订单失败:', err);
          return db.rollback(() => {
            res.json({ success: false, message: '取消订单失败' });
          });
        }

        if (result.affectedRows === 0) {
          return db.rollback(() => {
            res.json({ success: false, message: '订单取消失败' });
          });
        }

        // 如果订单使用了优惠券，恢复优惠券状态
        if (order.discount_amount && order.discount_amount > 0) {
          const restoreCouponSql = `UPDATE user_coupon SET status = 1, used_at = NULL, order_id = NULL WHERE order_id = ? AND status = 2`;

          db.query(restoreCouponSql, [orderId], (err, couponResult) => {
            if (err) {
              console.error('恢复优惠券状态失败:', err);
              return db.rollback(() => {
                res.json({ success: false, message: '恢复优惠券状态失败' });
              });
            }

            // 提交事务
            db.commit((err) => {
              if (err) {
                console.error('提交事务失败:', err);
                return db.rollback(() => {
                  res.json({ success: false, message: '取消订单失败' });
                });
              }

              const message = couponResult.affectedRows > 0
                ? '订单已成功取消，优惠券已恢复可用状态'
                : '订单已成功取消';

              res.json({
                success: true,
                message: message,
                data: {
                  orderId: orderId,
                  restoredCoupons: couponResult.affectedRows
                }
              });
            });
          });
        } else {
          // 没有使用优惠券，直接提交事务
          db.commit((err) => {
            if (err) {
              console.error('提交事务失败:', err);
              return db.rollback(() => {
                res.json({ success: false, message: '取消订单失败' });
              });
            }

            res.json({
              success: true,
              message: '订单已成功取消',
              data: { orderId: orderId }
            });
          });
        }
      });
    });
  });
});

// 获取订单详情
app.get('/api/orders/:id', (req, res) => {
  const orderId = req.params.id;
  const user_id = req.query.user_id;

  if (!user_id) {
    return res.json({ success: false, message: '缺少用户ID参数' });
  }

  // 查询订单基本信息
  const sql = `SELECT o.*,
               CASE
                 WHEN o.order_status = 10 THEN 'pending'
                 WHEN o.order_status = 20 THEN 'paid'
                 WHEN o.order_status = 30 THEN 'shipped'
                 WHEN o.order_status = 40 THEN 'delivered'
                 WHEN o.order_status = 50 THEN 'completed'
                 WHEN o.order_status = 60 THEN 'cancelled'
                 ELSE 'pending'
               END as status
               FROM orders o
               WHERE o.id = ? AND o.user_id = ?`;

  db.query(sql, [orderId, user_id], (err, results) => {
    if (err) {
      console.error('查询订单失败:', err);
      return res.json({ success: false, message: '查询订单失败' });
    }

    if (results.length === 0) {
      return res.json({ success: false, message: '订单不存在' });
    }

    const order = results[0];

    // 查询订单商品详情
    const itemsSql = `SELECT * FROM order_items WHERE order_id = ?`;
    db.query(itemsSql, [orderId], (err, itemsResults) => {
      if (err) {
        console.error('查询订单商品失败:', err);
        return res.json({ success: false, message: '查询订单商品失败' });
      }

      // 转换价格格式（从分转为元）
      const processedOrder = {
        ...order,
        total_product_amount: (order.total_product_amount / 100).toFixed(2),
        shipping_fee: (order.shipping_fee / 100).toFixed(2),
        discount_amount: (order.discount_amount / 100).toFixed(2),
        total_order_amount: (order.total_order_amount / 100).toFixed(2),
        items: itemsResults.map(item => ({
          ...item,
          price_at_purchase: (item.price_at_purchase / 100).toFixed(2),
          line_total_amount: (item.line_total_amount / 100).toFixed(2)
        }))
      };

      res.json({ success: true, data: processedOrder });
    });
  });
});

// ==================== 素材管理相关接口 ====================

// 获取边框分类列表
app.get('/api/border-categories', (req, res) => {
  console.log('收到边框分类查询请求');
  const sql = `
    SELECT category_id, name, sort_order, type
    FROM material_categories
    WHERE type = 'border'
    ORDER BY sort_order
  `;

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询边框分类失败:', err);
      res.json({ code: 500, msg: '数据库查询失败', data: [] });
      return;
    }
    console.log('查询边框分类成功，结果数量:', results.length);
    res.json({ code: 200, msg: '查询成功', data: results });
  });
});

// 获取卡背分类列表
app.get('/api/card-back-categories', (req, res) => {
  const sql = `
    SELECT category_id, name, sort_order, type
    FROM material_categories
    WHERE type = 'card_back'
    ORDER BY sort_order
  `;

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询卡背分类失败:', err);
      res.json({ code: 500, msg: '数据库查询失败', data: [] });
      return;
    }
    res.json({ code: 200, msg: '查询成功', data: results });
  });
});

// 获取所有边框素材（不分类）- 必须在参数路由之前
app.get('/api/borders', (req, res) => {
  console.log('收到边框API请求');

  const sql = `
    SELECT m.id, m.name, m.image_url, m.thumbnail_url, m.sort_order, m.status,
           m.inner_frame_size, mc.name as category_name, mc.category_id
    FROM materials m, material_group mg, material_categories mc
    WHERE mc.category_id = mg.category_id
    AND mg.material_id = m.id
    AND m.status = 1
    AND mc.type = 'border'
    ORDER BY mc.sort_order, mg.sort_order
  `;

  console.log('执行SQL查询:', sql);

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询所有边框素材失败:', err);
      res.json({ code: 500, msg: '数据库查询失败', data: [] });
      return;
    }
    console.log('查询成功，结果数量:', results.length);
    res.json({ code: 200, msg: '查询成功', data: results });
  });
});

// 根据分类ID获取边框素材
app.get('/api/borders/:categoryId', (req, res) => {
  const categoryId = req.params.categoryId;

  const sql = `
    SELECT m.id, m.name, m.image_url, m.thumbnail_url, m.sort_order, m.status,
           m.inner_frame_size
    FROM materials m, material_group mg, material_categories mc
    WHERE mc.category_id = ?
    AND mc.category_id = mg.category_id
    AND mg.material_id = m.id
    AND m.status = 1
    AND mc.type = 'border'
    ORDER BY mg.sort_order
  `;

  db.query(sql, [categoryId], (err, results) => {
    if (err) {
      console.error('查询边框素材失败:', err);
      res.json({ code: 500, msg: '数据库查询失败', data: [] });
      return;
    }
    res.json({ code: 200, msg: '查询成功', data: results });
  });
});

// 根据分类ID获取卡背素材
app.get('/api/card-backs/:categoryId', (req, res) => {
  const categoryId = req.params.categoryId;

  const sql = `
    SELECT m.id, m.name, m.image_url, m.thumbnail_url, m.sort_order, m.status
    FROM materials m, material_group mg, material_categories mc
    WHERE mc.category_id = ?
    AND mc.category_id = mg.category_id
    AND mg.material_id = m.id
    AND m.status = 1
    AND mc.type = 'card_back'
    ORDER BY mg.sort_order
  `;

  db.query(sql, [categoryId], (err, results) => {
    if (err) {
      console.error('查询卡背素材失败:', err);
      res.json({ code: 500, msg: '数据库查询失败', data: [] });
      return;
    }
    res.json({ code: 200, msg: '查询成功', data: results });
  });
});

// 获取所有卡背素材（不分类）
app.get('/api/card-backs', (req, res) => {
  const sql = `
    SELECT m.id, m.name, m.image_url, m.thumbnail_url, m.sort_order, m.status,
           mc.name as category_name, mc.category_id
    FROM materials m, material_group mg, material_categories mc
    WHERE mc.category_id = mg.category_id
    AND mg.material_id = m.id
    AND m.status = 1
    AND mc.type = 'card_back'
    ORDER BY mc.sort_order, mg.sort_order
  `;

  db.query(sql, (err, results) => {
    if (err) {
      console.error('查询所有卡背素材失败:', err);
      res.json({ code: 500, msg: '数据库查询失败', data: [] });
      return;
    }
    res.json({ code: 200, msg: '查询成功', data: results });
  });
});

// ==================== 优惠券相关接口 ====================

// 获取可领取的优惠券列表
app.get('/api/coupons/available', (req, res) => {
  const { user_id, phone_number } = req.query;

  if (!user_id && !phone_number) {
    return res.json({ success: false, message: '缺少用户信息' });
  }

  // 查询用户没有领取过的优惠券
  let sql = `
    SELECT
      cc.id as campaign_id,
      cc.campaign_name,
      cc.template_id,
      cc.claim_start_time,
      cc.claim_end_time,
      cc.max_quantity,
      cc.claimed_quantity,
      ct.name as template_name,
      ct.type,
      ct.value,
      ct.threshold_amount,
      ct.validity_type,
      ct.valid_from,
      ct.valid_to,
      ct.valid_days_after_claim,
      ct.scope_type
    FROM coupon_campaign cc
    LEFT JOIN coupon_template ct ON cc.template_id = ct.id
    WHERE cc.status = 1
      AND cc.channel_type = 1
      AND NOW() BETWEEN cc.claim_start_time AND cc.claim_end_time
      AND (cc.max_quantity = -1 OR cc.claimed_quantity < cc.max_quantity)
  `;

  // 排除用户已领取的优惠券
  if (user_id && phone_number) {
    sql += ` AND NOT EXISTS(
      SELECT 1 FROM user_coupon uc
      WHERE uc.campaign_id = cc.id AND (uc.user_id = ? OR uc.phone_number = ?)
    )`;
  } else if (user_id) {
    sql += ` AND NOT EXISTS(
      SELECT 1 FROM user_coupon uc
      WHERE uc.campaign_id = cc.id AND uc.user_id = ?
    )`;
  } else if (phone_number) {
    sql += ` AND NOT EXISTS(
      SELECT 1 FROM user_coupon uc
      WHERE uc.campaign_id = cc.id AND uc.phone_number = ?
    )`;
  }

  sql += ` ORDER BY cc.id DESC`;

  const params = [];
  if (user_id && phone_number) {
    params.push(user_id, phone_number);
  } else if (user_id) {
    params.push(user_id);
  } else if (phone_number) {
    params.push(phone_number);
  }

  db.query(sql, params, (err, results) => {
    if (err) {
      console.error('查询可领取优惠券失败:', err);
      res.json({ success: false, message: '查询失败' });
      return;
    }

    // 处理数据格式
    const processedResults = results.map(item => ({
      ...item,
      value: parseFloat(item.value),
      threshold_amount: parseFloat(item.threshold_amount),
      // 计算剩余数量
      remaining_quantity: item.max_quantity === -1 ? -1 : item.max_quantity - item.claimed_quantity
    }));

    res.json({ success: true, data: processedResults });
  });
});

// 获取用户的优惠券列表
app.get('/api/coupons/my', (req, res) => {
  const { user_id, phone_number, status } = req.query;

  console.log('查询我的优惠券，接收参数:', { user_id, phone_number, status });

  if (!user_id && !phone_number) {
    return res.json({ success: false, message: '缺少用户信息' });
  }

  let sql = `
    SELECT
      uc.*,
      cc.campaign_name,
      ct.name as template_name,
      ct.type,
      ct.value,
      ct.threshold_amount,
      ct.scope_type
    FROM user_coupon uc
    LEFT JOIN coupon_campaign cc ON uc.campaign_id = cc.id
    LEFT JOIN coupon_template ct ON uc.template_id = ct.id
    WHERE 1=1
  `;

  const params = [];

  if (user_id && phone_number) {
    // 如果同时有user_id和phone_number，查询两种情况
    sql += ` AND (uc.user_id = ? OR uc.phone_number = ?)`;
    params.push(user_id, phone_number);
  } else if (user_id) {
    sql += ` AND uc.user_id = ?`;
    params.push(user_id);
  } else if (phone_number) {
    sql += ` AND uc.phone_number = ?`;
    params.push(phone_number);
  }

  if (status) {
    sql += ` AND uc.status = ?`;
    params.push(status);
  }

  sql += ` ORDER BY uc.claimed_at DESC`;

  console.log('执行SQL:', sql);
  console.log('SQL参数:', params);

  db.query(sql, params, (err, results) => {
    if (err) {
      console.error('查询用户优惠券失败:', err);
      res.json({ success: false, message: '查询失败' });
      return;
    }

    console.log('查询结果数量:', results.length);
    console.log('查询结果:', results);

    // 处理数据格式
    const processedResults = results.map(item => ({
      ...item,
      value: parseFloat(item.value),
      threshold_amount: parseFloat(item.threshold_amount),
      // 判断是否过期
      is_expired: new Date() > new Date(item.valid_to)
    }));

    res.json({ success: true, data: processedResults });
  });
});

// 领取优惠券
app.post('/api/coupons/claim', (req, res) => {
  const { user_id, phone_number, campaign_id } = req.body;

  console.log('领取优惠券，接收参数:', { user_id, phone_number, campaign_id });

  if (!campaign_id) {
    return res.json({ success: false, message: '缺少活动ID' });
  }

  if (!user_id && !phone_number) {
    return res.json({ success: false, message: '缺少用户信息' });
  }

  // 开始事务
  db.beginTransaction((err) => {
    if (err) {
      return res.json({ success: false, message: '事务开始失败' });
    }

    // 1. 检查活动是否有效
    const checkCampaignSql = `
      SELECT cc.*, ct.*
      FROM coupon_campaign cc
      LEFT JOIN coupon_template ct ON cc.template_id = ct.id
      WHERE cc.id = ? AND cc.status = 1
        AND NOW() BETWEEN cc.claim_start_time AND cc.claim_end_time
        AND (cc.max_quantity = -1 OR cc.claimed_quantity < cc.max_quantity)
    `;

    db.query(checkCampaignSql, [campaign_id], (err, campaigns) => {
      if (err || campaigns.length === 0) {
        return db.rollback(() => {
          res.json({ success: false, message: '活动不存在或已结束' });
        });
      }

      const campaign = campaigns[0];

      // 2. 检查用户是否已领取
      let checkUserSql = `SELECT id FROM user_coupon WHERE campaign_id = ?`;
      const checkParams = [campaign_id];

      if (user_id) {
        checkUserSql += ` AND user_id = ?`;
        checkParams.push(user_id);
      }

      if (phone_number) {
        checkUserSql += ` AND phone_number = ?`;
        checkParams.push(phone_number);
      }

      db.query(checkUserSql, checkParams, (err, existing) => {
        if (err) {
          return db.rollback(() => {
            res.json({ success: false, message: '检查失败' });
          });
        }

        if (existing.length > 0) {
          return db.rollback(() => {
            res.json({ success: false, message: '您已领取过此优惠券' });
          });
        }

        // 3. 生成优惠券码
        const couponCode = 'COUP' + Date.now().toString(36).toUpperCase() + Math.random().toString(36).substr(2, 6).toUpperCase();

        // 4. 计算有效期
        let validFrom = new Date();
        let validTo = new Date();

        if (campaign.validity_type === 1) {
          // 固定时间
          validFrom = new Date(campaign.valid_from);
          validTo = new Date(campaign.valid_to);
        } else {
          // 领取后N天有效
          validTo.setDate(validTo.getDate() + campaign.valid_days_after_claim);
        }

        // 5. 插入用户优惠券
        const insertCouponSql = `
          INSERT INTO user_coupon (
            user_id, phone_number, template_id, campaign_id, coupon_code,
            status, valid_from, valid_to, claimed_at
          ) VALUES (?, ?, ?, ?, ?, 1, ?, ?, NOW())
        `;

        db.query(insertCouponSql, [
          user_id || -1, phone_number || null, campaign.template_id, campaign_id,
          couponCode, validFrom, validTo
        ], (err, result) => {
          if (err) {
            return db.rollback(() => {
              res.json({ success: false, message: '领取失败' });
            });
          }

          // 6. 更新活动已领取数量
          const updateCampaignSql = `
            UPDATE coupon_campaign
            SET claimed_quantity = claimed_quantity + 1
            WHERE id = ?
          `;

          db.query(updateCampaignSql, [campaign_id], (err) => {
            if (err) {
              return db.rollback(() => {
                res.json({ success: false, message: '更新失败' });
              });
            }

            // 提交事务
            db.commit((err) => {
              if (err) {
                return db.rollback(() => {
                  res.json({ success: false, message: '提交失败' });
                });
              }

              res.json({
                success: true,
                message: '领取成功',
                data: {
                  coupon_code: couponCode,
                  valid_to: validTo
                }
              });
            });
          });
        });
      });
    });
  });
});

app.listen(8000, '0.0.0.0', () => {
  console.log('后端服务已启动，端口 8000，监听所有网络接口');
});