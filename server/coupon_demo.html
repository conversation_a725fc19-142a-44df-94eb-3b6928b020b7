<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优惠券功能演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #ff6b9d;
        }
        .coupon-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 10px;
            padding: 3px;
            margin: 10px 0;
        }
        .coupon-content {
            background: white;
            border-radius: 7px;
            padding: 15px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .coupon-info {
            flex: 1;
        }
        .coupon-value {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b9d;
        }
        .coupon-desc {
            color: #666;
            font-size: 14px;
        }
        .claim-btn {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: bold;
        }
        .claim-btn:hover {
            opacity: 0.9;
        }
        .claim-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .input-group {
            margin: 10px 0;
        }
        .input-group label {
            display: inline-block;
            width: 100px;
            font-weight: bold;
        }
        .input-group input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        .btn {
            background: #ff6b9d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #ff5a8a;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            background: #f9f9f9;
            border-left: 4px solid #ff6b9d;
        }
        .error {
            border-left-color: #ff4444;
            background: #fff5f5;
        }
        .success {
            border-left-color: #44ff44;
            background: #f5fff5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎫 优惠券功能演示</h1>
        
        <!-- 用户信息输入 -->
        <div class="section">
            <h3>用户信息</h3>
            <div class="input-group">
                <label>用户ID:</label>
                <input type="number" id="userId" value="3250" placeholder="输入用户ID">
            </div>
            <div class="input-group">
                <label>手机号:</label>
                <input type="text" id="phoneNumber" value="" placeholder="输入手机号(可选)">
            </div>
        </div>

        <!-- 可领取的优惠券 -->
        <div class="section">
            <h3>可领取的优惠券</h3>
            <button class="btn" onclick="loadAvailableCoupons()">刷新可领取优惠券</button>
            <div id="availableCoupons"></div>
        </div>

        <!-- 我的优惠券 -->
        <div class="section">
            <h3>我的优惠券</h3>
            <button class="btn" onclick="loadMyCoupons()">刷新我的优惠券</button>
            <div id="myCoupons"></div>
        </div>

        <!-- 操作结果 -->
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api';

        function showResult(message, isError = false) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="result ${isError ? 'error' : 'success'}">${message}</div>`;
        }

        function getUserInfo() {
            return {
                user_id: document.getElementById('userId').value,
                phone_number: document.getElementById('phoneNumber').value
            };
        }

        async function loadAvailableCoupons() {
            const userInfo = getUserInfo();
            if (!userInfo.user_id && !userInfo.phone_number) {
                showResult('请输入用户ID或手机号', true);
                return;
            }

            try {
                const params = new URLSearchParams();
                if (userInfo.user_id) params.append('user_id', userInfo.user_id);
                if (userInfo.phone_number) params.append('phone_number', userInfo.phone_number);

                const response = await fetch(`${API_BASE}/coupons/available?${params}`);
                const data = await response.json();

                if (data.success) {
                    displayAvailableCoupons(data.data);
                    showResult(`加载成功，找到 ${data.data.length} 张可领取优惠券`);
                } else {
                    showResult(data.message || '加载失败', true);
                }
            } catch (error) {
                showResult('网络错误: ' + error.message, true);
            }
        }

        async function loadMyCoupons() {
            const userInfo = getUserInfo();
            if (!userInfo.user_id && !userInfo.phone_number) {
                showResult('请输入用户ID或手机号', true);
                return;
            }

            try {
                const params = new URLSearchParams();
                if (userInfo.user_id) params.append('user_id', userInfo.user_id);
                if (userInfo.phone_number) params.append('phone_number', userInfo.phone_number);

                const response = await fetch(`${API_BASE}/coupons/my?${params}`);
                const data = await response.json();

                if (data.success) {
                    displayMyCoupons(data.data);
                    showResult(`加载成功，找到 ${data.data.length} 张我的优惠券`);
                } else {
                    showResult(data.message || '加载失败', true);
                }
            } catch (error) {
                showResult('网络错误: ' + error.message, true);
            }
        }

        async function claimCoupon(campaignId) {
            const userInfo = getUserInfo();
            if (!userInfo.user_id && !userInfo.phone_number) {
                showResult('请输入用户ID或手机号', true);
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/coupons/claim`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: userInfo.user_id,
                        phone_number: userInfo.phone_number,
                        campaign_id: campaignId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    showResult('领取成功！');
                    loadAvailableCoupons(); // 刷新可领取列表
                    loadMyCoupons(); // 刷新我的优惠券列表
                } else {
                    showResult(data.message || '领取失败', true);
                }
            } catch (error) {
                showResult('网络错误: ' + error.message, true);
            }
        }

        function displayAvailableCoupons(coupons) {
            const container = document.getElementById('availableCoupons');
            if (coupons.length === 0) {
                container.innerHTML = '<p>暂无可领取的优惠券</p>';
                return;
            }

            container.innerHTML = coupons.map(coupon => `
                <div class="coupon-card">
                    <div class="coupon-content">
                        <div class="coupon-info">
                            <div class="coupon-value">¥${coupon.value}</div>
                            <div class="coupon-desc">
                                ${coupon.template_name || coupon.campaign_name}
                                ${coupon.threshold_amount > 0 ? `(满${coupon.threshold_amount}减${coupon.value})` : '(无门槛券)'}
                            </div>
                            <div class="coupon-desc">
                                ${coupon.validity_type === 1 ? 
                                    `有效期: ${coupon.valid_from} 至 ${coupon.valid_to}` : 
                                    `领取后${coupon.valid_days_after_claim}天内有效`
                                }
                            </div>
                            ${coupon.max_quantity !== -1 ? 
                                `<div class="coupon-desc">剩余: ${coupon.remaining_quantity}</div>` : ''
                            }
                        </div>
                        <button class="claim-btn" onclick="claimCoupon(${coupon.campaign_id})">
                            立即领取
                        </button>
                    </div>
                </div>
            `).join('');
        }

        function displayMyCoupons(coupons) {
            const container = document.getElementById('myCoupons');
            if (coupons.length === 0) {
                container.innerHTML = '<p>您还没有优惠券</p>';
                return;
            }

            container.innerHTML = coupons.map(coupon => `
                <div class="coupon-card">
                    <div class="coupon-content">
                        <div class="coupon-info">
                            <div class="coupon-value">¥${coupon.value}</div>
                            <div class="coupon-desc">
                                ${coupon.template_name || coupon.campaign_name}
                                ${coupon.threshold_amount > 0 ? `(满${coupon.threshold_amount}减${coupon.value})` : '(无门槛券)'}
                            </div>
                            <div class="coupon-desc">
                                有效期: ${coupon.valid_from} 至 ${coupon.valid_to}
                            </div>
                            <div class="coupon-desc">券码: ${coupon.coupon_code}</div>
                        </div>
                        <div style="text-align: center;">
                            ${coupon.status === 1 && !coupon.is_expired ? 
                                '<span style="color: #44ff44; font-weight: bold;">可使用</span>' :
                                coupon.is_expired ? 
                                '<span style="color: #ff4444; font-weight: bold;">已过期</span>' :
                                coupon.status === 2 ?
                                '<span style="color: #999; font-weight: bold;">已使用</span>' :
                                '<span style="color: #999; font-weight: bold;">不可用</span>'
                            }
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 页面加载时自动加载数据
        window.onload = function() {
            loadAvailableCoupons();
            loadMyCoupons();
        };
    </script>
</body>
</html>
