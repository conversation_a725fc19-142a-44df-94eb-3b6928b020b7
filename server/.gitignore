# 依赖包
node_modules/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/

# nyc测试覆盖率
.nyc_output

# Grunt中间存储
.grunt

# Bower依赖目录
bower_components

# node-waf配置
.lock-wscript

# 编译的二进制插件
build/Release

# 依赖目录
node_modules/
jspm_packages/

# TypeScript v1声明文件
typings/

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env
.env.test

# parcel-bundler缓存
.cache
.parcel-cache

# Next.js构建输出
.next

# Nuxt.js构建/生成输出
.nuxt
dist

# Gatsby文件
.cache/
public

# Storybook构建输出
.out
.storybook-out

# 临时文件夹
tmp/
temp/

# 编辑器目录和文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db