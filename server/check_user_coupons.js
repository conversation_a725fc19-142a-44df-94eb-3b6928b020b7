const mysql = require('mysql2');

// 数据库连接配置
const db = mysql.createConnection({
  host: '**************',
  user: 'miniapp',
  password: 'Miniapp@123456',
  database: 'miniapp'
});

// 连接数据库
db.connect((err) => {
  if (err) {
    console.error('数据库连接失败:', err);
    return;
  }
  console.log('数据库连接成功');
  checkUserCoupons();
});

function checkUserCoupons() {
  console.log('\n=== 检查用户优惠券数据 ===\n');

  // 1. 查看所有用户优惠券
  const sql1 = `
    SELECT 
      uc.id,
      uc.user_id,
      uc.phone_number,
      uc.coupon_code,
      uc.status,
      uc.claimed_at,
      cc.campaign_name,
      ct.name as template_name,
      ct.value
    FROM user_coupon uc
    LEFT JOIN coupon_campaign cc ON uc.campaign_id = cc.id
    LEFT JOIN coupon_template ct ON uc.template_id = ct.id
    ORDER BY uc.claimed_at DESC
    LIMIT 10
  `;

  db.query(sql1, (err, results) => {
    if (err) {
      console.error('查询失败:', err);
      return;
    }

    console.log('所有用户优惠券 (最近10条):');
    console.table(results);

    // 2. 查看特定用户的优惠券
    const sql2 = `
      SELECT 
        uc.*,
        cc.campaign_name,
        ct.name as template_name,
        ct.value
      FROM user_coupon uc
      LEFT JOIN coupon_campaign cc ON uc.campaign_id = cc.id
      LEFT JOIN coupon_template ct ON uc.template_id = ct.id
      WHERE uc.user_id = 3259 OR uc.phone_number = '13800138001'
      ORDER BY uc.claimed_at DESC
    `;

    db.query(sql2, (err, results) => {
      if (err) {
        console.error('查询用户3250的优惠券失败:', err);
        return;
      }

      console.log('\n用户3259的优惠券:');
      console.table(results);

      // 3. 查看可领取的优惠券
      const sql3 = `
        SELECT 
          cc.id as campaign_id,
          cc.campaign_name,
          cc.template_id,
          cc.max_quantity,
          cc.claimed_quantity,
          ct.name as template_name,
          ct.value,
          ct.threshold_amount
        FROM coupon_campaign cc
        LEFT JOIN coupon_template ct ON cc.template_id = ct.id
        WHERE cc.status = 1 
          AND cc.channel_type = 1
          AND NOW() BETWEEN cc.claim_start_time AND cc.claim_end_time
          AND (cc.max_quantity = -1 OR cc.claimed_quantity < cc.max_quantity)
        ORDER BY cc.id DESC
      `;

      db.query(sql3, (err, results) => {
        if (err) {
          console.error('查询可领取优惠券失败:', err);
          return;
        }

        console.log('\n可领取的优惠券:');
        console.table(results);

        db.end();
      });
    });
  });
}
