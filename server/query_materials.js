const mysql = require('mysql2');

const db = mysql.createConnection({
  host: '**************',
  user: 'miniapp',
  password: 'Miniapp@123456',
  database: 'miniapp'
});

console.log('查询数据库中的实际素材数据...\n');

// 查询所有边框素材
const borderSql = `
  SELECT m.id, m.name, m.image_url, m.thumbnail_url, m.sort_order, m.status,
         mc.name as category_name, mc.category_id
  FROM materials m, material_group mg, material_categories mc
  WHERE mc.category_id = mg.category_id 
  AND mg.material_id = m.id 
  AND m.status = 1 
  AND mc.type = 'border'
  ORDER BY mc.sort_order, mg.sort_order
`;

db.query(borderSql, (err, results) => {
  if (err) {
    console.error('查询边框素材失败:', err);
  } else {
    console.log('边框素材数据:');
    console.table(results);
  }
  
  // 查询所有卡背素材
  const cardBackSql = `
    SELECT m.id, m.name, m.image_url, m.thumbnail_url, m.sort_order, m.status,
           mc.name as category_name, mc.category_id
    FROM materials m, material_group mg, material_categories mc
    WHERE mc.category_id = mg.category_id 
    AND mg.material_id = m.id 
    AND m.status = 1 
    AND mc.type = 'card_back'
    ORDER BY mc.sort_order, mg.sort_order
  `;
  
  db.query(cardBackSql, (err, results) => {
    if (err) {
      console.error('查询卡背素材失败:', err);
    } else {
      console.log('\n卡背素材数据:');
      console.table(results);
    }
    
    // 查询所有素材（不分类型）
    const allMaterialsSql = `SELECT * FROM materials WHERE status = 1 ORDER BY id`;
    db.query(allMaterialsSql, (err, results) => {
      if (err) {
        console.error('查询所有素材失败:', err);
      } else {
        console.log('\n所有素材数据:');
        console.table(results);
      }
      db.end();
    });
  });
});
